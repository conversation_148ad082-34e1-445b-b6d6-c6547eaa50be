'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';

// Simple Card component
const Card = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
  <div className={`bg-white rounded-lg border shadow-sm p-4 ${className}`}>{children}</div>
);
import {
  Copy,
  Share2,
  Users,
  TrendingUp,
  DollarSign,
  Eye,
  CheckCircle,
  ExternalLink
} from 'lucide-react';
import { ReferralStats } from '@/lib/promotions/types';

interface ReferralLinkCardProps {
  candidateId: string;
  candidateName: string;
}

interface ReferralData {
  referralLink: {
    id: string;
    referralCode: string;
    clickCount: number;
    conversionCount: number;
    totalEarnings: string;
    status: string;
  } | null;
  stats: ReferralStats;
  conversions: Array<{
    id: string;
    candidateName: string;
    commissionAmount: string;
    status: string;
    convertedAt: string;
    metadata: any;
  }>;
}

export function ReferralLinkCard({ candidateId, candidateName }: ReferralLinkCardProps) {
  const [referralData, setReferralData] = useState<ReferralData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    fetchReferralData();
  }, [candidateId]);

  const fetchReferralData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/promotions/referrals?candidateId=${candidateId}`);

      if (response.ok) {
        const data = await response.json();
        setReferralData(data);
      }
    } catch (error) {
      console.error('Failed to fetch referral data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const createReferralLink = async () => {
    try {
      setIsCreating(true);
      const response = await fetch('/api/promotions/referrals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ candidateId }),
      });

      if (response.ok) {
        const data = await response.json();
        setReferralData(data);
      }
    } catch (error) {
      console.error('Failed to create referral link:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const copyReferralLink = async () => {
    if (!referralData?.referralLink) return;

    const referralUrl = `${window.location.origin}/ref/${referralData.referralLink.referralCode}`;

    try {
      await navigator.clipboard.writeText(referralUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const shareReferralLink = async () => {
    if (!referralData?.referralLink) return;

    const referralUrl = `${window.location.origin}/ref/${referralData.referralLink.referralCode}`;
    const shareText = `Check out this IELTS test center! Get premium features with my referral link: ${referralUrl}`;

    if (navigator.share) {
      try {
        await navigator.share({
          title: 'IELTS Test Center Referral',
          text: shareText,
          url: referralUrl,
        });
      } catch (error) {
        console.error('Failed to share:', error);
      }
    } else {
      // Fallback to copying
      copyReferralLink();
    }
  };

  const formatAmount = (amount: number | string) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return new Intl.NumberFormat('uz-UZ', {
      style: 'currency',
      currency: 'UZS',
      minimumFractionDigits: 0,
    }).format(numAmount);
  };

  if (isLoading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
      </Card>
    );
  }

  if (!referralData?.referralLink) {
    return (
      <Card className="p-6">
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mx-auto">
            <Share2 className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold">Create Your Referral Link</h3>
            <p className="text-gray-600 text-sm mt-1">
              Earn commissions by referring other candidates to premium features
            </p>
          </div>
          <Button onClick={createReferralLink} disabled={isCreating}>
            {isCreating ? 'Creating...' : 'Create Referral Link'}
          </Button>
        </div>
      </Card>
    );
  }

  const referralUrl = `${window.location.origin}/ref/${referralData.referralLink.referralCode}`;

  return (
    <div className="space-y-6">
      {/* Referral Link Card */}
      <Card className="p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Your Referral Link</h3>
            <Badge variant={referralData.referralLink.status === 'active' ? 'default' : 'secondary'}>
              {referralData.referralLink.status}
            </Badge>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Referral Code</label>
            <div className="flex space-x-2">
              <Input
                value={referralData.referralLink.referralCode}
                readOnly
                className="font-mono"
              />
              <Button variant="outline" size="sm" onClick={copyReferralLink}>
                {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Referral URL</label>
            <div className="flex space-x-2">
              <Input
                value={referralUrl}
                readOnly
                className="font-mono text-xs"
              />
              <Button variant="outline" size="sm" onClick={shareReferralLink}>
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="text-xs text-gray-500">
            Share this link with other candidates. You'll earn a commission when they purchase premium features.
          </div>
        </div>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Eye className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Clicks</p>
              <p className="text-2xl font-bold">{referralData.stats.totalClicks}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Users className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Conversions</p>
              <p className="text-2xl font-bold">{referralData.stats.totalConversions}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <TrendingUp className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Conversion Rate</p>
              <p className="text-2xl font-bold">{referralData.stats.conversionRate.toFixed(1)}%</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <DollarSign className="h-5 w-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Earnings</p>
              <p className="text-lg font-bold">{formatAmount(referralData.stats.totalEarnings)}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Earnings Breakdown */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Earnings Breakdown</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-yellow-50 rounded-lg">
            <p className="text-sm text-gray-600">Pending</p>
            <p className="text-xl font-bold text-yellow-600">
              {formatAmount(referralData.stats.pendingEarnings)}
            </p>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <p className="text-sm text-gray-600">Paid</p>
            <p className="text-xl font-bold text-green-600">
              {formatAmount(referralData.stats.paidEarnings)}
            </p>
          </div>
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <p className="text-sm text-gray-600">Total</p>
            <p className="text-xl font-bold text-blue-600">
              {formatAmount(referralData.stats.totalEarnings)}
            </p>
          </div>
        </div>
      </Card>

      {/* Recent Conversions */}
      {referralData.conversions.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Conversions</h3>
          <div className="space-y-3">
            {referralData.conversions.map((conversion) => (
              <div key={conversion.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium">{conversion.candidateName}</p>
                  <p className="text-sm text-gray-600">
                    {new Date(conversion.convertedAt).toLocaleDateString()}
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-medium">{formatAmount(conversion.commissionAmount)}</p>
                  <Badge variant={conversion.status === 'paid' ? 'default' : 'secondary'} className="text-xs">
                    {conversion.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
}
