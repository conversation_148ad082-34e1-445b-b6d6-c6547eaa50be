import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { referralLinks, referralConversions, candidates } from '@/lib/db/schema';
import { eq, and, desc, sql } from 'drizzle-orm';
import { z } from 'zod';
import { createReferralLink, getReferralStats } from '@/lib/promotions/utils';
import { CreateReferralLinkRequest } from '@/lib/promotions/types';

const createReferralSchema = z.object({
  candidateId: z.string().min(1),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createReferralSchema.parse(body);

    // Verify candidate belongs to organization
    const candidate = await db
      .select()
      .from(candidates)
      .where(
        and(
          eq(candidates.id, validatedData.candidateId),
          eq(candidates.organizationId, session.user.organizationId)
        )
      )
      .limit(1);

    if (candidate.length === 0) {
      return NextResponse.json(
        { error: 'Candidate not found or does not belong to your organization' },
        { status: 404 }
      );
    }

    const createRequest: CreateReferralLinkRequest = {
      candidateId: validatedData.candidateId,
      organizationId: session.user.organizationId,
    };

    const result = await createReferralLink(createRequest);

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    // Get referral stats
    const stats = await getReferralStats(validatedData.candidateId);

    return NextResponse.json({
      referralLink: result.referralLink,
      stats,
    }, { status: 201 });
  } catch (error) {
    console.error('Create referral link error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create referral link' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const candidateId = searchParams.get('candidateId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    if (candidateId) {
      // Get specific candidate's referral data
      const referralLink = await db
        .select()
        .from(referralLinks)
        .where(
          and(
            eq(referralLinks.candidateId, candidateId),
            eq(referralLinks.organizationId, session.user.organizationId)
          )
        )
        .limit(1);

      if (referralLink.length === 0) {
        return NextResponse.json({
          referralLink: null,
          stats: {
            totalClicks: 0,
            totalConversions: 0,
            conversionRate: 0,
            totalEarnings: 0,
            pendingEarnings: 0,
            paidEarnings: 0,
          },
          conversions: [],
        });
      }

      const stats = await getReferralStats(candidateId);

      // Get recent conversions
      const conversions = await db
        .select({
          id: referralConversions.id,
          referredCandidateId: referralConversions.referredCandidateId,
          commissionAmount: referralConversions.commissionAmount,
          commissionRate: referralConversions.commissionRate,
          status: referralConversions.status,
          convertedAt: referralConversions.convertedAt,
          metadata: referralConversions.metadata,
          candidateName: candidates.fullName,
        })
        .from(referralConversions)
        .innerJoin(candidates, eq(referralConversions.referredCandidateId, candidates.id))
        .where(eq(referralConversions.referralLinkId, referralLink[0].id))
        .orderBy(desc(referralConversions.convertedAt))
        .limit(10);

      return NextResponse.json({
        referralLink: referralLink[0],
        stats,
        conversions,
      });
    }

    // Get all referral links for organization
    const referralLinks = await db
      .select({
        id: referralLinks.id,
        candidateId: referralLinks.candidateId,
        referralCode: referralLinks.referralCode,
        clickCount: referralLinks.clickCount,
        conversionCount: referralLinks.conversionCount,
        totalEarnings: referralLinks.totalEarnings,
        status: referralLinks.status,
        createdAt: referralLinks.createdAt,
        candidateName: candidates.fullName,
        candidateEmail: candidates.email,
      })
      .from(referralLinks)
      .innerJoin(candidates, eq(referralLinks.candidateId, candidates.id))
      .where(eq(referralLinks.organizationId, session.user.organizationId))
      .orderBy(desc(referralLinks.createdAt))
      .limit(limit)
      .offset((page - 1) * limit);

    // Get total count
    const [{ count: total }] = await db
      .select({ count: sql<number>`count(*)` })
      .from(referralLinks)
      .where(eq(referralLinks.organizationId, session.user.organizationId));

    return NextResponse.json({
      referralLinks,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Get referral links error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch referral links' },
      { status: 500 }
    );
  }
}
