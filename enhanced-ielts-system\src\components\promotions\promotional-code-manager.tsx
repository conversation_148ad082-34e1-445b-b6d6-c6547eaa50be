'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';

// Simple UI component replacements
const Card = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
  <div className={`bg-white rounded-lg border shadow-sm ${className}`}>{children}</div>
);

const Label = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
  <label className={`text-sm font-medium text-gray-700 ${className}`}>{children}</label>
);

const Select = ({ value, onValueChange, children }: { value: string; onValueChange: (value: string) => void; children: React.ReactNode }) => (
  <select
    value={value}
    onChange={(e) => onValueChange(e.target.value)}
    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
  >
    {children}
  </select>
);

const SelectTrigger = ({ children }: { children: React.ReactNode }) => <>{children}</>;
const SelectValue = () => null;
const SelectContent = ({ children }: { children: React.ReactNode }) => <>{children}</>;
const SelectItem = ({ value, children }: { value: string; children: React.ReactNode }) => (
  <option value={value}>{children}</option>
);

const Textarea = ({ placeholder, value, onChange, className = '' }: {
  placeholder?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  className?: string;
}) => (
  <textarea
    placeholder={placeholder}
    value={value}
    onChange={onChange}
    className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${className}`}
    rows={3}
  />
);

const Switch = ({ checked, onCheckedChange }: { checked: boolean; onCheckedChange: (checked: boolean) => void }) => (
  <input
    type="checkbox"
    checked={checked}
    onChange={(e) => onCheckedChange(e.target.checked)}
    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
  />
);
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Copy,
  Calendar,
  Percent,
  DollarSign,
  Gift,
  Users,
  TrendingUp
} from 'lucide-react';
import { PromotionalCode } from '@/lib/promotions/types';

interface PromotionalCodeManagerProps {
  organizationId: string;
}

interface CreateCodeForm {
  code: string;
  name: string;
  description: string;
  type: 'percentage' | 'fixed_amount' | 'free_access';
  value: number;
  featureType: 'feedback' | 'certificate' | 'progress' | 'all';
  validFrom: string;
  validUntil: string;
  usageLimit?: number;
  minPurchaseAmount?: number;
  maxDiscountAmount?: number;
  isPublic: boolean;
  autoGenerate: boolean;
}

export function PromotionalCodeManager({ organizationId }: PromotionalCodeManagerProps) {
  const [codes, setCodes] = useState<PromotionalCode[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [featureFilter, setFeatureFilter] = useState('all');

  const [createForm, setCreateForm] = useState<CreateCodeForm>({
    code: '',
    name: '',
    description: '',
    type: 'percentage',
    value: 10,
    featureType: 'all',
    validFrom: new Date().toISOString().split('T')[0],
    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    isPublic: false,
    autoGenerate: true,
  });

  useEffect(() => {
    fetchCodes();
  }, [searchTerm, statusFilter, featureFilter]);

  const fetchCodes = async () => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (featureFilter !== 'all') params.append('featureType', featureFilter);

      const response = await fetch(`/api/promotions/codes?${params.toString()}`);

      if (response.ok) {
        const data = await response.json();
        setCodes(data.codes || []);
      }
    } catch (error) {
      console.error('Failed to fetch promotional codes:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const createCode = async () => {
    try {
      const response = await fetch('/api/promotions/codes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...createForm,
          validFrom: new Date(createForm.validFrom).toISOString(),
          validUntil: new Date(createForm.validUntil).toISOString(),
        }),
      });

      if (response.ok) {
        setShowCreateForm(false);
        setCreateForm({
          code: '',
          name: '',
          description: '',
          type: 'percentage',
          value: 10,
          featureType: 'all',
          validFrom: new Date().toISOString().split('T')[0],
          validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          isPublic: false,
          autoGenerate: true,
        });
        fetchCodes();
      }
    } catch (error) {
      console.error('Failed to create promotional code:', error);
    }
  };

  const copyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  const getDiscountIcon = (type: string) => {
    switch (type) {
      case 'percentage':
        return <Percent className="h-4 w-4" />;
      case 'fixed_amount':
        return <DollarSign className="h-4 w-4" />;
      case 'free_access':
        return <Gift className="h-4 w-4" />;
      default:
        return <Percent className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      case 'used_up':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatAmount = (amount: string | number) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return new Intl.NumberFormat('uz-UZ', {
      style: 'currency',
      currency: 'UZS',
      minimumFractionDigits: 0,
    }).format(numAmount);
  };

  const getDiscountText = (code: PromotionalCode) => {
    switch (code.type) {
      case 'percentage':
        return `${code.value}% off`;
      case 'fixed_amount':
        return `${formatAmount(code.value)} off`;
      case 'free_access':
        return 'Free access';
      default:
        return 'Discount';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Promotional Codes</h2>
          <p className="text-gray-600">Manage discount codes and promotional offers</p>
        </div>
        <Button onClick={() => setShowCreateForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Code
        </Button>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <Label>Search</Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search codes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div>
            <Label>Status</Label>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
                <SelectItem value="used_up">Used Up</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label>Feature Type</Label>
            <Select value={featureFilter} onValueChange={setFeatureFilter}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Features</SelectItem>
                <SelectItem value="feedback">AI Feedback</SelectItem>
                <SelectItem value="certificate">Certificate</SelectItem>
                <SelectItem value="progress">Progress Tracking</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-end">
            <Button variant="outline" onClick={fetchCodes}>
              <Filter className="h-4 w-4 mr-2" />
              Apply Filters
            </Button>
          </div>
        </div>
      </Card>

      {/* Create Form */}
      {showCreateForm && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Create Promotional Code</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label>Code</Label>
              <div className="flex items-center space-x-2">
                <Input
                  placeholder="Enter code or auto-generate"
                  value={createForm.code}
                  onChange={(e) => setCreateForm({ ...createForm, code: e.target.value })}
                  disabled={createForm.autoGenerate}
                />
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={createForm.autoGenerate}
                    onCheckedChange={(checked) => setCreateForm({ ...createForm, autoGenerate: checked })}
                  />
                  <Label className="text-sm">Auto-generate</Label>
                </div>
              </div>
            </div>
            <div>
              <Label>Name</Label>
              <Input
                placeholder="Promotional code name"
                value={createForm.name}
                onChange={(e) => setCreateForm({ ...createForm, name: e.target.value })}
              />
            </div>
            <div className="md:col-span-2">
              <Label>Description</Label>
              <Textarea
                placeholder="Description of the promotional offer"
                value={createForm.description}
                onChange={(e) => setCreateForm({ ...createForm, description: e.target.value })}
              />
            </div>
            <div>
              <Label>Discount Type</Label>
              <Select value={createForm.type} onValueChange={(value: any) => setCreateForm({ ...createForm, type: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">Percentage</SelectItem>
                  <SelectItem value="fixed_amount">Fixed Amount</SelectItem>
                  <SelectItem value="free_access">Free Access</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Discount Value</Label>
              <Input
                type="number"
                placeholder={createForm.type === 'percentage' ? 'Percentage (1-100)' : 'Amount in UZS'}
                value={createForm.value}
                onChange={(e) => setCreateForm({ ...createForm, value: parseFloat(e.target.value) || 0 })}
              />
            </div>
            <div>
              <Label>Feature Type</Label>
              <Select value={createForm.featureType} onValueChange={(value: any) => setCreateForm({ ...createForm, featureType: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Features</SelectItem>
                  <SelectItem value="feedback">AI Feedback</SelectItem>
                  <SelectItem value="certificate">Certificate</SelectItem>
                  <SelectItem value="progress">Progress Tracking</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Usage Limit</Label>
              <Input
                type="number"
                placeholder="Leave empty for unlimited"
                value={createForm.usageLimit || ''}
                onChange={(e) => setCreateForm({ ...createForm, usageLimit: e.target.value ? parseInt(e.target.value) : undefined })}
              />
            </div>
            <div>
              <Label>Valid From</Label>
              <Input
                type="date"
                value={createForm.validFrom}
                onChange={(e) => setCreateForm({ ...createForm, validFrom: e.target.value })}
              />
            </div>
            <div>
              <Label>Valid Until</Label>
              <Input
                type="date"
                value={createForm.validUntil}
                onChange={(e) => setCreateForm({ ...createForm, validUntil: e.target.value })}
              />
            </div>
            <div className="md:col-span-2 flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={createForm.isPublic}
                  onCheckedChange={(checked) => setCreateForm({ ...createForm, isPublic: checked })}
                />
                <Label>Public (can be shared)</Label>
              </div>
            </div>
          </div>
          <div className="flex justify-end space-x-2 mt-6">
            <Button variant="outline" onClick={() => setShowCreateForm(false)}>
              Cancel
            </Button>
            <Button onClick={createCode}>
              Create Code
            </Button>
          </div>
        </Card>
      )}

      {/* Codes List */}
      <div className="grid grid-cols-1 gap-4">
        {isLoading ? (
          <div className="text-center py-8">Loading promotional codes...</div>
        ) : codes.length === 0 ? (
          <Card className="p-8 text-center">
            <p className="text-gray-500">No promotional codes found</p>
          </Card>
        ) : (
          codes.map((code) => (
            <Card key={code.id} className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    {getDiscountIcon(code.type)}
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-mono font-bold text-lg">{code.code}</span>
                        <Button variant="ghost" size="sm" onClick={() => copyCode(code.code)}>
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                      <p className="text-sm text-gray-600">{code.name}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(code.status)}>
                      {code.status}
                    </Badge>
                    <Badge variant="outline">
                      {code.featureType === 'all' ? 'All Features' : code.featureType}
                    </Badge>
                    {code.isPublic && (
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">
                        Public
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-lg">{getDiscountText(code)}</p>
                  <p className="text-sm text-gray-600">
                    Used: {code.usageCount}{code.usageLimit ? `/${code.usageLimit}` : ''}
                  </p>
                </div>
              </div>

              {code.description && (
                <p className="text-gray-600 mt-2">{code.description}</p>
              )}

              <div className="flex items-center justify-between mt-4 text-sm text-gray-500">
                <span>Valid: {new Date(code.validFrom).toLocaleDateString()} - {new Date(code.validUntil).toLocaleDateString()}</span>
                <div className="flex items-center space-x-4">
                  <Button variant="ghost" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="text-red-600">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
