'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TestResultEntryModal } from '@/components/forms/test-result-entry-modal';
import { FileText, Calendar, User, MapPin, Edit, Eye } from 'lucide-react';

interface TestRegistration {
  registration: {
    id: string;
    candidateId: string;
    candidateNumber: string;
    testDate: Date;
    testCenter: string;
    status: string;
    createdAt: Date;
    updatedAt: Date;
  };
  candidate: {
    id: string;
    fullName: string;
    email: string | null;
    passportNumber: string;
    nationality: string;
  } | null;
  result: {
    id: string;
    overallBandScore: string | null;
    status: string;
    createdAt: Date;
  } | null;
}

interface TestResultsTableProps {
  registrations: TestRegistration[];
}

export function TestResultsTable({ registrations }: TestResultsTableProps) {
  const [selectedRegistration, setSelectedRegistration] = useState<TestRegistration | null>(null);
  const [isResultModalOpen, setIsResultModalOpen] = useState(false);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      registered: { color: 'blue', label: 'Registered' },
      completed: { color: 'green', label: 'Completed' },
      cancelled: { color: 'red', label: 'Cancelled' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'gray', label: status };
    
    return (
      <Badge variant={config.color as any}>
        {config.label}
      </Badge>
    );
  };

  const getResultStatusBadge = (result: TestRegistration['result']) => {
    if (!result) {
      return <Badge variant="gray">No Result</Badge>;
    }

    const statusConfig = {
      draft: { color: 'yellow', label: 'Draft' },
      completed: { color: 'green', label: 'Completed' },
      verified: { color: 'blue', label: 'Verified' },
    };

    const config = statusConfig[result.status as keyof typeof statusConfig] || { color: 'gray', label: result.status };
    
    return (
      <Badge variant={config.color as any}>
        {config.label}
      </Badge>
    );
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleEnterResult = (registration: TestRegistration) => {
    setSelectedRegistration(registration);
    setIsResultModalOpen(true);
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Test Registrations & Results</h3>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Candidate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Test Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Result
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Band Score
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {registrations.map((registration) => (
                <tr key={registration.registration.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <User className="h-8 w-8 text-gray-400 mr-3" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {registration.candidate?.fullName || 'Unknown'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {registration.candidate?.passportNumber}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="flex items-center mb-1">
                        <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                        {formatDate(registration.registration.testDate)}
                      </div>
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 text-gray-400 mr-1" />
                        {registration.registration.testCenter}
                      </div>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      #{registration.registration.candidateNumber}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(registration.registration.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getResultStatusBadge(registration.result)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {registration.result?.overallBandScore ? (
                        <span className="text-lg font-bold text-blue-600">
                          {registration.result.overallBandScore}
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      {!registration.result && registration.registration.status === 'completed' && (
                        <Button
                          size="sm"
                          onClick={() => handleEnterResult(registration)}
                        >
                          <FileText className="h-4 w-4 mr-1" />
                          Enter Result
                        </Button>
                      )}
                      {registration.result && (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEnterResult(registration)}
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </Button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {registrations.length === 0 && (
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No registrations found</h3>
            <p className="mt-1 text-sm text-gray-500">No test registrations match your current filters.</p>
          </div>
        )}
      </div>

      {/* Test Result Entry Modal */}
      {selectedRegistration && (
        <TestResultEntryModal
          isOpen={isResultModalOpen}
          onClose={() => {
            setIsResultModalOpen(false);
            setSelectedRegistration(null);
          }}
          registration={selectedRegistration}
        />
      )}
    </>
  );
}
