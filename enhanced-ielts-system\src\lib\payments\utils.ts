/**
 * Payment utility functions
 */

import { db } from '@/lib/db';
import { paymentTransactions, accessPermissions, candidates, testResults } from '@/lib/db/schema';
import { eq, and, desc } from 'drizzle-orm';
import { createId } from '@paralleldrive/cuid2';
import {
  PaymentRequest,
  PaymentResponse,
  PaymentTransaction,
  AccessPermission,
  FeatureType,
  PaymentGateway,
  PaymentStatus,
  PAYMENT_PRICING,
  PAYMENT_VALIDATION,
  ACCESS_PERMISSION_CONFIG,
} from './types';
import { createClickPayment } from './click';
import { createPaymePayment } from './payme';

/**
 * Create a new payment transaction
 */
export async function createPaymentTransaction(request: PaymentRequest): Promise<PaymentResponse> {
  try {
    // Validate request
    const validation = validatePaymentRequest(request);
    if (!validation.isValid) {
      return {
        transactionId: '',
        status: 'failed',
        error: validation.error,
      };
    }

    // Check if candidate exists and belongs to organization
    const candidate = await db
      .select()
      .from(candidates)
      .where(
        and(
          eq(candidates.id, request.candidateId),
          eq(candidates.organizationId, request.organizationId)
        )
      )
      .limit(1);

    if (candidate.length === 0) {
      return {
        transactionId: '',
        status: 'failed',
        error: 'Candidate not found',
      };
    }

    // Check if feature is already unlocked
    if (request.resultId) {
      const existingAccess = await checkFeatureAccess(
        request.candidateId,
        request.featureType,
        request.resultId
      );
      
      if (existingAccess.hasAccess) {
        return {
          transactionId: '',
          status: 'failed',
          error: 'Feature already unlocked',
        };
      }
    }

    // Create transaction record
    const transactionId = createId();
    const [transaction] = await db
      .insert(paymentTransactions)
      .values({
        id: transactionId,
        candidateId: request.candidateId,
        organizationId: request.organizationId,
        amount: request.amount.toString(),
        currency: request.currency || 'UZS',
        gateway: request.gateway,
        status: 'pending',
        featureType: request.featureType,
        resultId: request.resultId,
        metadata: {},
      })
      .returning();

    // Generate payment URL based on gateway
    let paymentUrl: string | undefined;
    let gatewayTransactionId: string | undefined;

    if (request.gateway === 'click') {
      paymentUrl = await createClickPayment({
        amount: request.amount,
        merchant_trans_id: transactionId,
        return_url: request.returnUrl,
        merchant_user_id: request.candidateId,
      });
    } else if (request.gateway === 'payme') {
      paymentUrl = await createPaymePayment({
        amount: request.amount,
        account: {
          order_id: transactionId,
          user_id: request.candidateId,
        },
        return_url: request.returnUrl,
        description: request.description,
      });
    }
    // Manual payments don't need a payment URL

    return {
      transactionId,
      paymentUrl,
      status: 'pending',
      gatewayTransactionId,
    };
  } catch (error) {
    console.error('Payment creation error:', error);
    return {
      transactionId: '',
      status: 'failed',
      error: 'Payment creation failed',
    };
  }
}

/**
 * Update payment transaction status
 */
export async function updatePaymentStatus(
  transactionId: string,
  status: PaymentStatus,
  gatewayTransactionId?: string,
  metadata?: any
): Promise<boolean> {
  try {
    const updateData: any = {
      status,
      updatedAt: new Date(),
    };

    if (gatewayTransactionId) {
      updateData.gatewayTransactionId = gatewayTransactionId;
    }

    if (metadata) {
      updateData.metadata = metadata;
    }

    if (status === 'completed') {
      updateData.completedAt = new Date();
    }

    await db
      .update(paymentTransactions)
      .set(updateData)
      .where(eq(paymentTransactions.id, transactionId));

    // If payment completed, grant access to feature
    if (status === 'completed') {
      await grantFeatureAccess(transactionId);
    }

    return true;
  } catch (error) {
    console.error('Payment status update error:', error);
    return false;
  }
}

/**
 * Grant access to premium feature after successful payment
 */
export async function grantFeatureAccess(transactionId: string): Promise<boolean> {
  try {
    // Get transaction details
    const transaction = await db
      .select()
      .from(paymentTransactions)
      .where(eq(paymentTransactions.id, transactionId))
      .limit(1);

    if (transaction.length === 0) {
      return false;
    }

    const tx = transaction[0];

    // Calculate expiry date
    const expiresAt = ACCESS_PERMISSION_CONFIG.defaultExpiry[tx.featureType]
      ? new Date(Date.now() + ACCESS_PERMISSION_CONFIG.defaultExpiry[tx.featureType]!)
      : null;

    // Create access permission
    await db.insert(accessPermissions).values({
      id: createId(),
      candidateId: tx.candidateId,
      resultId: tx.resultId,
      featureType: tx.featureType,
      accessType: 'paid',
      grantedAt: new Date(),
      expiresAt,
      metadata: {
        transactionId,
      },
    });

    return true;
  } catch (error) {
    console.error('Feature access grant error:', error);
    return false;
  }
}

/**
 * Check if candidate has access to a premium feature
 */
export async function checkFeatureAccess(
  candidateId: string,
  featureType: FeatureType,
  resultId?: string
): Promise<{
  hasAccess: boolean;
  accessType?: 'paid' | 'promotional' | 'manual';
  expiresAt?: Date;
  daysRemaining?: number;
}> {
  try {
    let query = db
      .select()
      .from(accessPermissions)
      .where(
        and(
          eq(accessPermissions.candidateId, candidateId),
          eq(accessPermissions.featureType, featureType)
        )
      );

    if (resultId) {
      query = query.where(
        and(
          eq(accessPermissions.candidateId, candidateId),
          eq(accessPermissions.featureType, featureType),
          eq(accessPermissions.resultId, resultId)
        )
      );
    }

    const permissions = await query.orderBy(desc(accessPermissions.grantedAt));

    if (permissions.length === 0) {
      return { hasAccess: false };
    }

    const permission = permissions[0];

    // Check if permission has expired
    if (permission.expiresAt && new Date() > permission.expiresAt) {
      return { hasAccess: false };
    }

    // Calculate days remaining
    let daysRemaining: number | undefined;
    if (permission.expiresAt) {
      const msRemaining = permission.expiresAt.getTime() - Date.now();
      daysRemaining = Math.ceil(msRemaining / (1000 * 60 * 60 * 24));
    }

    return {
      hasAccess: true,
      accessType: permission.accessType,
      expiresAt: permission.expiresAt || undefined,
      daysRemaining,
    };
  } catch (error) {
    console.error('Feature access check error:', error);
    return { hasAccess: false };
  }
}

/**
 * Get payment history for a candidate
 */
export async function getPaymentHistory(candidateId: string): Promise<PaymentTransaction[]> {
  try {
    const transactions = await db
      .select()
      .from(paymentTransactions)
      .where(eq(paymentTransactions.candidateId, candidateId))
      .orderBy(desc(paymentTransactions.createdAt));

    return transactions.map(tx => ({
      id: tx.id,
      candidateId: tx.candidateId,
      organizationId: tx.organizationId,
      amount: parseFloat(tx.amount),
      currency: tx.currency,
      gateway: tx.gateway as PaymentGateway,
      gatewayTransactionId: tx.gatewayTransactionId || undefined,
      status: tx.status as PaymentStatus,
      featureType: tx.featureType as FeatureType,
      resultId: tx.resultId || undefined,
      metadata: tx.metadata as any,
      createdAt: tx.createdAt,
      completedAt: tx.completedAt || undefined,
    }));
  } catch (error) {
    console.error('Payment history error:', error);
    return [];
  }
}

/**
 * Validate payment request
 */
function validatePaymentRequest(request: PaymentRequest): {
  isValid: boolean;
  error?: string;
} {
  // Check amount
  if (request.amount < PAYMENT_VALIDATION.minAmount) {
    return {
      isValid: false,
      error: `Minimum amount is ${PAYMENT_VALIDATION.minAmount} ${request.currency || 'UZS'}`,
    };
  }

  if (request.amount > PAYMENT_VALIDATION.maxAmount) {
    return {
      isValid: false,
      error: `Maximum amount is ${PAYMENT_VALIDATION.maxAmount} ${request.currency || 'UZS'}`,
    };
  }

  // Check currency
  const currency = request.currency || 'UZS';
  if (!PAYMENT_VALIDATION.allowedCurrencies.includes(currency)) {
    return {
      isValid: false,
      error: `Currency ${currency} is not supported`,
    };
  }

  // Check feature type pricing
  const pricing = PAYMENT_PRICING[request.featureType];
  if (request.amount !== pricing.amount) {
    return {
      isValid: false,
      error: `Invalid amount for ${request.featureType}. Expected: ${pricing.amount} ${pricing.currency}`,
    };
  }

  return { isValid: true };
}

/**
 * Format amount for display
 */
export function formatAmount(amount: number, currency: string = 'UZS'): string {
  if (currency === 'UZS') {
    return new Intl.NumberFormat('uz-UZ', {
      style: 'currency',
      currency: 'UZS',
      minimumFractionDigits: 0,
    }).format(amount);
  }

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount);
}

/**
 * Get feature pricing
 */
export function getFeaturePricing(featureType: FeatureType) {
  return PAYMENT_PRICING[featureType];
}

/**
 * Check if manual payment needs approval
 */
export async function needsManualApproval(
  organizationId: string,
  amount: number
): Promise<boolean> {
  // All manual payments need approval for now
  return true;
}
