import { NextRequest, NextResponse } from 'next/server';
import { processPaymeWebhook } from '@/lib/payments/payme';
import { updatePaymentStatus } from '@/lib/payments/utils';

export async function POST(request: NextRequest) {
  try {
    // Get the raw body for JSON-RPC processing
    const body = await request.json();
    
    console.log('Payme webhook received:', body);

    // Process the webhook
    const result = await processPaymeWebhook(body);

    if (!result.success) {
      console.error('Payme webhook processing failed:', result.error);
      
      // Return JSON-RPC error response
      return NextResponse.json({
        jsonrpc: '2.0',
        id: body.id || null,
        error: result.error,
      });
    }

    // Update payment status in database for specific methods
    if (body.method === 'PerformTransaction' && body.params?.id) {
      await updatePaymentStatus(
        body.params.account?.order_id || body.params.id,
        'completed',
        body.params.id,
        {
          gatewayResponse: body,
          performTime: result.result?.perform_time,
        }
      );
    } else if (body.method === 'CancelTransaction' && body.params?.id) {
      await updatePaymentStatus(
        body.params.account?.order_id || body.params.id,
        'cancelled',
        body.params.id,
        {
          gatewayResponse: body,
          cancelTime: result.result?.cancel_time,
          cancelReason: body.params.reason,
        }
      );
    }

    // Return JSON-RPC success response
    return NextResponse.json({
      jsonrpc: '2.0',
      id: body.id || null,
      result: result.result,
    });
  } catch (error) {
    console.error('Payme webhook error:', error);
    
    // Return JSON-RPC error response
    return NextResponse.json({
      jsonrpc: '2.0',
      id: null,
      error: {
        code: -32700,
        message: 'Parse error',
      },
    }, { status: 500 });
  }
}

// Handle GET requests (for health checks)
export async function GET(request: NextRequest) {
  return NextResponse.json({
    status: 'Payme webhook endpoint is active',
    timestamp: new Date().toISOString(),
  });
}
