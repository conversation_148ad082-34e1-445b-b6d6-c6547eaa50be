import { InferSelectModel, InferInsertModel } from 'drizzle-orm';
import {
  promotionalRules,
  promotionalCodes,
  promotionalCodeUsage,
  referralLinks,
  referralConversions,
} from '@/lib/db/schema';

// Database model types
export type PromotionalRule = InferSelectModel<typeof promotionalRules>;
export type NewPromotionalRule = InferInsertModel<typeof promotionalRules>;

export type PromotionalCode = InferSelectModel<typeof promotionalCodes>;
export type NewPromotionalCode = InferInsertModel<typeof promotionalCodes>;

export type PromotionalCodeUsage = InferSelectModel<typeof promotionalCodeUsage>;
export type NewPromotionalCodeUsage = InferInsertModel<typeof promotionalCodeUsage>;

export type ReferralLink = InferSelectModel<typeof referralLinks>;
export type NewReferralLink = InferInsertModel<typeof referralLinks>;

export type ReferralConversion = InferSelectModel<typeof referralConversions>;
export type NewReferralConversion = InferInsertModel<typeof referralConversions>;

// Promotional system types
export type PromotionType = 'student_discount' | 'loyalty_reward' | 'time_based' | 'custom';
export type FeatureType = 'feedback' | 'certificate' | 'progress' | 'all';
export type DiscountType = 'percentage' | 'fixed_amount' | 'free_access';

// Promotional code validation result
export interface PromotionalCodeValidation {
  isValid: boolean;
  code?: PromotionalCode;
  error?: string;
  discountAmount?: number;
  finalAmount?: number;
  savings?: number;
}

// Promotional rule criteria
export interface PromotionalCriteria {
  studentStatus?: boolean;
  minTests?: number;
  testDateRange?: { start: string; end: string };
  customConditions?: any;
}

// Promotional benefits
export interface PromotionalBenefits {
  discountPercent?: number;
  freeAccess?: boolean;
  validityDays?: number;
}

// Discount calculation result
export interface DiscountCalculation {
  originalAmount: number;
  discountAmount: number;
  finalAmount: number;
  discountType: DiscountType;
  discountValue: number;
  savings: number;
  isValid: boolean;
  error?: string;
}

// Promotional code application request
export interface ApplyPromotionalCodeRequest {
  code: string;
  candidateId: string;
  featureType: FeatureType;
  originalAmount: number;
  resultId?: string;
  organizationId: string;
}

// Promotional code creation request
export interface CreatePromotionalCodeRequest {
  organizationId: string;
  code: string;
  name: string;
  description?: string;
  type: DiscountType;
  value: number;
  featureType: FeatureType;
  validFrom: Date;
  validUntil: Date;
  usageLimit?: number;
  minPurchaseAmount?: number;
  maxDiscountAmount?: number;
  isPublic?: boolean;
  createdBy: string;
}

// Referral system types
export interface ReferralStats {
  totalClicks: number;
  totalConversions: number;
  conversionRate: number;
  totalEarnings: number;
  pendingEarnings: number;
  paidEarnings: number;
}

export interface CreateReferralLinkRequest {
  candidateId: string;
  organizationId: string;
}

// Promotional analytics
export interface PromotionalAnalytics {
  totalCodes: number;
  activeCodes: number;
  totalUsage: number;
  totalDiscountGiven: number;
  topPerformingCodes: Array<{
    code: string;
    name: string;
    usageCount: number;
    totalDiscount: number;
  }>;
  usageByFeature: Record<FeatureType, number>;
  usageByMonth: Array<{
    month: string;
    usage: number;
    discount: number;
  }>;
}

// Promotional rule eligibility check
export interface PromotionalEligibility {
  isEligible: boolean;
  rules: PromotionalRule[];
  benefits: PromotionalBenefits[];
  reason?: string;
}

// Commission configuration
export const REFERRAL_CONFIG = {
  defaultCommissionRate: 10, // 10% commission
  minCommissionAmount: 1000, // Minimum 1000 UZS commission
  payoutThreshold: 50000, // Minimum 50,000 UZS to request payout
  cookieExpiry: 30 * 24 * 60 * 60 * 1000, // 30 days in milliseconds
};

// Promotional code generation settings
export const PROMO_CODE_CONFIG = {
  defaultLength: 8,
  allowedCharacters: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
  prefixes: {
    student: 'STU',
    loyalty: 'LOY',
    time_based: 'TIME',
    custom: 'PROMO',
  },
  maxUsageLimit: 10000,
  maxDiscountPercent: 100,
  maxDiscountAmount: 1000000, // 1,000,000 UZS
};

// Validation rules
export const PROMOTIONAL_VALIDATION = {
  codeMinLength: 3,
  codeMaxLength: 20,
  nameMinLength: 3,
  nameMaxLength: 100,
  descriptionMaxLength: 500,
  minDiscountPercent: 1,
  maxDiscountPercent: 100,
  minFixedAmount: 1000, // 1,000 UZS
  maxFixedAmount: 500000, // 500,000 UZS
  maxValidityDays: 365,
};

// Error codes
export const PROMOTIONAL_ERROR_CODES = {
  CODE_NOT_FOUND: 'CODE_NOT_FOUND',
  CODE_EXPIRED: 'CODE_EXPIRED',
  CODE_INACTIVE: 'CODE_INACTIVE',
  CODE_USED_UP: 'CODE_USED_UP',
  CODE_ALREADY_USED: 'CODE_ALREADY_USED',
  MINIMUM_AMOUNT_NOT_MET: 'MINIMUM_AMOUNT_NOT_MET',
  FEATURE_TYPE_MISMATCH: 'FEATURE_TYPE_MISMATCH',
  ORGANIZATION_MISMATCH: 'ORGANIZATION_MISMATCH',
  INVALID_DISCOUNT_VALUE: 'INVALID_DISCOUNT_VALUE',
  USAGE_LIMIT_EXCEEDED: 'USAGE_LIMIT_EXCEEDED',
} as const;

export type PromotionalErrorCode = typeof PROMOTIONAL_ERROR_CODES[keyof typeof PROMOTIONAL_ERROR_CODES];

// Success messages
export const PROMOTIONAL_SUCCESS_MESSAGES = {
  CODE_APPLIED: 'Promotional code applied successfully',
  CODE_CREATED: 'Promotional code created successfully',
  CODE_UPDATED: 'Promotional code updated successfully',
  CODE_DELETED: 'Promotional code deleted successfully',
  REFERRAL_CREATED: 'Referral link created successfully',
} as const;
