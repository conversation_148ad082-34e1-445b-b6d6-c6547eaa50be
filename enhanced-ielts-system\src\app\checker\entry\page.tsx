import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { candidates, testRegistrations, testResults } from '@/lib/db/schema';
import { eq, and, desc, isNull } from 'drizzle-orm';
import { TestResultEntryForm } from '@/components/forms/test-result-entry-form';
import { RecentResultsTable } from '@/components/specialized/recent-results-table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, Plus, Search } from 'lucide-react';
import Link from 'next/link';

export default async function CheckerEntryPage() {
  const session = await auth();
  
  if (!session?.user?.organizationId) {
    return <div>Access denied</div>;
  }

  // Get recent test registrations that need results
  const pendingRegistrations = await db
    .select({
      registration: testRegistrations,
      candidate: candidates,
    })
    .from(testRegistrations)
    .leftJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
    .leftJoin(testResults, eq(testRegistrations.id, testResults.registrationId))
    .where(
      and(
        eq(testRegistrations.organizationId, session.user.organizationId),
        eq(testRegistrations.status, 'completed'),
        isNull(testResults.id) // No result exists yet
      )
    )
    .orderBy(desc(testRegistrations.testDate))
    .limit(10);

  // Get recent results entered by this checker
  const recentResults = await db
    .select({
      result: testResults,
      candidate: candidates,
      registration: testRegistrations,
    })
    .from(testResults)
    .leftJoin(testRegistrations, eq(testResults.registrationId, testRegistrations.id))
    .leftJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
    .where(
      and(
        eq(testResults.organizationId, session.user.organizationId),
        eq(testResults.enteredBy, session.user.id)
      )
    )
    .orderBy(desc(testResults.createdAt))
    .limit(5);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Test Result Entry</h1>
          <p className="text-muted-foreground">
            Enter and manage IELTS test results for completed tests
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild variant="outline">
            <Link href="/checker/results">
              <Search className="mr-2 h-4 w-4" />
              View All Results
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Test Result Entry Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Enter New Result
            </CardTitle>
            <CardDescription>
              Enter test results for completed IELTS tests
            </CardDescription>
          </CardHeader>
          <CardContent>
            <TestResultEntryForm organizationId={session.user.organizationId} />
          </CardContent>
        </Card>

        {/* Pending Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Pending Results ({pendingRegistrations.length})
            </CardTitle>
            <CardDescription>
              Tests completed but awaiting result entry
            </CardDescription>
          </CardHeader>
          <CardContent>
            {pendingRegistrations.length === 0 ? (
              <p className="text-sm text-muted-foreground text-center py-4">
                No pending test results to enter
              </p>
            ) : (
              <div className="space-y-3">
                {pendingRegistrations.slice(0, 5).map(({ registration, candidate }) => (
                  <div
                    key={registration.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div>
                      <p className="font-medium">{candidate?.fullName}</p>
                      <p className="text-sm text-muted-foreground">
                        Test Date: {registration.testDate.toLocaleDateString()}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Type: {registration.testType}
                      </p>
                    </div>
                    <Button size="sm" asChild>
                      <Link href={`/checker/entry?registration=${registration.id}`}>
                        Enter Result
                      </Link>
                    </Button>
                  </div>
                ))}
                {pendingRegistrations.length > 5 && (
                  <div className="text-center pt-2">
                    <Button variant="outline" size="sm" asChild>
                      <Link href="/checker/results?filter=pending">
                        View All Pending ({pendingRegistrations.length})
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Results */}
      {recentResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Results Entered</CardTitle>
            <CardDescription>
              Your recently entered test results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RecentResultsTable results={recentResults} />
          </CardContent>
        </Card>
      )}
    </div>
  );
}
