'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, Loader2, AlertCircle } from 'lucide-react';

const searchSchema = z.object({
  passport: z.string().min(5, 'Passport/Birth certificate number must be at least 5 characters'),
  dateOfBirth: z.string().min(1, 'Date of birth is required'),
});

type SearchForm = z.infer<typeof searchSchema>;

export function PublicSearchForm() {
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SearchForm>({
    resolver: zod<PERSON><PERSON><PERSON>ver(searchSchema),
  });

  const onSubmit = async (data: SearchForm) => {
    setIsSearching(true);
    setSearchError(null);

    try {
      // Validate the search parameters by making a quick API call
      const response = await fetch('/api/public/validate-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Search validation failed');
      }

      // Redirect to search results
      const params = new URLSearchParams({
        passport: data.passport.trim(),
        dob: data.dateOfBirth,
      });

      router.push(`/search?${params.toString()}`);
    } catch (error) {
      console.error('Search error:', error);
      setSearchError(error instanceof Error ? error.message : 'Search failed. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Passport/Birth Certificate Number */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Passport or Birth Certificate Number
        </label>
        <Input
          {...register('passport')}
          placeholder="Enter your passport or birth certificate number"
          className="text-lg"
          error={errors.passport?.message}
          disabled={isSearching}
        />
        <p className="text-xs text-gray-500 mt-1">
          Enter the exact number used during test registration
        </p>
      </div>

      {/* Date of Birth */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Date of Birth
        </label>
        <Input
          {...register('dateOfBirth')}
          type="date"
          className="text-lg"
          error={errors.dateOfBirth?.message}
          disabled={isSearching}
        />
        <p className="text-xs text-gray-500 mt-1">
          Select your date of birth as registered
        </p>
      </div>

      {/* Error Message */}
      {searchError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start">
            <AlertCircle className="h-5 w-5 text-red-600 mr-3 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-red-800">Search Error</h3>
              <p className="text-sm text-red-700 mt-1">{searchError}</p>
            </div>
          </div>
        </div>
      )}

      {/* Search Button */}
      <Button
        type="submit"
        disabled={isSearching}
        className="w-full text-lg py-3"
      >
        {isSearching ? (
          <>
            <Loader2 className="h-5 w-5 mr-2 animate-spin" />
            Searching...
          </>
        ) : (
          <>
            <Search className="h-5 w-5 mr-2" />
            Search My Results
          </>
        )}
      </Button>

      {/* Privacy Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <div className="bg-blue-100 p-1 rounded-full mr-3 mt-0.5">
            <AlertCircle className="h-4 w-4 text-blue-600" />
          </div>
          <div className="text-sm text-blue-800">
            <h4 className="font-medium mb-1">Privacy & Security</h4>
            <p>
              Your personal information is protected and only used to retrieve your test results. 
              We do not store or share your search data.
            </p>
          </div>
        </div>
      </div>

      {/* Tips */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Search Tips:</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Use the exact passport/birth certificate number from your registration</li>
          <li>• Check for any spaces or special characters in your document number</li>
          <li>• Ensure your date of birth matches your registration details</li>
          <li>• Results are available immediately after test completion</li>
        </ul>
      </div>
    </form>
  );
}
