import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { testResults, testRegistrations, candidates } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';
import { validateTestResult } from '@/lib/utils/ielts-scoring';

const createTestResultSchema = z.object({
  testRegistrationId: z.string().min(1),
  listeningScore: z.number().min(0).max(40),
  listeningBandScore: z.number().min(0).max(9),
  readingScore: z.number().min(0).max(40),
  readingBandScore: z.number().min(0).max(9),
  writingTask1Score: z.number().min(0).max(9),
  writingTask2Score: z.number().min(0).max(9),
  writingBandScore: z.number().min(0).max(9),
  speakingFluencyScore: z.number().min(0).max(9),
  speakingLexicalScore: z.number().min(0).max(9),
  speakingGrammarScore: z.number().min(0).max(9),
  speakingPronunciationScore: z.number().min(0).max(9),
  speakingBandScore: z.number().min(0).max(9),
  overallBandScore: z.number().min(0).max(9),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (session.user.role !== 'admin' && session.user.role !== 'checker' && !session.user.masterAdmin) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createTestResultSchema.parse(body);

    // Validate test result data using our utility function
    const validation = validateTestResult({
      listeningScore: validatedData.listeningScore,
      readingScore: validatedData.readingScore,
      writingTask1Score: validatedData.writingTask1Score,
      writingTask2Score: validatedData.writingTask2Score,
      speakingFluencyScore: validatedData.speakingFluencyScore,
      speakingLexicalScore: validatedData.speakingLexicalScore,
      speakingGrammarScore: validatedData.speakingGrammarScore,
      speakingPronunciationScore: validatedData.speakingPronunciationScore,
    });

    if (!validation.isValid) {
      return NextResponse.json(
        { error: 'Invalid test result data', details: validation.errors },
        { status: 400 }
      );
    }

    // Verify test registration exists and belongs to the organization
    const registration = await db
      .select({
        registration: testRegistrations,
        candidate: candidates,
      })
      .from(testRegistrations)
      .leftJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(
        and(
          eq(testRegistrations.id, validatedData.testRegistrationId),
          eq(candidates.organizationId, session.user.organizationId)
        )
      )
      .limit(1);

    if (registration.length === 0) {
      return NextResponse.json(
        { error: 'Test registration not found or does not belong to your organization' },
        { status: 404 }
      );
    }

    // Check if result already exists
    const existingResult = await db
      .select()
      .from(testResults)
      .where(eq(testResults.testRegistrationId, validatedData.testRegistrationId))
      .limit(1);

    if (existingResult.length > 0) {
      return NextResponse.json(
        { error: 'Test result already exists for this registration' },
        { status: 409 }
      );
    }

    // Create test result
    const [result] = await db
      .insert(testResults)
      .values({
        testRegistrationId: validatedData.testRegistrationId,
        listeningScore: validatedData.listeningScore,
        listeningBandScore: validatedData.listeningBandScore.toString(),
        readingScore: validatedData.readingScore,
        readingBandScore: validatedData.readingBandScore.toString(),
        writingTask1Score: validatedData.writingTask1Score.toString(),
        writingTask2Score: validatedData.writingTask2Score.toString(),
        writingBandScore: validatedData.writingBandScore.toString(),
        speakingFluencyScore: validatedData.speakingFluencyScore.toString(),
        speakingLexicalScore: validatedData.speakingLexicalScore.toString(),
        speakingGrammarScore: validatedData.speakingGrammarScore.toString(),
        speakingPronunciationScore: validatedData.speakingPronunciationScore.toString(),
        speakingBandScore: validatedData.speakingBandScore.toString(),
        overallBandScore: validatedData.overallBandScore.toString(),
        status: 'completed',
        enteredBy: session.user.id,
      })
      .returning();

    // Update test registration status to completed
    await db
      .update(testRegistrations)
      .set({
        status: 'completed',
        updatedAt: new Date(),
      })
      .where(eq(testRegistrations.id, validatedData.testRegistrationId));

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Error creating test result:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to create test result' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const candidateId = searchParams.get('candidateId');
    const registrationId = searchParams.get('registrationId');

    let query = db
      .select({
        result: testResults,
        registration: testRegistrations,
        candidate: candidates,
      })
      .from(testResults)
      .leftJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .leftJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(eq(candidates.organizationId, session.user.organizationId));

    // Apply filters
    if (candidateId) {
      query = query.where(
        and(
          eq(candidates.organizationId, session.user.organizationId),
          eq(candidates.id, candidateId)
        )
      );
    }

    if (registrationId) {
      query = query.where(
        and(
          eq(candidates.organizationId, session.user.organizationId),
          eq(testRegistrations.id, registrationId)
        )
      );
    }

    const results = await query;

    return NextResponse.json(results);
  } catch (error) {
    console.error('Error fetching test results:', error);
    return NextResponse.json(
      { error: 'Failed to fetch test results' },
      { status: 500 }
    );
  }
}
