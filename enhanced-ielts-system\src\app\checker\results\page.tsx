import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { candidates, testRegistrations, testResults } from '@/lib/db/schema';
import { eq, and, desc, like, or, ilike } from 'drizzle-orm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Search, 
  Edit, 
  Eye, 
  Plus,
  Filter,
  Download,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';
import { Suspense } from 'react';

interface SearchParams {
  search?: string;
  filter?: string;
  page?: string;
}

export default async function CheckerResultsPage({
  searchParams,
}: {
  searchParams: SearchParams;
}) {
  const session = await auth();
  
  if (!session?.user?.organizationId) {
    return <div>Access denied</div>;
  }

  const search = searchParams.search || '';
  const filter = searchParams.filter || 'all';
  const page = parseInt(searchParams.page || '1');
  const limit = 20;
  const offset = (page - 1) * limit;

  // Build where conditions
  let whereConditions = [
    eq(testResults.organizationId, session.user.organizationId)
  ];

  // Add search conditions
  if (search) {
    whereConditions.push(
      or(
        ilike(candidates.fullName, `%${search}%`),
        ilike(candidates.email, `%${search}%`),
        ilike(candidates.passportNumber, `%${search}%`)
      )
    );
  }

  // Add filter conditions
  if (filter === 'draft') {
    whereConditions.push(eq(testResults.status, 'draft'));
  } else if (filter === 'completed') {
    whereConditions.push(eq(testResults.status, 'completed'));
  } else if (filter === 'verified') {
    whereConditions.push(eq(testResults.status, 'verified'));
  }

  // Get test results with candidate and registration info
  const results = await db
    .select({
      result: testResults,
      candidate: candidates,
      registration: testRegistrations,
    })
    .from(testResults)
    .leftJoin(testRegistrations, eq(testResults.registrationId, testRegistrations.id))
    .leftJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
    .where(and(...whereConditions))
    .orderBy(desc(testResults.createdAt))
    .limit(limit)
    .offset(offset);

  // Get counts for different statuses
  const statusCounts = await db
    .select({
      status: testResults.status,
      count: testResults.id,
    })
    .from(testResults)
    .where(eq(testResults.organizationId, session.user.organizationId))
    .groupBy(testResults.status);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'verified':
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'draft':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'verified':
        return 'bg-blue-100 text-blue-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Test Results</h1>
          <p className="text-muted-foreground">
            View and manage all IELTS test results
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/checker/entry">
              <Plus className="mr-2 h-4 w-4" />
              Enter New Result
            </Link>
          </Button>
        </div>
      </div>

      {/* Status Overview */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Total Results</p>
                <p className="text-2xl font-bold">{results.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        {statusCounts.map(({ status, count }) => (
          <Card key={status}>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                {getStatusIcon(status)}
                <div>
                  <p className="text-sm font-medium capitalize">{status}</p>
                  <p className="text-2xl font-bold">{count}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search & Filter
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search by candidate name, email, or passport..."
                defaultValue={search}
                className="w-full"
              />
            </div>
            <div className="flex gap-2">
              <Button variant={filter === 'all' ? 'default' : 'outline'} size="sm" asChild>
                <Link href="/checker/results?filter=all">All</Link>
              </Button>
              <Button variant={filter === 'draft' ? 'default' : 'outline'} size="sm" asChild>
                <Link href="/checker/results?filter=draft">Draft</Link>
              </Button>
              <Button variant={filter === 'completed' ? 'default' : 'outline'} size="sm" asChild>
                <Link href="/checker/results?filter=completed">Completed</Link>
              </Button>
              <Button variant={filter === 'verified' ? 'default' : 'outline'} size="sm" asChild>
                <Link href="/checker/results?filter=verified">Verified</Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Table */}
      <Card>
        <CardHeader>
          <CardTitle>Test Results</CardTitle>
          <CardDescription>
            {results.length} results found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {results.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-semibold text-gray-900">No results found</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                No test results match your current search and filter criteria.
              </p>
              <div className="mt-6">
                <Button asChild>
                  <Link href="/checker/entry">
                    <Plus className="mr-2 h-4 w-4" />
                    Enter New Result
                  </Link>
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {results.map(({ result, candidate, registration }) => (
                <div
                  key={result.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3">
                      <div>
                        <p className="font-medium">{candidate?.fullName}</p>
                        <p className="text-sm text-muted-foreground">
                          {candidate?.email} • {candidate?.passportNumber}
                        </p>
                      </div>
                      <Badge className={getStatusColor(result.status)}>
                        {result.status}
                      </Badge>
                    </div>
                    <div className="mt-2 flex items-center gap-4 text-sm text-muted-foreground">
                      <span>Test Date: {registration?.testDate.toLocaleDateString()}</span>
                      <span>Type: {registration?.testType}</span>
                      <span>Overall: {result.overallBand}</span>
                      <span>Created: {result.createdAt.toLocaleDateString()}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/admin/results/${result.id}`}>
                        <Eye className="mr-2 h-4 w-4" />
                        View
                      </Link>
                    </Button>
                    {result.status === 'draft' && (
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/checker/entry?edit=${result.id}`}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </Link>
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
