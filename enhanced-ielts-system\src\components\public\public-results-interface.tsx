'use client';

import { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ResultsTab } from './tabs/results-tab';
import { ProgressTab } from './tabs/progress-tab';
import { FeedbackTab } from './tabs/feedback-tab';
import { CertificateTab } from './tabs/certificate-tab';
import {
  FileText,
  TrendingUp,
  MessageSquare,
  Award,
  User,
  Calendar,
  MapPin,
  ArrowLeft
} from 'lucide-react';
import Link from 'next/link';

interface Candidate {
  id: string;
  fullName: string;
  email: string | null;
  passportNumber: string;
  nationality: string;
  dateOfBirth: Date;
  totalTests: number;
}

interface Organization {
  id: string;
  name: string;
  slug: string;
}

interface TestData {
  registration: {
    id: string;
    candidateNumber: string;
    testDate: Date;
    testCenter: string;
    status: string;
  };
  result: {
    id: string;
    listeningScore: number | null;
    listeningBandScore: string | null;
    readingScore: number | null;
    readingBandScore: string | null;
    writingTask1Score: string | null;
    writingTask2Score: string | null;
    writingBandScore: string | null;
    speakingFluencyScore: string | null;
    speakingLexicalScore: string | null;
    speakingGrammarScore: string | null;
    speakingPronunciationScore: string | null;
    speakingBandScore: string | null;
    overallBandScore: string | null;
    status: string;
    createdAt: Date;
  } | null;
}

interface PublicResultsInterfaceProps {
  candidate: Candidate;
  organization: Organization | null;
  testData: TestData[];
}

export function PublicResultsInterface({
  candidate,
  organization,
  testData
}: PublicResultsInterfaceProps) {
  const [activeTab, setActiveTab] = useState('results');

  // Get completed tests with results
  const completedTests = testData.filter(test => test.result && test.result.status === 'completed');
  const latestTest = completedTests[0];

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-lg mb-6">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-100 p-3 rounded-full">
                <User className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{candidate.fullName}</h1>
                <p className="text-gray-600">IELTS Test Results</p>
              </div>
            </div>
            <Link href="/search">
              <button className="inline-flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                <ArrowLeft className="h-4 w-4 mr-2" />
                New Search
              </button>
            </Link>
          </div>

          {/* Candidate Info */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-4 w-4 text-gray-400" />
              <div>
                <div className="text-xs text-gray-500">Document Number</div>
                <div className="font-medium">{candidate.passportNumber}</div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-400" />
              <div>
                <div className="text-xs text-gray-500">Date of Birth</div>
                <div className="font-medium">{formatDate(candidate.dateOfBirth)}</div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-gray-400" />
              <div>
                <div className="text-xs text-gray-500">Test Center</div>
                <div className="font-medium">{organization?.name || 'Unknown'}</div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-gray-400" />
              <div>
                <div className="text-xs text-gray-500">Total Tests</div>
                <div className="font-medium">{candidate.totalTests}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Latest Result Summary */}
        {latestTest && (
          <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Latest Test Result</h3>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {latestTest.result?.overallBandScore || 'N/A'}
                </div>
                <div className="text-sm text-gray-600">Overall</div>
              </div>
              <div className="text-center">
                <div className="text-xl font-semibold text-gray-900">
                  {latestTest.result?.listeningBandScore || 'N/A'}
                </div>
                <div className="text-sm text-gray-600">Listening</div>
              </div>
              <div className="text-center">
                <div className="text-xl font-semibold text-gray-900">
                  {latestTest.result?.readingBandScore || 'N/A'}
                </div>
                <div className="text-sm text-gray-600">Reading</div>
              </div>
              <div className="text-center">
                <div className="text-xl font-semibold text-gray-900">
                  {latestTest.result?.writingBandScore || 'N/A'}
                </div>
                <div className="text-sm text-gray-600">Writing</div>
              </div>
              <div className="text-center">
                <div className="text-xl font-semibold text-gray-900">
                  {latestTest.result?.speakingBandScore || 'N/A'}
                </div>
                <div className="text-sm text-gray-600">Speaking</div>
              </div>
            </div>
            <div className="mt-3 text-sm text-gray-600">
              Test Date: {formatDate(latestTest.registration.testDate)} •
              Test Center: {latestTest.registration.testCenter}
            </div>
          </div>
        )}
      </div>

      {/* Tabbed Interface */}
      <div className="bg-white rounded-lg shadow-lg">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4 bg-gray-50 p-1 rounded-t-lg">
            <TabsTrigger value="results" className="flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>Results</span>
            </TabsTrigger>
            <TabsTrigger value="progress" className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4" />
              <span>Progress</span>
            </TabsTrigger>
            <TabsTrigger value="feedback" className="flex items-center space-x-2">
              <MessageSquare className="h-4 w-4" />
              <span>Feedback</span>
            </TabsTrigger>
            <TabsTrigger value="certificate" className="flex items-center space-x-2">
              <Award className="h-4 w-4" />
              <span>Certificate</span>
            </TabsTrigger>
          </TabsList>

          <div className="p-6">
            <TabsContent value="results" className="mt-0">
              <ResultsTab
                candidate={candidate}
                testData={testData}
              />
            </TabsContent>

            <TabsContent value="progress" className="mt-0">
              <ProgressTab
                candidate={candidate}
                testData={completedTests}
              />
            </TabsContent>

            <TabsContent value="feedback" className="mt-0">
              <FeedbackTab
                candidate={candidate}
                testData={completedTests}
              />
            </TabsContent>

            <TabsContent value="certificate" className="mt-0">
              <CertificateTab
                candidate={candidate}
                testData={completedTests}
              />
            </TabsContent>
          </div>
        </Tabs>
      </div>

      {/* Footer Info */}
      <div className="mt-8 text-center text-gray-600">
        <p className="text-sm">
          Results are updated in real-time. For questions about your results,
          contact your test center directly.
        </p>
      </div>
    </div>
  );
}
