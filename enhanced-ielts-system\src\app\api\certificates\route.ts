import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { certificateLifecycle } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  const session = await auth();
  
  if (!session?.user?.organizationId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const resultId = searchParams.get('resultId');

  if (!resultId) {
    return NextResponse.json({ error: 'Result ID required' }, { status: 400 });
  }

  try {
    const certificate = await db
      .select()
      .from(certificateLifecycle)
      .where(eq(certificateLifecycle.resultId, resultId))
      .limit(1);

    if (certificate.length === 0) {
      return NextResponse.json({ error: 'Certificate not found' }, { status: 404 });
    }

    const cert = certificate[0];
    
    return NextResponse.json({
      id: cert.id,
      serialNumber: cert.serialNumber,
      generatedAt: cert.generatedAt.toISOString(),
      expiresAt: cert.expiresAt.toISOString(),
      status: cert.status,
      downloadUrl: `/api/certificates/download/${cert.id}`,
    });
  } catch (error) {
    console.error('Error fetching certificate:', error);
    return NextResponse.json({ error: 'Failed to fetch certificate' }, { status: 500 });
  }
}
