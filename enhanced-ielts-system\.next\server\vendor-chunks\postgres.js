"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/postgres";
exports.ids = ["vendor-chunks/postgres"];
exports.modules = {

/***/ "(rsc)/./node_modules/postgres/src/bytes.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/bytes.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst size = 256\nlet buffer = Buffer.allocUnsafe(size)\n\nconst messages = 'BCcDdEFfHPpQSX'.split('').reduce((acc, x) => {\n  const v = x.charCodeAt(0)\n  acc[x] = () => {\n    buffer[0] = v\n    b.i = 5\n    return b\n  }\n  return acc\n}, {})\n\nconst b = Object.assign(reset, messages, {\n  N: String.fromCharCode(0),\n  i: 0,\n  inc(x) {\n    b.i += x\n    return b\n  },\n  str(x) {\n    const length = Buffer.byteLength(x)\n    fit(length)\n    b.i += buffer.write(x, b.i, length, 'utf8')\n    return b\n  },\n  i16(x) {\n    fit(2)\n    buffer.writeUInt16BE(x, b.i)\n    b.i += 2\n    return b\n  },\n  i32(x, i) {\n    if (i || i === 0) {\n      buffer.writeUInt32BE(x, i)\n      return b\n    }\n    fit(4)\n    buffer.writeUInt32BE(x, b.i)\n    b.i += 4\n    return b\n  },\n  z(x) {\n    fit(x)\n    buffer.fill(0, b.i, b.i + x)\n    b.i += x\n    return b\n  },\n  raw(x) {\n    buffer = Buffer.concat([buffer.subarray(0, b.i), x])\n    b.i = buffer.length\n    return b\n  },\n  end(at = 1) {\n    buffer.writeUInt32BE(b.i - at, at)\n    const out = buffer.subarray(0, b.i)\n    b.i = 0\n    buffer = Buffer.allocUnsafe(size)\n    return out\n  }\n})\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (b);\n\nfunction fit(x) {\n  if (buffer.length - b.i < x) {\n    const prev = buffer\n        , length = prev.length\n\n    buffer = Buffer.allocUnsafe(length + (length >> 1) + x)\n    prev.copy(buffer)\n  }\n}\n\nfunction reset() {\n  b.i = 0\n  return b\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/bytes.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/connection.js":
/*!*************************************************!*\
  !*** ./node_modules/postgres/src/connection.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var net__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! net */ \"net\");\n/* harmony import */ var tls__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tls */ \"tls\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! stream */ \"stream\");\n/* harmony import */ var perf_hooks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! perf_hooks */ \"perf_hooks\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./types.js */ \"(rsc)/./node_modules/postgres/src/types.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors.js */ \"(rsc)/./node_modules/postgres/src/errors.js\");\n/* harmony import */ var _result_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./result.js */ \"(rsc)/./node_modules/postgres/src/result.js\");\n/* harmony import */ var _queue_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./queue.js */ \"(rsc)/./node_modules/postgres/src/queue.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./query.js */ \"(rsc)/./node_modules/postgres/src/query.js\");\n/* harmony import */ var _bytes_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./bytes.js */ \"(rsc)/./node_modules/postgres/src/bytes.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Connection);\n\nlet uid = 1\n\nconst Sync = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().S().end()\n    , Flush = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().H().end()\n    , SSLRequest = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().i32(8).i32(80877103).end(8)\n    , ExecuteUnnamed = Buffer.concat([(0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().E().str(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).i32(0).end(), Sync])\n    , DescribeUnnamed = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().D().str('S').str(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end()\n    , noop = () => { /* noop */ }\n\nconst retryRoutines = new Set([\n  'FetchPreparedStatement',\n  'RevalidateCachedQuery',\n  'transformAssignedExpr'\n])\n\nconst errorFields = {\n  83  : 'severity_local',    // S\n  86  : 'severity',          // V\n  67  : 'code',              // C\n  77  : 'message',           // M\n  68  : 'detail',            // D\n  72  : 'hint',              // H\n  80  : 'position',          // P\n  112 : 'internal_position', // p\n  113 : 'internal_query',    // q\n  87  : 'where',             // W\n  115 : 'schema_name',       // s\n  116 : 'table_name',        // t\n  99  : 'column_name',       // c\n  100 : 'data type_name',    // d\n  110 : 'constraint_name',   // n\n  70  : 'file',              // F\n  76  : 'line',              // L\n  82  : 'routine'            // R\n}\n\nfunction Connection(options, queues = {}, { onopen = noop, onend = noop, onclose = noop } = {}) {\n  const {\n    ssl,\n    max,\n    user,\n    host,\n    port,\n    database,\n    parsers,\n    transform,\n    onnotice,\n    onnotify,\n    onparameter,\n    max_pipeline,\n    keep_alive,\n    backoff,\n    target_session_attrs\n  } = options\n\n  const sent = (0,_queue_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])()\n      , id = uid++\n      , backend = { pid: null, secret: null }\n      , idleTimer = timer(end, options.idle_timeout)\n      , lifeTimer = timer(end, options.max_lifetime)\n      , connectTimer = timer(connectTimedOut, options.connect_timeout)\n\n  let socket = null\n    , cancelMessage\n    , result = new _result_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]()\n    , incoming = Buffer.alloc(0)\n    , needsTypes = options.fetch_types\n    , backendParameters = {}\n    , statements = {}\n    , statementId = Math.random().toString(36).slice(2)\n    , statementCount = 1\n    , closedDate = 0\n    , remaining = 0\n    , hostIndex = 0\n    , retries = 0\n    , length = 0\n    , delay = 0\n    , rows = 0\n    , serverSignature = null\n    , nextWriteTimer = null\n    , terminated = false\n    , incomings = null\n    , results = null\n    , initial = null\n    , ending = null\n    , stream = null\n    , chunk = null\n    , ended = null\n    , nonce = null\n    , query = null\n    , final = null\n\n  const connection = {\n    queue: queues.closed,\n    idleTimer,\n    connect(query) {\n      initial = query\n      reconnect()\n    },\n    terminate,\n    execute,\n    cancel,\n    end,\n    count: 0,\n    id\n  }\n\n  queues.closed && queues.closed.push(connection)\n\n  return connection\n\n  async function createSocket() {\n    let x\n    try {\n      x = options.socket\n        ? (await Promise.resolve(options.socket(options)))\n        : new net__WEBPACK_IMPORTED_MODULE_0__.Socket()\n    } catch (e) {\n      error(e)\n      return\n    }\n    x.on('error', error)\n    x.on('close', closed)\n    x.on('drain', drain)\n    return x\n  }\n\n  async function cancel({ pid, secret }, resolve, reject) {\n    try {\n      cancelMessage = (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().i32(16).i32(80877102).i32(pid).i32(secret).end(16)\n      await connect()\n      socket.once('error', reject)\n      socket.once('close', resolve)\n    } catch (error) {\n      reject(error)\n    }\n  }\n\n  function execute(q) {\n    if (terminated)\n      return queryError(q, _errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_DESTROYED', options))\n\n    if (q.cancelled)\n      return\n\n    try {\n      q.state = backend\n      query\n        ? sent.push(q)\n        : (query = q, query.active = true)\n\n      build(q)\n      return write(toBuffer(q))\n        && !q.describeFirst\n        && !q.cursorFn\n        && sent.length < max_pipeline\n        && (!q.options.onexecute || q.options.onexecute(connection))\n    } catch (error) {\n      sent.length === 0 && write(Sync)\n      errored(error)\n      return true\n    }\n  }\n\n  function toBuffer(q) {\n    if (q.parameters.length >= 65534)\n      throw _errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.generic('MAX_PARAMETERS_EXCEEDED', 'Max number of parameters (65534) exceeded')\n\n    return q.options.simple\n      ? (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().Q().str(q.statement.string + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end()\n      : q.describeFirst\n        ? Buffer.concat([describe(q), Flush])\n        : q.prepare\n          ? q.prepared\n            ? prepared(q)\n            : Buffer.concat([describe(q), prepared(q)])\n          : unnamed(q)\n  }\n\n  function describe(q) {\n    return Buffer.concat([\n      Parse(q.statement.string, q.parameters, q.statement.types, q.statement.name),\n      Describe('S', q.statement.name)\n    ])\n  }\n\n  function prepared(q) {\n    return Buffer.concat([\n      Bind(q.parameters, q.statement.types, q.statement.name, q.cursorName),\n      q.cursorFn\n        ? Execute('', q.cursorRows)\n        : ExecuteUnnamed\n    ])\n  }\n\n  function unnamed(q) {\n    return Buffer.concat([\n      Parse(q.statement.string, q.parameters, q.statement.types),\n      DescribeUnnamed,\n      prepared(q)\n    ])\n  }\n\n  function build(q) {\n    const parameters = []\n        , types = []\n\n    const string = (0,_types_js__WEBPACK_IMPORTED_MODULE_5__.stringify)(q, q.strings[0], q.args[0], parameters, types, options)\n\n    !q.tagged && q.args.forEach(x => (0,_types_js__WEBPACK_IMPORTED_MODULE_5__.handleValue)(x, parameters, types, options))\n\n    q.prepare = options.prepare && ('prepare' in q.options ? q.options.prepare : true)\n    q.string = string\n    q.signature = q.prepare && types + string\n    q.onlyDescribe && (delete statements[q.signature])\n    q.parameters = q.parameters || parameters\n    q.prepared = q.prepare && q.signature in statements\n    q.describeFirst = q.onlyDescribe || (parameters.length && !q.prepared)\n    q.statement = q.prepared\n      ? statements[q.signature]\n      : { string, types, name: q.prepare ? statementId + statementCount++ : '' }\n\n    typeof options.debug === 'function' && options.debug(id, string, parameters, types)\n  }\n\n  function write(x, fn) {\n    chunk = chunk ? Buffer.concat([chunk, x]) : Buffer.from(x)\n    if (fn || chunk.length >= 1024)\n      return nextWrite(fn)\n    nextWriteTimer === null && (nextWriteTimer = setImmediate(nextWrite))\n    return true\n  }\n\n  function nextWrite(fn) {\n    const x = socket.write(chunk, fn)\n    nextWriteTimer !== null && clearImmediate(nextWriteTimer)\n    chunk = nextWriteTimer = null\n    return x\n  }\n\n  function connectTimedOut() {\n    errored(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECT_TIMEOUT', options, socket))\n    socket.destroy()\n  }\n\n  async function secure() {\n    write(SSLRequest)\n    const canSSL = await new Promise(r => socket.once('data', x => r(x[0] === 83))) // S\n\n    if (!canSSL && ssl === 'prefer')\n      return connected()\n\n    socket.removeAllListeners()\n    socket = tls__WEBPACK_IMPORTED_MODULE_1__.connect({\n      socket,\n      servername: net__WEBPACK_IMPORTED_MODULE_0__.isIP(socket.host) ? undefined : socket.host,\n      ...(ssl === 'require' || ssl === 'allow' || ssl === 'prefer'\n        ? { rejectUnauthorized: false }\n        : ssl === 'verify-full'\n          ? {}\n          : typeof ssl === 'object'\n            ? ssl\n            : {}\n      )\n    })\n    socket.on('secureConnect', connected)\n    socket.on('error', error)\n    socket.on('close', closed)\n    socket.on('drain', drain)\n  }\n\n  /* c8 ignore next 3 */\n  function drain() {\n    !query && onopen(connection)\n  }\n\n  function data(x) {\n    if (incomings) {\n      incomings.push(x)\n      remaining -= x.length\n      if (remaining > 0)\n        return\n    }\n\n    incoming = incomings\n      ? Buffer.concat(incomings, length - remaining)\n      : incoming.length === 0\n        ? x\n        : Buffer.concat([incoming, x], incoming.length + x.length)\n\n    while (incoming.length > 4) {\n      length = incoming.readUInt32BE(1)\n      if (length >= incoming.length) {\n        remaining = length - incoming.length\n        incomings = [incoming]\n        break\n      }\n\n      try {\n        handle(incoming.subarray(0, length + 1))\n      } catch (e) {\n        query && (query.cursorFn || query.describeFirst) && write(Sync)\n        errored(e)\n      }\n      incoming = incoming.subarray(length + 1)\n      remaining = 0\n      incomings = null\n    }\n  }\n\n  async function connect() {\n    terminated = false\n    backendParameters = {}\n    socket || (socket = await createSocket())\n\n    if (!socket)\n      return\n\n    connectTimer.start()\n\n    if (options.socket)\n      return ssl ? secure() : connected()\n\n    socket.on('connect', ssl ? secure : connected)\n\n    if (options.path)\n      return socket.connect(options.path)\n\n    socket.ssl = ssl\n    socket.connect(port[hostIndex], host[hostIndex])\n    socket.host = host[hostIndex]\n    socket.port = port[hostIndex]\n\n    hostIndex = (hostIndex + 1) % port.length\n  }\n\n  function reconnect() {\n    setTimeout(connect, closedDate ? closedDate + delay - perf_hooks__WEBPACK_IMPORTED_MODULE_4__.performance.now() : 0)\n  }\n\n  function connected() {\n    try {\n      statements = {}\n      needsTypes = options.fetch_types\n      statementId = Math.random().toString(36).slice(2)\n      statementCount = 1\n      lifeTimer.start()\n      socket.on('data', data)\n      keep_alive && socket.setKeepAlive && socket.setKeepAlive(true, 1000 * keep_alive)\n      const s = StartupMessage()\n      write(s)\n    } catch (err) {\n      error(err)\n    }\n  }\n\n  function error(err) {\n    if (connection.queue === queues.connecting && options.host[retries + 1])\n      return\n\n    errored(err)\n    while (sent.length)\n      queryError(sent.shift(), err)\n  }\n\n  function errored(err) {\n    stream && (stream.destroy(err), stream = null)\n    query && queryError(query, err)\n    initial && (queryError(initial, err), initial = null)\n  }\n\n  function queryError(query, err) {\n    if (query.reserve)\n      return query.reject(err)\n\n    if (!err || typeof err !== 'object')\n      err = new Error(err)\n\n    'query' in err || 'parameters' in err || Object.defineProperties(err, {\n      stack: { value: err.stack + query.origin.replace(/.*\\n/, '\\n'), enumerable: options.debug },\n      query: { value: query.string, enumerable: options.debug },\n      parameters: { value: query.parameters, enumerable: options.debug },\n      args: { value: query.args, enumerable: options.debug },\n      types: { value: query.statement && query.statement.types, enumerable: options.debug }\n    })\n    query.reject(err)\n  }\n\n  function end() {\n    return ending || (\n      !connection.reserved && onend(connection),\n      !connection.reserved && !initial && !query && sent.length === 0\n        ? (terminate(), new Promise(r => socket && socket.readyState !== 'closed' ? socket.once('close', r) : r()))\n        : ending = new Promise(r => ended = r)\n    )\n  }\n\n  function terminate() {\n    terminated = true\n    if (stream || query || initial || sent.length)\n      error(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_DESTROYED', options))\n\n    clearImmediate(nextWriteTimer)\n    if (socket) {\n      socket.removeListener('data', data)\n      socket.removeListener('connect', connected)\n      socket.readyState === 'open' && socket.end((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().X().end())\n    }\n    ended && (ended(), ending = ended = null)\n  }\n\n  async function closed(hadError) {\n    incoming = Buffer.alloc(0)\n    remaining = 0\n    incomings = null\n    clearImmediate(nextWriteTimer)\n    socket.removeListener('data', data)\n    socket.removeListener('connect', connected)\n    idleTimer.cancel()\n    lifeTimer.cancel()\n    connectTimer.cancel()\n\n    socket.removeAllListeners()\n    socket = null\n\n    if (initial)\n      return reconnect()\n\n    !hadError && (query || sent.length) && error(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_CLOSED', options, socket))\n    closedDate = perf_hooks__WEBPACK_IMPORTED_MODULE_4__.performance.now()\n    hadError && options.shared.retries++\n    delay = (typeof backoff === 'function' ? backoff(options.shared.retries) : backoff) * 1000\n    onclose(connection, _errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_CLOSED', options, socket))\n  }\n\n  /* Handlers */\n  function handle(xs, x = xs[0]) {\n    (\n      x === 68 ? DataRow :                   // D\n      x === 100 ? CopyData :                 // d\n      x === 65 ? NotificationResponse :      // A\n      x === 83 ? ParameterStatus :           // S\n      x === 90 ? ReadyForQuery :             // Z\n      x === 67 ? CommandComplete :           // C\n      x === 50 ? BindComplete :              // 2\n      x === 49 ? ParseComplete :             // 1\n      x === 116 ? ParameterDescription :     // t\n      x === 84 ? RowDescription :            // T\n      x === 82 ? Authentication :            // R\n      x === 110 ? NoData :                   // n\n      x === 75 ? BackendKeyData :            // K\n      x === 69 ? ErrorResponse :             // E\n      x === 115 ? PortalSuspended :          // s\n      x === 51 ? CloseComplete :             // 3\n      x === 71 ? CopyInResponse :            // G\n      x === 78 ? NoticeResponse :            // N\n      x === 72 ? CopyOutResponse :           // H\n      x === 99 ? CopyDone :                  // c\n      x === 73 ? EmptyQueryResponse :        // I\n      x === 86 ? FunctionCallResponse :      // V\n      x === 118 ? NegotiateProtocolVersion : // v\n      x === 87 ? CopyBothResponse :          // W\n      /* c8 ignore next */\n      UnknownMessage\n    )(xs)\n  }\n\n  function DataRow(x) {\n    let index = 7\n    let length\n    let column\n    let value\n\n    const row = query.isRaw ? new Array(query.statement.columns.length) : {}\n    for (let i = 0; i < query.statement.columns.length; i++) {\n      column = query.statement.columns[i]\n      length = x.readInt32BE(index)\n      index += 4\n\n      value = length === -1\n        ? null\n        : query.isRaw === true\n          ? x.subarray(index, index += length)\n          : column.parser === undefined\n            ? x.toString('utf8', index, index += length)\n            : column.parser.array === true\n              ? column.parser(x.toString('utf8', index + 1, index += length))\n              : column.parser(x.toString('utf8', index, index += length))\n\n      query.isRaw\n        ? (row[i] = query.isRaw === true\n          ? value\n          : transform.value.from ? transform.value.from(value, column) : value)\n        : (row[column.name] = transform.value.from ? transform.value.from(value, column) : value)\n    }\n\n    query.forEachFn\n      ? query.forEachFn(transform.row.from ? transform.row.from(row) : row, result)\n      : (result[rows++] = transform.row.from ? transform.row.from(row) : row)\n  }\n\n  function ParameterStatus(x) {\n    const [k, v] = x.toString('utf8', 5, x.length - 1).split(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N)\n    backendParameters[k] = v\n    if (options.parameters[k] !== v) {\n      options.parameters[k] = v\n      onparameter && onparameter(k, v)\n    }\n  }\n\n  function ReadyForQuery(x) {\n    query && query.options.simple && query.resolve(results || result)\n    query = results = null\n    result = new _result_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]()\n    connectTimer.cancel()\n\n    if (initial) {\n      if (target_session_attrs) {\n        if (!backendParameters.in_hot_standby || !backendParameters.default_transaction_read_only)\n          return fetchState()\n        else if (tryNext(target_session_attrs, backendParameters))\n          return terminate()\n      }\n\n      if (needsTypes) {\n        initial.reserve && (initial = null)\n        return fetchArrayTypes()\n      }\n\n      initial && !initial.reserve && execute(initial)\n      options.shared.retries = retries = 0\n      initial = null\n      return\n    }\n\n    while (sent.length && (query = sent.shift()) && (query.active = true, query.cancelled))\n      Connection(options).cancel(query.state, query.cancelled.resolve, query.cancelled.reject)\n\n    if (query)\n      return // Consider opening if able and sent.length < 50\n\n    connection.reserved\n      ? !connection.reserved.release && x[5] === 73 // I\n        ? ending\n          ? terminate()\n          : (connection.reserved = null, onopen(connection))\n        : connection.reserved()\n      : ending\n        ? terminate()\n        : onopen(connection)\n  }\n\n  function CommandComplete(x) {\n    rows = 0\n\n    for (let i = x.length - 1; i > 0; i--) {\n      if (x[i] === 32 && x[i + 1] < 58 && result.count === null)\n        result.count = +x.toString('utf8', i + 1, x.length - 1)\n      if (x[i - 1] >= 65) {\n        result.command = x.toString('utf8', 5, i)\n        result.state = backend\n        break\n      }\n    }\n\n    final && (final(), final = null)\n\n    if (result.command === 'BEGIN' && max !== 1 && !connection.reserved)\n      return errored(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.generic('UNSAFE_TRANSACTION', 'Only use sql.begin, sql.reserved or max: 1'))\n\n    if (query.options.simple)\n      return BindComplete()\n\n    if (query.cursorFn) {\n      result.count && query.cursorFn(result)\n      write(Sync)\n    }\n\n    query.resolve(result)\n  }\n\n  function ParseComplete() {\n    query.parsing = false\n  }\n\n  function BindComplete() {\n    !result.statement && (result.statement = query.statement)\n    result.columns = query.statement.columns\n  }\n\n  function ParameterDescription(x) {\n    const length = x.readUInt16BE(5)\n\n    for (let i = 0; i < length; ++i)\n      !query.statement.types[i] && (query.statement.types[i] = x.readUInt32BE(7 + i * 4))\n\n    query.prepare && (statements[query.signature] = query.statement)\n    query.describeFirst && !query.onlyDescribe && (write(prepared(query)), query.describeFirst = false)\n  }\n\n  function RowDescription(x) {\n    if (result.command) {\n      results = results || [result]\n      results.push(result = new _result_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]())\n      result.count = null\n      query.statement.columns = null\n    }\n\n    const length = x.readUInt16BE(5)\n    let index = 7\n    let start\n\n    query.statement.columns = Array(length)\n\n    for (let i = 0; i < length; ++i) {\n      start = index\n      while (x[index++] !== 0);\n      const table = x.readUInt32BE(index)\n      const number = x.readUInt16BE(index + 4)\n      const type = x.readUInt32BE(index + 6)\n      query.statement.columns[i] = {\n        name: transform.column.from\n          ? transform.column.from(x.toString('utf8', start, index - 1))\n          : x.toString('utf8', start, index - 1),\n        parser: parsers[type],\n        table,\n        number,\n        type\n      }\n      index += 18\n    }\n\n    result.statement = query.statement\n    if (query.onlyDescribe)\n      return (query.resolve(query.statement), write(Sync))\n  }\n\n  async function Authentication(x, type = x.readUInt32BE(5)) {\n    (\n      type === 3 ? AuthenticationCleartextPassword :\n      type === 5 ? AuthenticationMD5Password :\n      type === 10 ? SASL :\n      type === 11 ? SASLContinue :\n      type === 12 ? SASLFinal :\n      type !== 0 ? UnknownAuth :\n      noop\n    )(x, type)\n  }\n\n  /* c8 ignore next 5 */\n  async function AuthenticationCleartextPassword() {\n    const payload = await Pass()\n    write(\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().p().str(payload).z(1).end()\n    )\n  }\n\n  async function AuthenticationMD5Password(x) {\n    const payload = 'md5' + (\n      await md5(\n        Buffer.concat([\n          Buffer.from(await md5((await Pass()) + user)),\n          x.subarray(9)\n        ])\n      )\n    )\n    write(\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().p().str(payload).z(1).end()\n    )\n  }\n\n  async function SASL() {\n    nonce = (await crypto__WEBPACK_IMPORTED_MODULE_2__.randomBytes(18)).toString('base64')\n    ;(0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().p().str('SCRAM-SHA-256' + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N)\n    const i = _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i\n    write(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].inc(4).str('n,,n=*,r=' + nonce).i32(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i - i - 4, i).end())\n  }\n\n  async function SASLContinue(x) {\n    const res = x.toString('utf8', 9).split(',').reduce((acc, x) => (acc[x[0]] = x.slice(2), acc), {})\n\n    const saltedPassword = await crypto__WEBPACK_IMPORTED_MODULE_2__.pbkdf2Sync(\n      await Pass(),\n      Buffer.from(res.s, 'base64'),\n      parseInt(res.i), 32,\n      'sha256'\n    )\n\n    const clientKey = await hmac(saltedPassword, 'Client Key')\n\n    const auth = 'n=*,r=' + nonce + ','\n               + 'r=' + res.r + ',s=' + res.s + ',i=' + res.i\n               + ',c=biws,r=' + res.r\n\n    serverSignature = (await hmac(await hmac(saltedPassword, 'Server Key'), auth)).toString('base64')\n\n    const payload = 'c=biws,r=' + res.r + ',p=' + xor(\n      clientKey, Buffer.from(await hmac(await sha256(clientKey), auth))\n    ).toString('base64')\n\n    write(\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().p().str(payload).end()\n    )\n  }\n\n  function SASLFinal(x) {\n    if (x.toString('utf8', 9).split(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N, 1)[0].slice(2) === serverSignature)\n      return\n    /* c8 ignore next 5 */\n    errored(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.generic('SASL_SIGNATURE_MISMATCH', 'The server did not return the correct signature'))\n    socket.destroy()\n  }\n\n  function Pass() {\n    return Promise.resolve(typeof options.pass === 'function'\n      ? options.pass()\n      : options.pass\n    )\n  }\n\n  function NoData() {\n    result.statement = query.statement\n    result.statement.columns = []\n    if (query.onlyDescribe)\n      return (query.resolve(query.statement), write(Sync))\n  }\n\n  function BackendKeyData(x) {\n    backend.pid = x.readUInt32BE(5)\n    backend.secret = x.readUInt32BE(9)\n  }\n\n  async function fetchArrayTypes() {\n    needsTypes = false\n    const types = await new _query_js__WEBPACK_IMPORTED_MODULE_9__.Query([`\n      select b.oid, b.typarray\n      from pg_catalog.pg_type a\n      left join pg_catalog.pg_type b on b.oid = a.typelem\n      where a.typcategory = 'A'\n      group by b.oid, b.typarray\n      order by b.oid\n    `], [], execute)\n    types.forEach(({ oid, typarray }) => addArrayType(oid, typarray))\n  }\n\n  function addArrayType(oid, typarray) {\n    if (!!options.parsers[typarray] && !!options.serializers[typarray]) return\n    const parser = options.parsers[oid]\n    options.shared.typeArrayMap[oid] = typarray\n    options.parsers[typarray] = (xs) => (0,_types_js__WEBPACK_IMPORTED_MODULE_5__.arrayParser)(xs, parser, typarray)\n    options.parsers[typarray].array = true\n    options.serializers[typarray] = (xs) => (0,_types_js__WEBPACK_IMPORTED_MODULE_5__.arraySerializer)(xs, options.serializers[oid], options, typarray)\n  }\n\n  function tryNext(x, xs) {\n    return (\n      (x === 'read-write' && xs.default_transaction_read_only === 'on') ||\n      (x === 'read-only' && xs.default_transaction_read_only === 'off') ||\n      (x === 'primary' && xs.in_hot_standby === 'on') ||\n      (x === 'standby' && xs.in_hot_standby === 'off') ||\n      (x === 'prefer-standby' && xs.in_hot_standby === 'off' && options.host[retries])\n    )\n  }\n\n  function fetchState() {\n    const query = new _query_js__WEBPACK_IMPORTED_MODULE_9__.Query([`\n      show transaction_read_only;\n      select pg_catalog.pg_is_in_recovery()\n    `], [], execute, null, { simple: true })\n    query.resolve = ([[a], [b]]) => {\n      backendParameters.default_transaction_read_only = a.transaction_read_only\n      backendParameters.in_hot_standby = b.pg_is_in_recovery ? 'on' : 'off'\n    }\n    query.execute()\n  }\n\n  function ErrorResponse(x) {\n    query && (query.cursorFn || query.describeFirst) && write(Sync)\n    const error = _errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.postgres(parseError(x))\n    query && query.retried\n      ? errored(query.retried)\n      : query && query.prepared && retryRoutines.has(error.routine)\n        ? retry(query, error)\n        : errored(error)\n  }\n\n  function retry(q, error) {\n    delete statements[q.signature]\n    q.retried = error\n    execute(q)\n  }\n\n  function NotificationResponse(x) {\n    if (!onnotify)\n      return\n\n    let index = 9\n    while (x[index++] !== 0);\n    onnotify(\n      x.toString('utf8', 9, index - 1),\n      x.toString('utf8', index, x.length - 1)\n    )\n  }\n\n  async function PortalSuspended() {\n    try {\n      const x = await Promise.resolve(query.cursorFn(result))\n      rows = 0\n      x === _query_js__WEBPACK_IMPORTED_MODULE_9__.CLOSE\n        ? write(Close(query.portal))\n        : (result = new _result_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"](), write(Execute('', query.cursorRows)))\n    } catch (err) {\n      write(Sync)\n      query.reject(err)\n    }\n  }\n\n  function CloseComplete() {\n    result.count && query.cursorFn(result)\n    query.resolve(result)\n  }\n\n  function CopyInResponse() {\n    stream = new stream__WEBPACK_IMPORTED_MODULE_3__.Writable({\n      autoDestroy: true,\n      write(chunk, encoding, callback) {\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().d().raw(chunk).end(), callback)\n      },\n      destroy(error, callback) {\n        callback(error)\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().f().str(error + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end())\n        stream = null\n      },\n      final(callback) {\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().c().end())\n        final = callback\n      }\n    })\n    query.resolve(stream)\n  }\n\n  function CopyOutResponse() {\n    stream = new stream__WEBPACK_IMPORTED_MODULE_3__.Readable({\n      read() { socket.resume() }\n    })\n    query.resolve(stream)\n  }\n\n  /* c8 ignore next 3 */\n  function CopyBothResponse() {\n    stream = new stream__WEBPACK_IMPORTED_MODULE_3__.Duplex({\n      autoDestroy: true,\n      read() { socket.resume() },\n      /* c8 ignore next 11 */\n      write(chunk, encoding, callback) {\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().d().raw(chunk).end(), callback)\n      },\n      destroy(error, callback) {\n        callback(error)\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().f().str(error + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end())\n        stream = null\n      },\n      final(callback) {\n        socket.write((0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().c().end())\n        final = callback\n      }\n    })\n    query.resolve(stream)\n  }\n\n  function CopyData(x) {\n    stream && (stream.push(x.subarray(5)) || socket.pause())\n  }\n\n  function CopyDone() {\n    stream && stream.push(null)\n    stream = null\n  }\n\n  function NoticeResponse(x) {\n    onnotice\n      ? onnotice(parseError(x))\n      : console.log(parseError(x)) // eslint-disable-line\n\n  }\n\n  /* c8 ignore next 3 */\n  function EmptyQueryResponse() {\n    /* noop */\n  }\n\n  /* c8 ignore next 3 */\n  function FunctionCallResponse() {\n    errored(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.notSupported('FunctionCallResponse'))\n  }\n\n  /* c8 ignore next 3 */\n  function NegotiateProtocolVersion() {\n    errored(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.notSupported('NegotiateProtocolVersion'))\n  }\n\n  /* c8 ignore next 3 */\n  function UnknownMessage(x) {\n    console.error('Postgres.js : Unknown Message:', x[0]) // eslint-disable-line\n  }\n\n  /* c8 ignore next 3 */\n  function UnknownAuth(x, type) {\n    console.error('Postgres.js : Unknown Auth:', type) // eslint-disable-line\n  }\n\n  /* Messages */\n  function Bind(parameters, types, statement = '', portal = '') {\n    let prev\n      , type\n\n    ;(0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().B().str(portal + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).str(statement + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).i16(0).i16(parameters.length)\n\n    parameters.forEach((x, i) => {\n      if (x === null)\n        return _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i32(0xFFFFFFFF)\n\n      type = types[i]\n      parameters[i] = x = type in options.serializers\n        ? options.serializers[type](x)\n        : '' + x\n\n      prev = _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i\n      _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].inc(4).str(x).i32(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i - prev - 4, prev)\n    })\n\n    _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i16(0)\n\n    return _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].end()\n  }\n\n  function Parse(str, parameters, types, name = '') {\n    (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().P().str(name + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).str(str + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).i16(parameters.length)\n    parameters.forEach((x, i) => _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].i32(types[i] || 0))\n    return _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].end()\n  }\n\n  function Describe(x, name = '') {\n    return (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().D().str(x).str(name + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end()\n  }\n\n  function Execute(portal = '', rows = 0) {\n    return Buffer.concat([\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().E().str(portal + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).i32(rows).end(),\n      Flush\n    ])\n  }\n\n  function Close(portal = '') {\n    return Buffer.concat([\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().C().str('P').str(portal + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N).end(),\n      (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().S().end()\n    ])\n  }\n\n  function StartupMessage() {\n    return cancelMessage || (0,_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])().inc(4).i16(3).z(2).str(\n      Object.entries(Object.assign({\n        user,\n        database,\n        client_encoding: 'UTF8'\n      },\n        options.connection\n      )).filter(([, v]) => v).map(([k, v]) => k + _bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N + v).join(_bytes_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"].N)\n    ).z(2).end(0)\n  }\n\n}\n\nfunction parseError(x) {\n  const error = {}\n  let start = 5\n  for (let i = 5; i < x.length - 1; i++) {\n    if (x[i] === 0) {\n      error[errorFields[x[start]]] = x.toString('utf8', start + 1, i)\n      start = i + 1\n    }\n  }\n  return error\n}\n\nfunction md5(x) {\n  return crypto__WEBPACK_IMPORTED_MODULE_2__.createHash('md5').update(x).digest('hex')\n}\n\nfunction hmac(key, x) {\n  return crypto__WEBPACK_IMPORTED_MODULE_2__.createHmac('sha256', key).update(x).digest()\n}\n\nfunction sha256(x) {\n  return crypto__WEBPACK_IMPORTED_MODULE_2__.createHash('sha256').update(x).digest()\n}\n\nfunction xor(a, b) {\n  const length = Math.max(a.length, b.length)\n  const buffer = Buffer.allocUnsafe(length)\n  for (let i = 0; i < length; i++)\n    buffer[i] = a[i] ^ b[i]\n  return buffer\n}\n\nfunction timer(fn, seconds) {\n  seconds = typeof seconds === 'function' ? seconds() : seconds\n  if (!seconds)\n    return { cancel: noop, start: noop }\n\n  let timer\n  return {\n    cancel() {\n      timer && (clearTimeout(timer), timer = null)\n    },\n    start() {\n      timer && clearTimeout(timer)\n      timer = setTimeout(done, seconds * 1000, arguments)\n    }\n  }\n\n  function done(args) {\n    fn.apply(null, args)\n    timer = null\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcG9zdGdyZXMvc3JjL2Nvbm5lY3Rpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXFCO0FBQ0E7QUFDTTtBQUNBO0FBQ2E7O0FBRXlDO0FBQzdDO0FBQ0o7QUFDRjtBQUNXO0FBQ2Y7O0FBRTFCLGlFQUFlLFVBQVU7O0FBRXpCOztBQUVBLGFBQWEsc0RBQUM7QUFDZCxjQUFjLHNEQUFDO0FBQ2YsbUJBQW1CLHNEQUFDO0FBQ3BCLHNDQUFzQyxzREFBQyxXQUFXLGtEQUFDO0FBQ25ELHdCQUF3QixzREFBQyxvQkFBb0Isa0RBQUM7QUFDOUMscUJBQXFCOztBQUVyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsd0NBQXdDLElBQUksOENBQThDLElBQUk7QUFDOUY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJOztBQUVKLGVBQWUscURBQUs7QUFDcEI7QUFDQSxvQkFBb0I7QUFDcEI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxtQkFBbUIsa0RBQU07QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsdUNBQVU7QUFDeEIsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsMEJBQTBCLGFBQWE7QUFDdkM7QUFDQSxzQkFBc0Isc0RBQUM7QUFDdkI7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsMkJBQTJCLDhDQUFNOztBQUVqQztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFlBQVksOENBQU07O0FBRWxCO0FBQ0EsUUFBUSxzREFBQyxnQ0FBZ0Msa0RBQUM7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsbUJBQW1CLG9EQUFTOztBQUU1QixxQ0FBcUMsc0RBQVc7O0FBRWhEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7O0FBRVY7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZLDhDQUFNO0FBQ2xCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxhQUFhLHdDQUFXO0FBQ3hCO0FBQ0Esa0JBQWtCLHFDQUFRO0FBQzFCO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSwwREFBMEQsbURBQVc7QUFDckU7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxlQUFlLGtGQUFrRjtBQUNqRyxlQUFlLGdEQUFnRDtBQUMvRCxvQkFBb0Isb0RBQW9EO0FBQ3hFLGNBQWMsOENBQThDO0FBQzVELGVBQWU7QUFDZixLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFlBQVksOENBQU07O0FBRWxCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQWlELHNEQUFDO0FBQ2xEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsaURBQWlELDhDQUFNO0FBQ3ZELGlCQUFpQixtREFBVztBQUM1QjtBQUNBO0FBQ0Esd0JBQXdCLDhDQUFNO0FBQzlCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxvQkFBb0Isb0NBQW9DO0FBQ3hEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsNkRBQTZELGtEQUFDO0FBQzlEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsa0RBQU07QUFDdkI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSwrQkFBK0IsT0FBTztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EscUJBQXFCLDhDQUFNOztBQUUzQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsb0JBQW9CLFlBQVk7QUFDaEM7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxrREFBTTtBQUN0QztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBLG9CQUFvQixZQUFZO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLHNEQUFDO0FBQ1A7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sc0RBQUM7QUFDUDtBQUNBOztBQUVBO0FBQ0EsbUJBQW1CLCtDQUFrQjtBQUNyQyxJQUFJLHVEQUFDLDZCQUE2QixrREFBQztBQUNuQyxjQUFjLGtEQUFDO0FBQ2YsVUFBVSxrREFBQyxxQ0FBcUMsa0RBQUM7QUFDakQ7O0FBRUE7QUFDQSxxR0FBcUc7O0FBRXJHLGlDQUFpQyw4Q0FBaUI7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsTUFBTSxzREFBQztBQUNQO0FBQ0E7O0FBRUE7QUFDQSxvQ0FBb0Msa0RBQUM7QUFDckM7QUFDQTtBQUNBLFlBQVksOENBQU07QUFDbEI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSw0QkFBNEIsNENBQUs7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsZUFBZTtBQUNwQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdDQUF3QyxzREFBVztBQUNuRDtBQUNBLDRDQUE0QywwREFBZTtBQUMzRDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxzQkFBc0IsNENBQUs7QUFDM0I7QUFDQTtBQUNBLDZCQUE2QixjQUFjO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esa0JBQWtCLDhDQUFNO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksNENBQUs7QUFDakI7QUFDQSx3QkFBd0Isa0RBQU07QUFDOUIsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsaUJBQWlCLDRDQUFlO0FBQ2hDO0FBQ0E7QUFDQSxxQkFBcUIsc0RBQUM7QUFDdEIsT0FBTztBQUNQO0FBQ0E7QUFDQSxxQkFBcUIsc0RBQUMsbUJBQW1CLGtEQUFDO0FBQzFDO0FBQ0EsT0FBTztBQUNQO0FBQ0EscUJBQXFCLHNEQUFDO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBLGlCQUFpQiw0Q0FBZTtBQUNoQyxlQUFlO0FBQ2YsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGlCQUFpQiwwQ0FBYTtBQUM5QjtBQUNBLGVBQWUsaUJBQWlCO0FBQ2hDO0FBQ0E7QUFDQSxxQkFBcUIsc0RBQUM7QUFDdEIsT0FBTztBQUNQO0FBQ0E7QUFDQSxxQkFBcUIsc0RBQUMsbUJBQW1CLGtEQUFDO0FBQzFDO0FBQ0EsT0FBTztBQUNQO0FBQ0EscUJBQXFCLHNEQUFDO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFlBQVksOENBQU07QUFDbEI7O0FBRUE7QUFDQTtBQUNBLFlBQVksOENBQU07QUFDbEI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsSUFBSSx1REFBQyxvQkFBb0Isa0RBQUMsb0JBQW9CLGtEQUFDOztBQUUvQztBQUNBO0FBQ0EsZUFBZSxrREFBQzs7QUFFaEI7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsYUFBYSxrREFBQztBQUNkLE1BQU0sa0RBQUMsbUJBQW1CLGtEQUFDO0FBQzNCLEtBQUs7O0FBRUwsSUFBSSxrREFBQzs7QUFFTCxXQUFXLGtEQUFDO0FBQ1o7O0FBRUE7QUFDQSxJQUFJLHNEQUFDLGtCQUFrQixrREFBQyxjQUFjLGtEQUFDO0FBQ3ZDLGlDQUFpQyxrREFBQztBQUNsQyxXQUFXLGtEQUFDO0FBQ1o7O0FBRUE7QUFDQSxXQUFXLHNEQUFDLHlCQUF5QixrREFBQztBQUN0Qzs7QUFFQTtBQUNBO0FBQ0EsTUFBTSxzREFBQyxvQkFBb0Isa0RBQUM7QUFDNUI7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxNQUFNLHNEQUFDLDZCQUE2QixrREFBQztBQUNyQyxNQUFNLHNEQUFDO0FBQ1A7QUFDQTs7QUFFQTtBQUNBLDRCQUE0QixzREFBQztBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLGtEQUFrRCxrREFBQyxhQUFhLGtEQUFDO0FBQ2pFO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLGtCQUFrQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFNBQVMsOENBQWlCO0FBQzFCOztBQUVBO0FBQ0EsU0FBUyw4Q0FBaUI7QUFDMUI7O0FBRUE7QUFDQSxTQUFTLDhDQUFpQjtBQUMxQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsWUFBWTtBQUM5QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsYUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxUTEQgU3lzdGVtXFxlbmhhbmNlZC1pZWx0cy1zeXN0ZW1cXG5vZGVfbW9kdWxlc1xccG9zdGdyZXNcXHNyY1xcY29ubmVjdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbmV0IGZyb20gJ25ldCdcbmltcG9ydCB0bHMgZnJvbSAndGxzJ1xuaW1wb3J0IGNyeXB0byBmcm9tICdjcnlwdG8nXG5pbXBvcnQgU3RyZWFtIGZyb20gJ3N0cmVhbSdcbmltcG9ydCB7IHBlcmZvcm1hbmNlIH0gZnJvbSAncGVyZl9ob29rcydcblxuaW1wb3J0IHsgc3RyaW5naWZ5LCBoYW5kbGVWYWx1ZSwgYXJyYXlQYXJzZXIsIGFycmF5U2VyaWFsaXplciB9IGZyb20gJy4vdHlwZXMuanMnXG5pbXBvcnQgeyBFcnJvcnMgfSBmcm9tICcuL2Vycm9ycy5qcydcbmltcG9ydCBSZXN1bHQgZnJvbSAnLi9yZXN1bHQuanMnXG5pbXBvcnQgUXVldWUgZnJvbSAnLi9xdWV1ZS5qcydcbmltcG9ydCB7IFF1ZXJ5LCBDTE9TRSB9IGZyb20gJy4vcXVlcnkuanMnXG5pbXBvcnQgYiBmcm9tICcuL2J5dGVzLmpzJ1xuXG5leHBvcnQgZGVmYXVsdCBDb25uZWN0aW9uXG5cbmxldCB1aWQgPSAxXG5cbmNvbnN0IFN5bmMgPSBiKCkuUygpLmVuZCgpXG4gICAgLCBGbHVzaCA9IGIoKS5IKCkuZW5kKClcbiAgICAsIFNTTFJlcXVlc3QgPSBiKCkuaTMyKDgpLmkzMig4MDg3NzEwMykuZW5kKDgpXG4gICAgLCBFeGVjdXRlVW5uYW1lZCA9IEJ1ZmZlci5jb25jYXQoW2IoKS5FKCkuc3RyKGIuTikuaTMyKDApLmVuZCgpLCBTeW5jXSlcbiAgICAsIERlc2NyaWJlVW5uYW1lZCA9IGIoKS5EKCkuc3RyKCdTJykuc3RyKGIuTikuZW5kKClcbiAgICAsIG5vb3AgPSAoKSA9PiB7IC8qIG5vb3AgKi8gfVxuXG5jb25zdCByZXRyeVJvdXRpbmVzID0gbmV3IFNldChbXG4gICdGZXRjaFByZXBhcmVkU3RhdGVtZW50JyxcbiAgJ1JldmFsaWRhdGVDYWNoZWRRdWVyeScsXG4gICd0cmFuc2Zvcm1Bc3NpZ25lZEV4cHInXG5dKVxuXG5jb25zdCBlcnJvckZpZWxkcyA9IHtcbiAgODMgIDogJ3NldmVyaXR5X2xvY2FsJywgICAgLy8gU1xuICA4NiAgOiAnc2V2ZXJpdHknLCAgICAgICAgICAvLyBWXG4gIDY3ICA6ICdjb2RlJywgICAgICAgICAgICAgIC8vIENcbiAgNzcgIDogJ21lc3NhZ2UnLCAgICAgICAgICAgLy8gTVxuICA2OCAgOiAnZGV0YWlsJywgICAgICAgICAgICAvLyBEXG4gIDcyICA6ICdoaW50JywgICAgICAgICAgICAgIC8vIEhcbiAgODAgIDogJ3Bvc2l0aW9uJywgICAgICAgICAgLy8gUFxuICAxMTIgOiAnaW50ZXJuYWxfcG9zaXRpb24nLCAvLyBwXG4gIDExMyA6ICdpbnRlcm5hbF9xdWVyeScsICAgIC8vIHFcbiAgODcgIDogJ3doZXJlJywgICAgICAgICAgICAgLy8gV1xuICAxMTUgOiAnc2NoZW1hX25hbWUnLCAgICAgICAvLyBzXG4gIDExNiA6ICd0YWJsZV9uYW1lJywgICAgICAgIC8vIHRcbiAgOTkgIDogJ2NvbHVtbl9uYW1lJywgICAgICAgLy8gY1xuICAxMDAgOiAnZGF0YSB0eXBlX25hbWUnLCAgICAvLyBkXG4gIDExMCA6ICdjb25zdHJhaW50X25hbWUnLCAgIC8vIG5cbiAgNzAgIDogJ2ZpbGUnLCAgICAgICAgICAgICAgLy8gRlxuICA3NiAgOiAnbGluZScsICAgICAgICAgICAgICAvLyBMXG4gIDgyICA6ICdyb3V0aW5lJyAgICAgICAgICAgIC8vIFJcbn1cblxuZnVuY3Rpb24gQ29ubmVjdGlvbihvcHRpb25zLCBxdWV1ZXMgPSB7fSwgeyBvbm9wZW4gPSBub29wLCBvbmVuZCA9IG5vb3AsIG9uY2xvc2UgPSBub29wIH0gPSB7fSkge1xuICBjb25zdCB7XG4gICAgc3NsLFxuICAgIG1heCxcbiAgICB1c2VyLFxuICAgIGhvc3QsXG4gICAgcG9ydCxcbiAgICBkYXRhYmFzZSxcbiAgICBwYXJzZXJzLFxuICAgIHRyYW5zZm9ybSxcbiAgICBvbm5vdGljZSxcbiAgICBvbm5vdGlmeSxcbiAgICBvbnBhcmFtZXRlcixcbiAgICBtYXhfcGlwZWxpbmUsXG4gICAga2VlcF9hbGl2ZSxcbiAgICBiYWNrb2ZmLFxuICAgIHRhcmdldF9zZXNzaW9uX2F0dHJzXG4gIH0gPSBvcHRpb25zXG5cbiAgY29uc3Qgc2VudCA9IFF1ZXVlKClcbiAgICAgICwgaWQgPSB1aWQrK1xuICAgICAgLCBiYWNrZW5kID0geyBwaWQ6IG51bGwsIHNlY3JldDogbnVsbCB9XG4gICAgICAsIGlkbGVUaW1lciA9IHRpbWVyKGVuZCwgb3B0aW9ucy5pZGxlX3RpbWVvdXQpXG4gICAgICAsIGxpZmVUaW1lciA9IHRpbWVyKGVuZCwgb3B0aW9ucy5tYXhfbGlmZXRpbWUpXG4gICAgICAsIGNvbm5lY3RUaW1lciA9IHRpbWVyKGNvbm5lY3RUaW1lZE91dCwgb3B0aW9ucy5jb25uZWN0X3RpbWVvdXQpXG5cbiAgbGV0IHNvY2tldCA9IG51bGxcbiAgICAsIGNhbmNlbE1lc3NhZ2VcbiAgICAsIHJlc3VsdCA9IG5ldyBSZXN1bHQoKVxuICAgICwgaW5jb21pbmcgPSBCdWZmZXIuYWxsb2MoMClcbiAgICAsIG5lZWRzVHlwZXMgPSBvcHRpb25zLmZldGNoX3R5cGVzXG4gICAgLCBiYWNrZW5kUGFyYW1ldGVycyA9IHt9XG4gICAgLCBzdGF0ZW1lbnRzID0ge31cbiAgICAsIHN0YXRlbWVudElkID0gTWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc2xpY2UoMilcbiAgICAsIHN0YXRlbWVudENvdW50ID0gMVxuICAgICwgY2xvc2VkRGF0ZSA9IDBcbiAgICAsIHJlbWFpbmluZyA9IDBcbiAgICAsIGhvc3RJbmRleCA9IDBcbiAgICAsIHJldHJpZXMgPSAwXG4gICAgLCBsZW5ndGggPSAwXG4gICAgLCBkZWxheSA9IDBcbiAgICAsIHJvd3MgPSAwXG4gICAgLCBzZXJ2ZXJTaWduYXR1cmUgPSBudWxsXG4gICAgLCBuZXh0V3JpdGVUaW1lciA9IG51bGxcbiAgICAsIHRlcm1pbmF0ZWQgPSBmYWxzZVxuICAgICwgaW5jb21pbmdzID0gbnVsbFxuICAgICwgcmVzdWx0cyA9IG51bGxcbiAgICAsIGluaXRpYWwgPSBudWxsXG4gICAgLCBlbmRpbmcgPSBudWxsXG4gICAgLCBzdHJlYW0gPSBudWxsXG4gICAgLCBjaHVuayA9IG51bGxcbiAgICAsIGVuZGVkID0gbnVsbFxuICAgICwgbm9uY2UgPSBudWxsXG4gICAgLCBxdWVyeSA9IG51bGxcbiAgICAsIGZpbmFsID0gbnVsbFxuXG4gIGNvbnN0IGNvbm5lY3Rpb24gPSB7XG4gICAgcXVldWU6IHF1ZXVlcy5jbG9zZWQsXG4gICAgaWRsZVRpbWVyLFxuICAgIGNvbm5lY3QocXVlcnkpIHtcbiAgICAgIGluaXRpYWwgPSBxdWVyeVxuICAgICAgcmVjb25uZWN0KClcbiAgICB9LFxuICAgIHRlcm1pbmF0ZSxcbiAgICBleGVjdXRlLFxuICAgIGNhbmNlbCxcbiAgICBlbmQsXG4gICAgY291bnQ6IDAsXG4gICAgaWRcbiAgfVxuXG4gIHF1ZXVlcy5jbG9zZWQgJiYgcXVldWVzLmNsb3NlZC5wdXNoKGNvbm5lY3Rpb24pXG5cbiAgcmV0dXJuIGNvbm5lY3Rpb25cblxuICBhc3luYyBmdW5jdGlvbiBjcmVhdGVTb2NrZXQoKSB7XG4gICAgbGV0IHhcbiAgICB0cnkge1xuICAgICAgeCA9IG9wdGlvbnMuc29ja2V0XG4gICAgICAgID8gKGF3YWl0IFByb21pc2UucmVzb2x2ZShvcHRpb25zLnNvY2tldChvcHRpb25zKSkpXG4gICAgICAgIDogbmV3IG5ldC5Tb2NrZXQoKVxuICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgIGVycm9yKGUpXG4gICAgICByZXR1cm5cbiAgICB9XG4gICAgeC5vbignZXJyb3InLCBlcnJvcilcbiAgICB4Lm9uKCdjbG9zZScsIGNsb3NlZClcbiAgICB4Lm9uKCdkcmFpbicsIGRyYWluKVxuICAgIHJldHVybiB4XG4gIH1cblxuICBhc3luYyBmdW5jdGlvbiBjYW5jZWwoeyBwaWQsIHNlY3JldCB9LCByZXNvbHZlLCByZWplY3QpIHtcbiAgICB0cnkge1xuICAgICAgY2FuY2VsTWVzc2FnZSA9IGIoKS5pMzIoMTYpLmkzMig4MDg3NzEwMikuaTMyKHBpZCkuaTMyKHNlY3JldCkuZW5kKDE2KVxuICAgICAgYXdhaXQgY29ubmVjdCgpXG4gICAgICBzb2NrZXQub25jZSgnZXJyb3InLCByZWplY3QpXG4gICAgICBzb2NrZXQub25jZSgnY2xvc2UnLCByZXNvbHZlKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZWplY3QoZXJyb3IpXG4gICAgfVxuICB9XG5cbiAgZnVuY3Rpb24gZXhlY3V0ZShxKSB7XG4gICAgaWYgKHRlcm1pbmF0ZWQpXG4gICAgICByZXR1cm4gcXVlcnlFcnJvcihxLCBFcnJvcnMuY29ubmVjdGlvbignQ09OTkVDVElPTl9ERVNUUk9ZRUQnLCBvcHRpb25zKSlcblxuICAgIGlmIChxLmNhbmNlbGxlZClcbiAgICAgIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIHEuc3RhdGUgPSBiYWNrZW5kXG4gICAgICBxdWVyeVxuICAgICAgICA/IHNlbnQucHVzaChxKVxuICAgICAgICA6IChxdWVyeSA9IHEsIHF1ZXJ5LmFjdGl2ZSA9IHRydWUpXG5cbiAgICAgIGJ1aWxkKHEpXG4gICAgICByZXR1cm4gd3JpdGUodG9CdWZmZXIocSkpXG4gICAgICAgICYmICFxLmRlc2NyaWJlRmlyc3RcbiAgICAgICAgJiYgIXEuY3Vyc29yRm5cbiAgICAgICAgJiYgc2VudC5sZW5ndGggPCBtYXhfcGlwZWxpbmVcbiAgICAgICAgJiYgKCFxLm9wdGlvbnMub25leGVjdXRlIHx8IHEub3B0aW9ucy5vbmV4ZWN1dGUoY29ubmVjdGlvbikpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNlbnQubGVuZ3RoID09PSAwICYmIHdyaXRlKFN5bmMpXG4gICAgICBlcnJvcmVkKGVycm9yKVxuICAgICAgcmV0dXJuIHRydWVcbiAgICB9XG4gIH1cblxuICBmdW5jdGlvbiB0b0J1ZmZlcihxKSB7XG4gICAgaWYgKHEucGFyYW1ldGVycy5sZW5ndGggPj0gNjU1MzQpXG4gICAgICB0aHJvdyBFcnJvcnMuZ2VuZXJpYygnTUFYX1BBUkFNRVRFUlNfRVhDRUVERUQnLCAnTWF4IG51bWJlciBvZiBwYXJhbWV0ZXJzICg2NTUzNCkgZXhjZWVkZWQnKVxuXG4gICAgcmV0dXJuIHEub3B0aW9ucy5zaW1wbGVcbiAgICAgID8gYigpLlEoKS5zdHIocS5zdGF0ZW1lbnQuc3RyaW5nICsgYi5OKS5lbmQoKVxuICAgICAgOiBxLmRlc2NyaWJlRmlyc3RcbiAgICAgICAgPyBCdWZmZXIuY29uY2F0KFtkZXNjcmliZShxKSwgRmx1c2hdKVxuICAgICAgICA6IHEucHJlcGFyZVxuICAgICAgICAgID8gcS5wcmVwYXJlZFxuICAgICAgICAgICAgPyBwcmVwYXJlZChxKVxuICAgICAgICAgICAgOiBCdWZmZXIuY29uY2F0KFtkZXNjcmliZShxKSwgcHJlcGFyZWQocSldKVxuICAgICAgICAgIDogdW5uYW1lZChxKVxuICB9XG5cbiAgZnVuY3Rpb24gZGVzY3JpYmUocSkge1xuICAgIHJldHVybiBCdWZmZXIuY29uY2F0KFtcbiAgICAgIFBhcnNlKHEuc3RhdGVtZW50LnN0cmluZywgcS5wYXJhbWV0ZXJzLCBxLnN0YXRlbWVudC50eXBlcywgcS5zdGF0ZW1lbnQubmFtZSksXG4gICAgICBEZXNjcmliZSgnUycsIHEuc3RhdGVtZW50Lm5hbWUpXG4gICAgXSlcbiAgfVxuXG4gIGZ1bmN0aW9uIHByZXBhcmVkKHEpIHtcbiAgICByZXR1cm4gQnVmZmVyLmNvbmNhdChbXG4gICAgICBCaW5kKHEucGFyYW1ldGVycywgcS5zdGF0ZW1lbnQudHlwZXMsIHEuc3RhdGVtZW50Lm5hbWUsIHEuY3Vyc29yTmFtZSksXG4gICAgICBxLmN1cnNvckZuXG4gICAgICAgID8gRXhlY3V0ZSgnJywgcS5jdXJzb3JSb3dzKVxuICAgICAgICA6IEV4ZWN1dGVVbm5hbWVkXG4gICAgXSlcbiAgfVxuXG4gIGZ1bmN0aW9uIHVubmFtZWQocSkge1xuICAgIHJldHVybiBCdWZmZXIuY29uY2F0KFtcbiAgICAgIFBhcnNlKHEuc3RhdGVtZW50LnN0cmluZywgcS5wYXJhbWV0ZXJzLCBxLnN0YXRlbWVudC50eXBlcyksXG4gICAgICBEZXNjcmliZVVubmFtZWQsXG4gICAgICBwcmVwYXJlZChxKVxuICAgIF0pXG4gIH1cblxuICBmdW5jdGlvbiBidWlsZChxKSB7XG4gICAgY29uc3QgcGFyYW1ldGVycyA9IFtdXG4gICAgICAgICwgdHlwZXMgPSBbXVxuXG4gICAgY29uc3Qgc3RyaW5nID0gc3RyaW5naWZ5KHEsIHEuc3RyaW5nc1swXSwgcS5hcmdzWzBdLCBwYXJhbWV0ZXJzLCB0eXBlcywgb3B0aW9ucylcblxuICAgICFxLnRhZ2dlZCAmJiBxLmFyZ3MuZm9yRWFjaCh4ID0+IGhhbmRsZVZhbHVlKHgsIHBhcmFtZXRlcnMsIHR5cGVzLCBvcHRpb25zKSlcblxuICAgIHEucHJlcGFyZSA9IG9wdGlvbnMucHJlcGFyZSAmJiAoJ3ByZXBhcmUnIGluIHEub3B0aW9ucyA/IHEub3B0aW9ucy5wcmVwYXJlIDogdHJ1ZSlcbiAgICBxLnN0cmluZyA9IHN0cmluZ1xuICAgIHEuc2lnbmF0dXJlID0gcS5wcmVwYXJlICYmIHR5cGVzICsgc3RyaW5nXG4gICAgcS5vbmx5RGVzY3JpYmUgJiYgKGRlbGV0ZSBzdGF0ZW1lbnRzW3Euc2lnbmF0dXJlXSlcbiAgICBxLnBhcmFtZXRlcnMgPSBxLnBhcmFtZXRlcnMgfHwgcGFyYW1ldGVyc1xuICAgIHEucHJlcGFyZWQgPSBxLnByZXBhcmUgJiYgcS5zaWduYXR1cmUgaW4gc3RhdGVtZW50c1xuICAgIHEuZGVzY3JpYmVGaXJzdCA9IHEub25seURlc2NyaWJlIHx8IChwYXJhbWV0ZXJzLmxlbmd0aCAmJiAhcS5wcmVwYXJlZClcbiAgICBxLnN0YXRlbWVudCA9IHEucHJlcGFyZWRcbiAgICAgID8gc3RhdGVtZW50c1txLnNpZ25hdHVyZV1cbiAgICAgIDogeyBzdHJpbmcsIHR5cGVzLCBuYW1lOiBxLnByZXBhcmUgPyBzdGF0ZW1lbnRJZCArIHN0YXRlbWVudENvdW50KysgOiAnJyB9XG5cbiAgICB0eXBlb2Ygb3B0aW9ucy5kZWJ1ZyA9PT0gJ2Z1bmN0aW9uJyAmJiBvcHRpb25zLmRlYnVnKGlkLCBzdHJpbmcsIHBhcmFtZXRlcnMsIHR5cGVzKVxuICB9XG5cbiAgZnVuY3Rpb24gd3JpdGUoeCwgZm4pIHtcbiAgICBjaHVuayA9IGNodW5rID8gQnVmZmVyLmNvbmNhdChbY2h1bmssIHhdKSA6IEJ1ZmZlci5mcm9tKHgpXG4gICAgaWYgKGZuIHx8IGNodW5rLmxlbmd0aCA+PSAxMDI0KVxuICAgICAgcmV0dXJuIG5leHRXcml0ZShmbilcbiAgICBuZXh0V3JpdGVUaW1lciA9PT0gbnVsbCAmJiAobmV4dFdyaXRlVGltZXIgPSBzZXRJbW1lZGlhdGUobmV4dFdyaXRlKSlcbiAgICByZXR1cm4gdHJ1ZVxuICB9XG5cbiAgZnVuY3Rpb24gbmV4dFdyaXRlKGZuKSB7XG4gICAgY29uc3QgeCA9IHNvY2tldC53cml0ZShjaHVuaywgZm4pXG4gICAgbmV4dFdyaXRlVGltZXIgIT09IG51bGwgJiYgY2xlYXJJbW1lZGlhdGUobmV4dFdyaXRlVGltZXIpXG4gICAgY2h1bmsgPSBuZXh0V3JpdGVUaW1lciA9IG51bGxcbiAgICByZXR1cm4geFxuICB9XG5cbiAgZnVuY3Rpb24gY29ubmVjdFRpbWVkT3V0KCkge1xuICAgIGVycm9yZWQoRXJyb3JzLmNvbm5lY3Rpb24oJ0NPTk5FQ1RfVElNRU9VVCcsIG9wdGlvbnMsIHNvY2tldCkpXG4gICAgc29ja2V0LmRlc3Ryb3koKVxuICB9XG5cbiAgYXN5bmMgZnVuY3Rpb24gc2VjdXJlKCkge1xuICAgIHdyaXRlKFNTTFJlcXVlc3QpXG4gICAgY29uc3QgY2FuU1NMID0gYXdhaXQgbmV3IFByb21pc2UociA9PiBzb2NrZXQub25jZSgnZGF0YScsIHggPT4gcih4WzBdID09PSA4MykpKSAvLyBTXG5cbiAgICBpZiAoIWNhblNTTCAmJiBzc2wgPT09ICdwcmVmZXInKVxuICAgICAgcmV0dXJuIGNvbm5lY3RlZCgpXG5cbiAgICBzb2NrZXQucmVtb3ZlQWxsTGlzdGVuZXJzKClcbiAgICBzb2NrZXQgPSB0bHMuY29ubmVjdCh7XG4gICAgICBzb2NrZXQsXG4gICAgICBzZXJ2ZXJuYW1lOiBuZXQuaXNJUChzb2NrZXQuaG9zdCkgPyB1bmRlZmluZWQgOiBzb2NrZXQuaG9zdCxcbiAgICAgIC4uLihzc2wgPT09ICdyZXF1aXJlJyB8fCBzc2wgPT09ICdhbGxvdycgfHwgc3NsID09PSAncHJlZmVyJ1xuICAgICAgICA/IHsgcmVqZWN0VW5hdXRob3JpemVkOiBmYWxzZSB9XG4gICAgICAgIDogc3NsID09PSAndmVyaWZ5LWZ1bGwnXG4gICAgICAgICAgPyB7fVxuICAgICAgICAgIDogdHlwZW9mIHNzbCA9PT0gJ29iamVjdCdcbiAgICAgICAgICAgID8gc3NsXG4gICAgICAgICAgICA6IHt9XG4gICAgICApXG4gICAgfSlcbiAgICBzb2NrZXQub24oJ3NlY3VyZUNvbm5lY3QnLCBjb25uZWN0ZWQpXG4gICAgc29ja2V0Lm9uKCdlcnJvcicsIGVycm9yKVxuICAgIHNvY2tldC5vbignY2xvc2UnLCBjbG9zZWQpXG4gICAgc29ja2V0Lm9uKCdkcmFpbicsIGRyYWluKVxuICB9XG5cbiAgLyogYzggaWdub3JlIG5leHQgMyAqL1xuICBmdW5jdGlvbiBkcmFpbigpIHtcbiAgICAhcXVlcnkgJiYgb25vcGVuKGNvbm5lY3Rpb24pXG4gIH1cblxuICBmdW5jdGlvbiBkYXRhKHgpIHtcbiAgICBpZiAoaW5jb21pbmdzKSB7XG4gICAgICBpbmNvbWluZ3MucHVzaCh4KVxuICAgICAgcmVtYWluaW5nIC09IHgubGVuZ3RoXG4gICAgICBpZiAocmVtYWluaW5nID4gMClcbiAgICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgaW5jb21pbmcgPSBpbmNvbWluZ3NcbiAgICAgID8gQnVmZmVyLmNvbmNhdChpbmNvbWluZ3MsIGxlbmd0aCAtIHJlbWFpbmluZylcbiAgICAgIDogaW5jb21pbmcubGVuZ3RoID09PSAwXG4gICAgICAgID8geFxuICAgICAgICA6IEJ1ZmZlci5jb25jYXQoW2luY29taW5nLCB4XSwgaW5jb21pbmcubGVuZ3RoICsgeC5sZW5ndGgpXG5cbiAgICB3aGlsZSAoaW5jb21pbmcubGVuZ3RoID4gNCkge1xuICAgICAgbGVuZ3RoID0gaW5jb21pbmcucmVhZFVJbnQzMkJFKDEpXG4gICAgICBpZiAobGVuZ3RoID49IGluY29taW5nLmxlbmd0aCkge1xuICAgICAgICByZW1haW5pbmcgPSBsZW5ndGggLSBpbmNvbWluZy5sZW5ndGhcbiAgICAgICAgaW5jb21pbmdzID0gW2luY29taW5nXVxuICAgICAgICBicmVha1xuICAgICAgfVxuXG4gICAgICB0cnkge1xuICAgICAgICBoYW5kbGUoaW5jb21pbmcuc3ViYXJyYXkoMCwgbGVuZ3RoICsgMSkpXG4gICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgIHF1ZXJ5ICYmIChxdWVyeS5jdXJzb3JGbiB8fCBxdWVyeS5kZXNjcmliZUZpcnN0KSAmJiB3cml0ZShTeW5jKVxuICAgICAgICBlcnJvcmVkKGUpXG4gICAgICB9XG4gICAgICBpbmNvbWluZyA9IGluY29taW5nLnN1YmFycmF5KGxlbmd0aCArIDEpXG4gICAgICByZW1haW5pbmcgPSAwXG4gICAgICBpbmNvbWluZ3MgPSBudWxsXG4gICAgfVxuICB9XG5cbiAgYXN5bmMgZnVuY3Rpb24gY29ubmVjdCgpIHtcbiAgICB0ZXJtaW5hdGVkID0gZmFsc2VcbiAgICBiYWNrZW5kUGFyYW1ldGVycyA9IHt9XG4gICAgc29ja2V0IHx8IChzb2NrZXQgPSBhd2FpdCBjcmVhdGVTb2NrZXQoKSlcblxuICAgIGlmICghc29ja2V0KVxuICAgICAgcmV0dXJuXG5cbiAgICBjb25uZWN0VGltZXIuc3RhcnQoKVxuXG4gICAgaWYgKG9wdGlvbnMuc29ja2V0KVxuICAgICAgcmV0dXJuIHNzbCA/IHNlY3VyZSgpIDogY29ubmVjdGVkKClcblxuICAgIHNvY2tldC5vbignY29ubmVjdCcsIHNzbCA/IHNlY3VyZSA6IGNvbm5lY3RlZClcblxuICAgIGlmIChvcHRpb25zLnBhdGgpXG4gICAgICByZXR1cm4gc29ja2V0LmNvbm5lY3Qob3B0aW9ucy5wYXRoKVxuXG4gICAgc29ja2V0LnNzbCA9IHNzbFxuICAgIHNvY2tldC5jb25uZWN0KHBvcnRbaG9zdEluZGV4XSwgaG9zdFtob3N0SW5kZXhdKVxuICAgIHNvY2tldC5ob3N0ID0gaG9zdFtob3N0SW5kZXhdXG4gICAgc29ja2V0LnBvcnQgPSBwb3J0W2hvc3RJbmRleF1cblxuICAgIGhvc3RJbmRleCA9IChob3N0SW5kZXggKyAxKSAlIHBvcnQubGVuZ3RoXG4gIH1cblxuICBmdW5jdGlvbiByZWNvbm5lY3QoKSB7XG4gICAgc2V0VGltZW91dChjb25uZWN0LCBjbG9zZWREYXRlID8gY2xvc2VkRGF0ZSArIGRlbGF5IC0gcGVyZm9ybWFuY2Uubm93KCkgOiAwKVxuICB9XG5cbiAgZnVuY3Rpb24gY29ubmVjdGVkKCkge1xuICAgIHRyeSB7XG4gICAgICBzdGF0ZW1lbnRzID0ge31cbiAgICAgIG5lZWRzVHlwZXMgPSBvcHRpb25zLmZldGNoX3R5cGVzXG4gICAgICBzdGF0ZW1lbnRJZCA9IE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnNsaWNlKDIpXG4gICAgICBzdGF0ZW1lbnRDb3VudCA9IDFcbiAgICAgIGxpZmVUaW1lci5zdGFydCgpXG4gICAgICBzb2NrZXQub24oJ2RhdGEnLCBkYXRhKVxuICAgICAga2VlcF9hbGl2ZSAmJiBzb2NrZXQuc2V0S2VlcEFsaXZlICYmIHNvY2tldC5zZXRLZWVwQWxpdmUodHJ1ZSwgMTAwMCAqIGtlZXBfYWxpdmUpXG4gICAgICBjb25zdCBzID0gU3RhcnR1cE1lc3NhZ2UoKVxuICAgICAgd3JpdGUocylcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGVycm9yKGVycilcbiAgICB9XG4gIH1cblxuICBmdW5jdGlvbiBlcnJvcihlcnIpIHtcbiAgICBpZiAoY29ubmVjdGlvbi5xdWV1ZSA9PT0gcXVldWVzLmNvbm5lY3RpbmcgJiYgb3B0aW9ucy5ob3N0W3JldHJpZXMgKyAxXSlcbiAgICAgIHJldHVyblxuXG4gICAgZXJyb3JlZChlcnIpXG4gICAgd2hpbGUgKHNlbnQubGVuZ3RoKVxuICAgICAgcXVlcnlFcnJvcihzZW50LnNoaWZ0KCksIGVycilcbiAgfVxuXG4gIGZ1bmN0aW9uIGVycm9yZWQoZXJyKSB7XG4gICAgc3RyZWFtICYmIChzdHJlYW0uZGVzdHJveShlcnIpLCBzdHJlYW0gPSBudWxsKVxuICAgIHF1ZXJ5ICYmIHF1ZXJ5RXJyb3IocXVlcnksIGVycilcbiAgICBpbml0aWFsICYmIChxdWVyeUVycm9yKGluaXRpYWwsIGVyciksIGluaXRpYWwgPSBudWxsKVxuICB9XG5cbiAgZnVuY3Rpb24gcXVlcnlFcnJvcihxdWVyeSwgZXJyKSB7XG4gICAgaWYgKHF1ZXJ5LnJlc2VydmUpXG4gICAgICByZXR1cm4gcXVlcnkucmVqZWN0KGVycilcblxuICAgIGlmICghZXJyIHx8IHR5cGVvZiBlcnIgIT09ICdvYmplY3QnKVxuICAgICAgZXJyID0gbmV3IEVycm9yKGVycilcblxuICAgICdxdWVyeScgaW4gZXJyIHx8ICdwYXJhbWV0ZXJzJyBpbiBlcnIgfHwgT2JqZWN0LmRlZmluZVByb3BlcnRpZXMoZXJyLCB7XG4gICAgICBzdGFjazogeyB2YWx1ZTogZXJyLnN0YWNrICsgcXVlcnkub3JpZ2luLnJlcGxhY2UoLy4qXFxuLywgJ1xcbicpLCBlbnVtZXJhYmxlOiBvcHRpb25zLmRlYnVnIH0sXG4gICAgICBxdWVyeTogeyB2YWx1ZTogcXVlcnkuc3RyaW5nLCBlbnVtZXJhYmxlOiBvcHRpb25zLmRlYnVnIH0sXG4gICAgICBwYXJhbWV0ZXJzOiB7IHZhbHVlOiBxdWVyeS5wYXJhbWV0ZXJzLCBlbnVtZXJhYmxlOiBvcHRpb25zLmRlYnVnIH0sXG4gICAgICBhcmdzOiB7IHZhbHVlOiBxdWVyeS5hcmdzLCBlbnVtZXJhYmxlOiBvcHRpb25zLmRlYnVnIH0sXG4gICAgICB0eXBlczogeyB2YWx1ZTogcXVlcnkuc3RhdGVtZW50ICYmIHF1ZXJ5LnN0YXRlbWVudC50eXBlcywgZW51bWVyYWJsZTogb3B0aW9ucy5kZWJ1ZyB9XG4gICAgfSlcbiAgICBxdWVyeS5yZWplY3QoZXJyKVxuICB9XG5cbiAgZnVuY3Rpb24gZW5kKCkge1xuICAgIHJldHVybiBlbmRpbmcgfHwgKFxuICAgICAgIWNvbm5lY3Rpb24ucmVzZXJ2ZWQgJiYgb25lbmQoY29ubmVjdGlvbiksXG4gICAgICAhY29ubmVjdGlvbi5yZXNlcnZlZCAmJiAhaW5pdGlhbCAmJiAhcXVlcnkgJiYgc2VudC5sZW5ndGggPT09IDBcbiAgICAgICAgPyAodGVybWluYXRlKCksIG5ldyBQcm9taXNlKHIgPT4gc29ja2V0ICYmIHNvY2tldC5yZWFkeVN0YXRlICE9PSAnY2xvc2VkJyA/IHNvY2tldC5vbmNlKCdjbG9zZScsIHIpIDogcigpKSlcbiAgICAgICAgOiBlbmRpbmcgPSBuZXcgUHJvbWlzZShyID0+IGVuZGVkID0gcilcbiAgICApXG4gIH1cblxuICBmdW5jdGlvbiB0ZXJtaW5hdGUoKSB7XG4gICAgdGVybWluYXRlZCA9IHRydWVcbiAgICBpZiAoc3RyZWFtIHx8IHF1ZXJ5IHx8IGluaXRpYWwgfHwgc2VudC5sZW5ndGgpXG4gICAgICBlcnJvcihFcnJvcnMuY29ubmVjdGlvbignQ09OTkVDVElPTl9ERVNUUk9ZRUQnLCBvcHRpb25zKSlcblxuICAgIGNsZWFySW1tZWRpYXRlKG5leHRXcml0ZVRpbWVyKVxuICAgIGlmIChzb2NrZXQpIHtcbiAgICAgIHNvY2tldC5yZW1vdmVMaXN0ZW5lcignZGF0YScsIGRhdGEpXG4gICAgICBzb2NrZXQucmVtb3ZlTGlzdGVuZXIoJ2Nvbm5lY3QnLCBjb25uZWN0ZWQpXG4gICAgICBzb2NrZXQucmVhZHlTdGF0ZSA9PT0gJ29wZW4nICYmIHNvY2tldC5lbmQoYigpLlgoKS5lbmQoKSlcbiAgICB9XG4gICAgZW5kZWQgJiYgKGVuZGVkKCksIGVuZGluZyA9IGVuZGVkID0gbnVsbClcbiAgfVxuXG4gIGFzeW5jIGZ1bmN0aW9uIGNsb3NlZChoYWRFcnJvcikge1xuICAgIGluY29taW5nID0gQnVmZmVyLmFsbG9jKDApXG4gICAgcmVtYWluaW5nID0gMFxuICAgIGluY29taW5ncyA9IG51bGxcbiAgICBjbGVhckltbWVkaWF0ZShuZXh0V3JpdGVUaW1lcilcbiAgICBzb2NrZXQucmVtb3ZlTGlzdGVuZXIoJ2RhdGEnLCBkYXRhKVxuICAgIHNvY2tldC5yZW1vdmVMaXN0ZW5lcignY29ubmVjdCcsIGNvbm5lY3RlZClcbiAgICBpZGxlVGltZXIuY2FuY2VsKClcbiAgICBsaWZlVGltZXIuY2FuY2VsKClcbiAgICBjb25uZWN0VGltZXIuY2FuY2VsKClcblxuICAgIHNvY2tldC5yZW1vdmVBbGxMaXN0ZW5lcnMoKVxuICAgIHNvY2tldCA9IG51bGxcblxuICAgIGlmIChpbml0aWFsKVxuICAgICAgcmV0dXJuIHJlY29ubmVjdCgpXG5cbiAgICAhaGFkRXJyb3IgJiYgKHF1ZXJ5IHx8IHNlbnQubGVuZ3RoKSAmJiBlcnJvcihFcnJvcnMuY29ubmVjdGlvbignQ09OTkVDVElPTl9DTE9TRUQnLCBvcHRpb25zLCBzb2NrZXQpKVxuICAgIGNsb3NlZERhdGUgPSBwZXJmb3JtYW5jZS5ub3coKVxuICAgIGhhZEVycm9yICYmIG9wdGlvbnMuc2hhcmVkLnJldHJpZXMrK1xuICAgIGRlbGF5ID0gKHR5cGVvZiBiYWNrb2ZmID09PSAnZnVuY3Rpb24nID8gYmFja29mZihvcHRpb25zLnNoYXJlZC5yZXRyaWVzKSA6IGJhY2tvZmYpICogMTAwMFxuICAgIG9uY2xvc2UoY29ubmVjdGlvbiwgRXJyb3JzLmNvbm5lY3Rpb24oJ0NPTk5FQ1RJT05fQ0xPU0VEJywgb3B0aW9ucywgc29ja2V0KSlcbiAgfVxuXG4gIC8qIEhhbmRsZXJzICovXG4gIGZ1bmN0aW9uIGhhbmRsZSh4cywgeCA9IHhzWzBdKSB7XG4gICAgKFxuICAgICAgeCA9PT0gNjggPyBEYXRhUm93IDogICAgICAgICAgICAgICAgICAgLy8gRFxuICAgICAgeCA9PT0gMTAwID8gQ29weURhdGEgOiAgICAgICAgICAgICAgICAgLy8gZFxuICAgICAgeCA9PT0gNjUgPyBOb3RpZmljYXRpb25SZXNwb25zZSA6ICAgICAgLy8gQVxuICAgICAgeCA9PT0gODMgPyBQYXJhbWV0ZXJTdGF0dXMgOiAgICAgICAgICAgLy8gU1xuICAgICAgeCA9PT0gOTAgPyBSZWFkeUZvclF1ZXJ5IDogICAgICAgICAgICAgLy8gWlxuICAgICAgeCA9PT0gNjcgPyBDb21tYW5kQ29tcGxldGUgOiAgICAgICAgICAgLy8gQ1xuICAgICAgeCA9PT0gNTAgPyBCaW5kQ29tcGxldGUgOiAgICAgICAgICAgICAgLy8gMlxuICAgICAgeCA9PT0gNDkgPyBQYXJzZUNvbXBsZXRlIDogICAgICAgICAgICAgLy8gMVxuICAgICAgeCA9PT0gMTE2ID8gUGFyYW1ldGVyRGVzY3JpcHRpb24gOiAgICAgLy8gdFxuICAgICAgeCA9PT0gODQgPyBSb3dEZXNjcmlwdGlvbiA6ICAgICAgICAgICAgLy8gVFxuICAgICAgeCA9PT0gODIgPyBBdXRoZW50aWNhdGlvbiA6ICAgICAgICAgICAgLy8gUlxuICAgICAgeCA9PT0gMTEwID8gTm9EYXRhIDogICAgICAgICAgICAgICAgICAgLy8gblxuICAgICAgeCA9PT0gNzUgPyBCYWNrZW5kS2V5RGF0YSA6ICAgICAgICAgICAgLy8gS1xuICAgICAgeCA9PT0gNjkgPyBFcnJvclJlc3BvbnNlIDogICAgICAgICAgICAgLy8gRVxuICAgICAgeCA9PT0gMTE1ID8gUG9ydGFsU3VzcGVuZGVkIDogICAgICAgICAgLy8gc1xuICAgICAgeCA9PT0gNTEgPyBDbG9zZUNvbXBsZXRlIDogICAgICAgICAgICAgLy8gM1xuICAgICAgeCA9PT0gNzEgPyBDb3B5SW5SZXNwb25zZSA6ICAgICAgICAgICAgLy8gR1xuICAgICAgeCA9PT0gNzggPyBOb3RpY2VSZXNwb25zZSA6ICAgICAgICAgICAgLy8gTlxuICAgICAgeCA9PT0gNzIgPyBDb3B5T3V0UmVzcG9uc2UgOiAgICAgICAgICAgLy8gSFxuICAgICAgeCA9PT0gOTkgPyBDb3B5RG9uZSA6ICAgICAgICAgICAgICAgICAgLy8gY1xuICAgICAgeCA9PT0gNzMgPyBFbXB0eVF1ZXJ5UmVzcG9uc2UgOiAgICAgICAgLy8gSVxuICAgICAgeCA9PT0gODYgPyBGdW5jdGlvbkNhbGxSZXNwb25zZSA6ICAgICAgLy8gVlxuICAgICAgeCA9PT0gMTE4ID8gTmVnb3RpYXRlUHJvdG9jb2xWZXJzaW9uIDogLy8gdlxuICAgICAgeCA9PT0gODcgPyBDb3B5Qm90aFJlc3BvbnNlIDogICAgICAgICAgLy8gV1xuICAgICAgLyogYzggaWdub3JlIG5leHQgKi9cbiAgICAgIFVua25vd25NZXNzYWdlXG4gICAgKSh4cylcbiAgfVxuXG4gIGZ1bmN0aW9uIERhdGFSb3coeCkge1xuICAgIGxldCBpbmRleCA9IDdcbiAgICBsZXQgbGVuZ3RoXG4gICAgbGV0IGNvbHVtblxuICAgIGxldCB2YWx1ZVxuXG4gICAgY29uc3Qgcm93ID0gcXVlcnkuaXNSYXcgPyBuZXcgQXJyYXkocXVlcnkuc3RhdGVtZW50LmNvbHVtbnMubGVuZ3RoKSA6IHt9XG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBxdWVyeS5zdGF0ZW1lbnQuY29sdW1ucy5sZW5ndGg7IGkrKykge1xuICAgICAgY29sdW1uID0gcXVlcnkuc3RhdGVtZW50LmNvbHVtbnNbaV1cbiAgICAgIGxlbmd0aCA9IHgucmVhZEludDMyQkUoaW5kZXgpXG4gICAgICBpbmRleCArPSA0XG5cbiAgICAgIHZhbHVlID0gbGVuZ3RoID09PSAtMVxuICAgICAgICA/IG51bGxcbiAgICAgICAgOiBxdWVyeS5pc1JhdyA9PT0gdHJ1ZVxuICAgICAgICAgID8geC5zdWJhcnJheShpbmRleCwgaW5kZXggKz0gbGVuZ3RoKVxuICAgICAgICAgIDogY29sdW1uLnBhcnNlciA9PT0gdW5kZWZpbmVkXG4gICAgICAgICAgICA/IHgudG9TdHJpbmcoJ3V0ZjgnLCBpbmRleCwgaW5kZXggKz0gbGVuZ3RoKVxuICAgICAgICAgICAgOiBjb2x1bW4ucGFyc2VyLmFycmF5ID09PSB0cnVlXG4gICAgICAgICAgICAgID8gY29sdW1uLnBhcnNlcih4LnRvU3RyaW5nKCd1dGY4JywgaW5kZXggKyAxLCBpbmRleCArPSBsZW5ndGgpKVxuICAgICAgICAgICAgICA6IGNvbHVtbi5wYXJzZXIoeC50b1N0cmluZygndXRmOCcsIGluZGV4LCBpbmRleCArPSBsZW5ndGgpKVxuXG4gICAgICBxdWVyeS5pc1Jhd1xuICAgICAgICA/IChyb3dbaV0gPSBxdWVyeS5pc1JhdyA9PT0gdHJ1ZVxuICAgICAgICAgID8gdmFsdWVcbiAgICAgICAgICA6IHRyYW5zZm9ybS52YWx1ZS5mcm9tID8gdHJhbnNmb3JtLnZhbHVlLmZyb20odmFsdWUsIGNvbHVtbikgOiB2YWx1ZSlcbiAgICAgICAgOiAocm93W2NvbHVtbi5uYW1lXSA9IHRyYW5zZm9ybS52YWx1ZS5mcm9tID8gdHJhbnNmb3JtLnZhbHVlLmZyb20odmFsdWUsIGNvbHVtbikgOiB2YWx1ZSlcbiAgICB9XG5cbiAgICBxdWVyeS5mb3JFYWNoRm5cbiAgICAgID8gcXVlcnkuZm9yRWFjaEZuKHRyYW5zZm9ybS5yb3cuZnJvbSA/IHRyYW5zZm9ybS5yb3cuZnJvbShyb3cpIDogcm93LCByZXN1bHQpXG4gICAgICA6IChyZXN1bHRbcm93cysrXSA9IHRyYW5zZm9ybS5yb3cuZnJvbSA/IHRyYW5zZm9ybS5yb3cuZnJvbShyb3cpIDogcm93KVxuICB9XG5cbiAgZnVuY3Rpb24gUGFyYW1ldGVyU3RhdHVzKHgpIHtcbiAgICBjb25zdCBbaywgdl0gPSB4LnRvU3RyaW5nKCd1dGY4JywgNSwgeC5sZW5ndGggLSAxKS5zcGxpdChiLk4pXG4gICAgYmFja2VuZFBhcmFtZXRlcnNba10gPSB2XG4gICAgaWYgKG9wdGlvbnMucGFyYW1ldGVyc1trXSAhPT0gdikge1xuICAgICAgb3B0aW9ucy5wYXJhbWV0ZXJzW2tdID0gdlxuICAgICAgb25wYXJhbWV0ZXIgJiYgb25wYXJhbWV0ZXIoaywgdilcbiAgICB9XG4gIH1cblxuICBmdW5jdGlvbiBSZWFkeUZvclF1ZXJ5KHgpIHtcbiAgICBxdWVyeSAmJiBxdWVyeS5vcHRpb25zLnNpbXBsZSAmJiBxdWVyeS5yZXNvbHZlKHJlc3VsdHMgfHwgcmVzdWx0KVxuICAgIHF1ZXJ5ID0gcmVzdWx0cyA9IG51bGxcbiAgICByZXN1bHQgPSBuZXcgUmVzdWx0KClcbiAgICBjb25uZWN0VGltZXIuY2FuY2VsKClcblxuICAgIGlmIChpbml0aWFsKSB7XG4gICAgICBpZiAodGFyZ2V0X3Nlc3Npb25fYXR0cnMpIHtcbiAgICAgICAgaWYgKCFiYWNrZW5kUGFyYW1ldGVycy5pbl9ob3Rfc3RhbmRieSB8fCAhYmFja2VuZFBhcmFtZXRlcnMuZGVmYXVsdF90cmFuc2FjdGlvbl9yZWFkX29ubHkpXG4gICAgICAgICAgcmV0dXJuIGZldGNoU3RhdGUoKVxuICAgICAgICBlbHNlIGlmICh0cnlOZXh0KHRhcmdldF9zZXNzaW9uX2F0dHJzLCBiYWNrZW5kUGFyYW1ldGVycykpXG4gICAgICAgICAgcmV0dXJuIHRlcm1pbmF0ZSgpXG4gICAgICB9XG5cbiAgICAgIGlmIChuZWVkc1R5cGVzKSB7XG4gICAgICAgIGluaXRpYWwucmVzZXJ2ZSAmJiAoaW5pdGlhbCA9IG51bGwpXG4gICAgICAgIHJldHVybiBmZXRjaEFycmF5VHlwZXMoKVxuICAgICAgfVxuXG4gICAgICBpbml0aWFsICYmICFpbml0aWFsLnJlc2VydmUgJiYgZXhlY3V0ZShpbml0aWFsKVxuICAgICAgb3B0aW9ucy5zaGFyZWQucmV0cmllcyA9IHJldHJpZXMgPSAwXG4gICAgICBpbml0aWFsID0gbnVsbFxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgd2hpbGUgKHNlbnQubGVuZ3RoICYmIChxdWVyeSA9IHNlbnQuc2hpZnQoKSkgJiYgKHF1ZXJ5LmFjdGl2ZSA9IHRydWUsIHF1ZXJ5LmNhbmNlbGxlZCkpXG4gICAgICBDb25uZWN0aW9uKG9wdGlvbnMpLmNhbmNlbChxdWVyeS5zdGF0ZSwgcXVlcnkuY2FuY2VsbGVkLnJlc29sdmUsIHF1ZXJ5LmNhbmNlbGxlZC5yZWplY3QpXG5cbiAgICBpZiAocXVlcnkpXG4gICAgICByZXR1cm4gLy8gQ29uc2lkZXIgb3BlbmluZyBpZiBhYmxlIGFuZCBzZW50Lmxlbmd0aCA8IDUwXG5cbiAgICBjb25uZWN0aW9uLnJlc2VydmVkXG4gICAgICA/ICFjb25uZWN0aW9uLnJlc2VydmVkLnJlbGVhc2UgJiYgeFs1XSA9PT0gNzMgLy8gSVxuICAgICAgICA/IGVuZGluZ1xuICAgICAgICAgID8gdGVybWluYXRlKClcbiAgICAgICAgICA6IChjb25uZWN0aW9uLnJlc2VydmVkID0gbnVsbCwgb25vcGVuKGNvbm5lY3Rpb24pKVxuICAgICAgICA6IGNvbm5lY3Rpb24ucmVzZXJ2ZWQoKVxuICAgICAgOiBlbmRpbmdcbiAgICAgICAgPyB0ZXJtaW5hdGUoKVxuICAgICAgICA6IG9ub3Blbihjb25uZWN0aW9uKVxuICB9XG5cbiAgZnVuY3Rpb24gQ29tbWFuZENvbXBsZXRlKHgpIHtcbiAgICByb3dzID0gMFxuXG4gICAgZm9yIChsZXQgaSA9IHgubGVuZ3RoIC0gMTsgaSA+IDA7IGktLSkge1xuICAgICAgaWYgKHhbaV0gPT09IDMyICYmIHhbaSArIDFdIDwgNTggJiYgcmVzdWx0LmNvdW50ID09PSBudWxsKVxuICAgICAgICByZXN1bHQuY291bnQgPSAreC50b1N0cmluZygndXRmOCcsIGkgKyAxLCB4Lmxlbmd0aCAtIDEpXG4gICAgICBpZiAoeFtpIC0gMV0gPj0gNjUpIHtcbiAgICAgICAgcmVzdWx0LmNvbW1hbmQgPSB4LnRvU3RyaW5nKCd1dGY4JywgNSwgaSlcbiAgICAgICAgcmVzdWx0LnN0YXRlID0gYmFja2VuZFxuICAgICAgICBicmVha1xuICAgICAgfVxuICAgIH1cblxuICAgIGZpbmFsICYmIChmaW5hbCgpLCBmaW5hbCA9IG51bGwpXG5cbiAgICBpZiAocmVzdWx0LmNvbW1hbmQgPT09ICdCRUdJTicgJiYgbWF4ICE9PSAxICYmICFjb25uZWN0aW9uLnJlc2VydmVkKVxuICAgICAgcmV0dXJuIGVycm9yZWQoRXJyb3JzLmdlbmVyaWMoJ1VOU0FGRV9UUkFOU0FDVElPTicsICdPbmx5IHVzZSBzcWwuYmVnaW4sIHNxbC5yZXNlcnZlZCBvciBtYXg6IDEnKSlcblxuICAgIGlmIChxdWVyeS5vcHRpb25zLnNpbXBsZSlcbiAgICAgIHJldHVybiBCaW5kQ29tcGxldGUoKVxuXG4gICAgaWYgKHF1ZXJ5LmN1cnNvckZuKSB7XG4gICAgICByZXN1bHQuY291bnQgJiYgcXVlcnkuY3Vyc29yRm4ocmVzdWx0KVxuICAgICAgd3JpdGUoU3luYylcbiAgICB9XG5cbiAgICBxdWVyeS5yZXNvbHZlKHJlc3VsdClcbiAgfVxuXG4gIGZ1bmN0aW9uIFBhcnNlQ29tcGxldGUoKSB7XG4gICAgcXVlcnkucGFyc2luZyA9IGZhbHNlXG4gIH1cblxuICBmdW5jdGlvbiBCaW5kQ29tcGxldGUoKSB7XG4gICAgIXJlc3VsdC5zdGF0ZW1lbnQgJiYgKHJlc3VsdC5zdGF0ZW1lbnQgPSBxdWVyeS5zdGF0ZW1lbnQpXG4gICAgcmVzdWx0LmNvbHVtbnMgPSBxdWVyeS5zdGF0ZW1lbnQuY29sdW1uc1xuICB9XG5cbiAgZnVuY3Rpb24gUGFyYW1ldGVyRGVzY3JpcHRpb24oeCkge1xuICAgIGNvbnN0IGxlbmd0aCA9IHgucmVhZFVJbnQxNkJFKDUpXG5cbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbmd0aDsgKytpKVxuICAgICAgIXF1ZXJ5LnN0YXRlbWVudC50eXBlc1tpXSAmJiAocXVlcnkuc3RhdGVtZW50LnR5cGVzW2ldID0geC5yZWFkVUludDMyQkUoNyArIGkgKiA0KSlcblxuICAgIHF1ZXJ5LnByZXBhcmUgJiYgKHN0YXRlbWVudHNbcXVlcnkuc2lnbmF0dXJlXSA9IHF1ZXJ5LnN0YXRlbWVudClcbiAgICBxdWVyeS5kZXNjcmliZUZpcnN0ICYmICFxdWVyeS5vbmx5RGVzY3JpYmUgJiYgKHdyaXRlKHByZXBhcmVkKHF1ZXJ5KSksIHF1ZXJ5LmRlc2NyaWJlRmlyc3QgPSBmYWxzZSlcbiAgfVxuXG4gIGZ1bmN0aW9uIFJvd0Rlc2NyaXB0aW9uKHgpIHtcbiAgICBpZiAocmVzdWx0LmNvbW1hbmQpIHtcbiAgICAgIHJlc3VsdHMgPSByZXN1bHRzIHx8IFtyZXN1bHRdXG4gICAgICByZXN1bHRzLnB1c2gocmVzdWx0ID0gbmV3IFJlc3VsdCgpKVxuICAgICAgcmVzdWx0LmNvdW50ID0gbnVsbFxuICAgICAgcXVlcnkuc3RhdGVtZW50LmNvbHVtbnMgPSBudWxsXG4gICAgfVxuXG4gICAgY29uc3QgbGVuZ3RoID0geC5yZWFkVUludDE2QkUoNSlcbiAgICBsZXQgaW5kZXggPSA3XG4gICAgbGV0IHN0YXJ0XG5cbiAgICBxdWVyeS5zdGF0ZW1lbnQuY29sdW1ucyA9IEFycmF5KGxlbmd0aClcblxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbGVuZ3RoOyArK2kpIHtcbiAgICAgIHN0YXJ0ID0gaW5kZXhcbiAgICAgIHdoaWxlICh4W2luZGV4KytdICE9PSAwKTtcbiAgICAgIGNvbnN0IHRhYmxlID0geC5yZWFkVUludDMyQkUoaW5kZXgpXG4gICAgICBjb25zdCBudW1iZXIgPSB4LnJlYWRVSW50MTZCRShpbmRleCArIDQpXG4gICAgICBjb25zdCB0eXBlID0geC5yZWFkVUludDMyQkUoaW5kZXggKyA2KVxuICAgICAgcXVlcnkuc3RhdGVtZW50LmNvbHVtbnNbaV0gPSB7XG4gICAgICAgIG5hbWU6IHRyYW5zZm9ybS5jb2x1bW4uZnJvbVxuICAgICAgICAgID8gdHJhbnNmb3JtLmNvbHVtbi5mcm9tKHgudG9TdHJpbmcoJ3V0ZjgnLCBzdGFydCwgaW5kZXggLSAxKSlcbiAgICAgICAgICA6IHgudG9TdHJpbmcoJ3V0ZjgnLCBzdGFydCwgaW5kZXggLSAxKSxcbiAgICAgICAgcGFyc2VyOiBwYXJzZXJzW3R5cGVdLFxuICAgICAgICB0YWJsZSxcbiAgICAgICAgbnVtYmVyLFxuICAgICAgICB0eXBlXG4gICAgICB9XG4gICAgICBpbmRleCArPSAxOFxuICAgIH1cblxuICAgIHJlc3VsdC5zdGF0ZW1lbnQgPSBxdWVyeS5zdGF0ZW1lbnRcbiAgICBpZiAocXVlcnkub25seURlc2NyaWJlKVxuICAgICAgcmV0dXJuIChxdWVyeS5yZXNvbHZlKHF1ZXJ5LnN0YXRlbWVudCksIHdyaXRlKFN5bmMpKVxuICB9XG5cbiAgYXN5bmMgZnVuY3Rpb24gQXV0aGVudGljYXRpb24oeCwgdHlwZSA9IHgucmVhZFVJbnQzMkJFKDUpKSB7XG4gICAgKFxuICAgICAgdHlwZSA9PT0gMyA/IEF1dGhlbnRpY2F0aW9uQ2xlYXJ0ZXh0UGFzc3dvcmQgOlxuICAgICAgdHlwZSA9PT0gNSA/IEF1dGhlbnRpY2F0aW9uTUQ1UGFzc3dvcmQgOlxuICAgICAgdHlwZSA9PT0gMTAgPyBTQVNMIDpcbiAgICAgIHR5cGUgPT09IDExID8gU0FTTENvbnRpbnVlIDpcbiAgICAgIHR5cGUgPT09IDEyID8gU0FTTEZpbmFsIDpcbiAgICAgIHR5cGUgIT09IDAgPyBVbmtub3duQXV0aCA6XG4gICAgICBub29wXG4gICAgKSh4LCB0eXBlKVxuICB9XG5cbiAgLyogYzggaWdub3JlIG5leHQgNSAqL1xuICBhc3luYyBmdW5jdGlvbiBBdXRoZW50aWNhdGlvbkNsZWFydGV4dFBhc3N3b3JkKCkge1xuICAgIGNvbnN0IHBheWxvYWQgPSBhd2FpdCBQYXNzKClcbiAgICB3cml0ZShcbiAgICAgIGIoKS5wKCkuc3RyKHBheWxvYWQpLnooMSkuZW5kKClcbiAgICApXG4gIH1cblxuICBhc3luYyBmdW5jdGlvbiBBdXRoZW50aWNhdGlvbk1ENVBhc3N3b3JkKHgpIHtcbiAgICBjb25zdCBwYXlsb2FkID0gJ21kNScgKyAoXG4gICAgICBhd2FpdCBtZDUoXG4gICAgICAgIEJ1ZmZlci5jb25jYXQoW1xuICAgICAgICAgIEJ1ZmZlci5mcm9tKGF3YWl0IG1kNSgoYXdhaXQgUGFzcygpKSArIHVzZXIpKSxcbiAgICAgICAgICB4LnN1YmFycmF5KDkpXG4gICAgICAgIF0pXG4gICAgICApXG4gICAgKVxuICAgIHdyaXRlKFxuICAgICAgYigpLnAoKS5zdHIocGF5bG9hZCkueigxKS5lbmQoKVxuICAgIClcbiAgfVxuXG4gIGFzeW5jIGZ1bmN0aW9uIFNBU0woKSB7XG4gICAgbm9uY2UgPSAoYXdhaXQgY3J5cHRvLnJhbmRvbUJ5dGVzKDE4KSkudG9TdHJpbmcoJ2Jhc2U2NCcpXG4gICAgYigpLnAoKS5zdHIoJ1NDUkFNLVNIQS0yNTYnICsgYi5OKVxuICAgIGNvbnN0IGkgPSBiLmlcbiAgICB3cml0ZShiLmluYyg0KS5zdHIoJ24sLG49KixyPScgKyBub25jZSkuaTMyKGIuaSAtIGkgLSA0LCBpKS5lbmQoKSlcbiAgfVxuXG4gIGFzeW5jIGZ1bmN0aW9uIFNBU0xDb250aW51ZSh4KSB7XG4gICAgY29uc3QgcmVzID0geC50b1N0cmluZygndXRmOCcsIDkpLnNwbGl0KCcsJykucmVkdWNlKChhY2MsIHgpID0+IChhY2NbeFswXV0gPSB4LnNsaWNlKDIpLCBhY2MpLCB7fSlcblxuICAgIGNvbnN0IHNhbHRlZFBhc3N3b3JkID0gYXdhaXQgY3J5cHRvLnBia2RmMlN5bmMoXG4gICAgICBhd2FpdCBQYXNzKCksXG4gICAgICBCdWZmZXIuZnJvbShyZXMucywgJ2Jhc2U2NCcpLFxuICAgICAgcGFyc2VJbnQocmVzLmkpLCAzMixcbiAgICAgICdzaGEyNTYnXG4gICAgKVxuXG4gICAgY29uc3QgY2xpZW50S2V5ID0gYXdhaXQgaG1hYyhzYWx0ZWRQYXNzd29yZCwgJ0NsaWVudCBLZXknKVxuXG4gICAgY29uc3QgYXV0aCA9ICduPSoscj0nICsgbm9uY2UgKyAnLCdcbiAgICAgICAgICAgICAgICsgJ3I9JyArIHJlcy5yICsgJyxzPScgKyByZXMucyArICcsaT0nICsgcmVzLmlcbiAgICAgICAgICAgICAgICsgJyxjPWJpd3Mscj0nICsgcmVzLnJcblxuICAgIHNlcnZlclNpZ25hdHVyZSA9IChhd2FpdCBobWFjKGF3YWl0IGhtYWMoc2FsdGVkUGFzc3dvcmQsICdTZXJ2ZXIgS2V5JyksIGF1dGgpKS50b1N0cmluZygnYmFzZTY0JylcblxuICAgIGNvbnN0IHBheWxvYWQgPSAnYz1iaXdzLHI9JyArIHJlcy5yICsgJyxwPScgKyB4b3IoXG4gICAgICBjbGllbnRLZXksIEJ1ZmZlci5mcm9tKGF3YWl0IGhtYWMoYXdhaXQgc2hhMjU2KGNsaWVudEtleSksIGF1dGgpKVxuICAgICkudG9TdHJpbmcoJ2Jhc2U2NCcpXG5cbiAgICB3cml0ZShcbiAgICAgIGIoKS5wKCkuc3RyKHBheWxvYWQpLmVuZCgpXG4gICAgKVxuICB9XG5cbiAgZnVuY3Rpb24gU0FTTEZpbmFsKHgpIHtcbiAgICBpZiAoeC50b1N0cmluZygndXRmOCcsIDkpLnNwbGl0KGIuTiwgMSlbMF0uc2xpY2UoMikgPT09IHNlcnZlclNpZ25hdHVyZSlcbiAgICAgIHJldHVyblxuICAgIC8qIGM4IGlnbm9yZSBuZXh0IDUgKi9cbiAgICBlcnJvcmVkKEVycm9ycy5nZW5lcmljKCdTQVNMX1NJR05BVFVSRV9NSVNNQVRDSCcsICdUaGUgc2VydmVyIGRpZCBub3QgcmV0dXJuIHRoZSBjb3JyZWN0IHNpZ25hdHVyZScpKVxuICAgIHNvY2tldC5kZXN0cm95KClcbiAgfVxuXG4gIGZ1bmN0aW9uIFBhc3MoKSB7XG4gICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSh0eXBlb2Ygb3B0aW9ucy5wYXNzID09PSAnZnVuY3Rpb24nXG4gICAgICA/IG9wdGlvbnMucGFzcygpXG4gICAgICA6IG9wdGlvbnMucGFzc1xuICAgIClcbiAgfVxuXG4gIGZ1bmN0aW9uIE5vRGF0YSgpIHtcbiAgICByZXN1bHQuc3RhdGVtZW50ID0gcXVlcnkuc3RhdGVtZW50XG4gICAgcmVzdWx0LnN0YXRlbWVudC5jb2x1bW5zID0gW11cbiAgICBpZiAocXVlcnkub25seURlc2NyaWJlKVxuICAgICAgcmV0dXJuIChxdWVyeS5yZXNvbHZlKHF1ZXJ5LnN0YXRlbWVudCksIHdyaXRlKFN5bmMpKVxuICB9XG5cbiAgZnVuY3Rpb24gQmFja2VuZEtleURhdGEoeCkge1xuICAgIGJhY2tlbmQucGlkID0geC5yZWFkVUludDMyQkUoNSlcbiAgICBiYWNrZW5kLnNlY3JldCA9IHgucmVhZFVJbnQzMkJFKDkpXG4gIH1cblxuICBhc3luYyBmdW5jdGlvbiBmZXRjaEFycmF5VHlwZXMoKSB7XG4gICAgbmVlZHNUeXBlcyA9IGZhbHNlXG4gICAgY29uc3QgdHlwZXMgPSBhd2FpdCBuZXcgUXVlcnkoW2BcbiAgICAgIHNlbGVjdCBiLm9pZCwgYi50eXBhcnJheVxuICAgICAgZnJvbSBwZ19jYXRhbG9nLnBnX3R5cGUgYVxuICAgICAgbGVmdCBqb2luIHBnX2NhdGFsb2cucGdfdHlwZSBiIG9uIGIub2lkID0gYS50eXBlbGVtXG4gICAgICB3aGVyZSBhLnR5cGNhdGVnb3J5ID0gJ0EnXG4gICAgICBncm91cCBieSBiLm9pZCwgYi50eXBhcnJheVxuICAgICAgb3JkZXIgYnkgYi5vaWRcbiAgICBgXSwgW10sIGV4ZWN1dGUpXG4gICAgdHlwZXMuZm9yRWFjaCgoeyBvaWQsIHR5cGFycmF5IH0pID0+IGFkZEFycmF5VHlwZShvaWQsIHR5cGFycmF5KSlcbiAgfVxuXG4gIGZ1bmN0aW9uIGFkZEFycmF5VHlwZShvaWQsIHR5cGFycmF5KSB7XG4gICAgaWYgKCEhb3B0aW9ucy5wYXJzZXJzW3R5cGFycmF5XSAmJiAhIW9wdGlvbnMuc2VyaWFsaXplcnNbdHlwYXJyYXldKSByZXR1cm5cbiAgICBjb25zdCBwYXJzZXIgPSBvcHRpb25zLnBhcnNlcnNbb2lkXVxuICAgIG9wdGlvbnMuc2hhcmVkLnR5cGVBcnJheU1hcFtvaWRdID0gdHlwYXJyYXlcbiAgICBvcHRpb25zLnBhcnNlcnNbdHlwYXJyYXldID0gKHhzKSA9PiBhcnJheVBhcnNlcih4cywgcGFyc2VyLCB0eXBhcnJheSlcbiAgICBvcHRpb25zLnBhcnNlcnNbdHlwYXJyYXldLmFycmF5ID0gdHJ1ZVxuICAgIG9wdGlvbnMuc2VyaWFsaXplcnNbdHlwYXJyYXldID0gKHhzKSA9PiBhcnJheVNlcmlhbGl6ZXIoeHMsIG9wdGlvbnMuc2VyaWFsaXplcnNbb2lkXSwgb3B0aW9ucywgdHlwYXJyYXkpXG4gIH1cblxuICBmdW5jdGlvbiB0cnlOZXh0KHgsIHhzKSB7XG4gICAgcmV0dXJuIChcbiAgICAgICh4ID09PSAncmVhZC13cml0ZScgJiYgeHMuZGVmYXVsdF90cmFuc2FjdGlvbl9yZWFkX29ubHkgPT09ICdvbicpIHx8XG4gICAgICAoeCA9PT0gJ3JlYWQtb25seScgJiYgeHMuZGVmYXVsdF90cmFuc2FjdGlvbl9yZWFkX29ubHkgPT09ICdvZmYnKSB8fFxuICAgICAgKHggPT09ICdwcmltYXJ5JyAmJiB4cy5pbl9ob3Rfc3RhbmRieSA9PT0gJ29uJykgfHxcbiAgICAgICh4ID09PSAnc3RhbmRieScgJiYgeHMuaW5faG90X3N0YW5kYnkgPT09ICdvZmYnKSB8fFxuICAgICAgKHggPT09ICdwcmVmZXItc3RhbmRieScgJiYgeHMuaW5faG90X3N0YW5kYnkgPT09ICdvZmYnICYmIG9wdGlvbnMuaG9zdFtyZXRyaWVzXSlcbiAgICApXG4gIH1cblxuICBmdW5jdGlvbiBmZXRjaFN0YXRlKCkge1xuICAgIGNvbnN0IHF1ZXJ5ID0gbmV3IFF1ZXJ5KFtgXG4gICAgICBzaG93IHRyYW5zYWN0aW9uX3JlYWRfb25seTtcbiAgICAgIHNlbGVjdCBwZ19jYXRhbG9nLnBnX2lzX2luX3JlY292ZXJ5KClcbiAgICBgXSwgW10sIGV4ZWN1dGUsIG51bGwsIHsgc2ltcGxlOiB0cnVlIH0pXG4gICAgcXVlcnkucmVzb2x2ZSA9IChbW2FdLCBbYl1dKSA9PiB7XG4gICAgICBiYWNrZW5kUGFyYW1ldGVycy5kZWZhdWx0X3RyYW5zYWN0aW9uX3JlYWRfb25seSA9IGEudHJhbnNhY3Rpb25fcmVhZF9vbmx5XG4gICAgICBiYWNrZW5kUGFyYW1ldGVycy5pbl9ob3Rfc3RhbmRieSA9IGIucGdfaXNfaW5fcmVjb3ZlcnkgPyAnb24nIDogJ29mZidcbiAgICB9XG4gICAgcXVlcnkuZXhlY3V0ZSgpXG4gIH1cblxuICBmdW5jdGlvbiBFcnJvclJlc3BvbnNlKHgpIHtcbiAgICBxdWVyeSAmJiAocXVlcnkuY3Vyc29yRm4gfHwgcXVlcnkuZGVzY3JpYmVGaXJzdCkgJiYgd3JpdGUoU3luYylcbiAgICBjb25zdCBlcnJvciA9IEVycm9ycy5wb3N0Z3JlcyhwYXJzZUVycm9yKHgpKVxuICAgIHF1ZXJ5ICYmIHF1ZXJ5LnJldHJpZWRcbiAgICAgID8gZXJyb3JlZChxdWVyeS5yZXRyaWVkKVxuICAgICAgOiBxdWVyeSAmJiBxdWVyeS5wcmVwYXJlZCAmJiByZXRyeVJvdXRpbmVzLmhhcyhlcnJvci5yb3V0aW5lKVxuICAgICAgICA/IHJldHJ5KHF1ZXJ5LCBlcnJvcilcbiAgICAgICAgOiBlcnJvcmVkKGVycm9yKVxuICB9XG5cbiAgZnVuY3Rpb24gcmV0cnkocSwgZXJyb3IpIHtcbiAgICBkZWxldGUgc3RhdGVtZW50c1txLnNpZ25hdHVyZV1cbiAgICBxLnJldHJpZWQgPSBlcnJvclxuICAgIGV4ZWN1dGUocSlcbiAgfVxuXG4gIGZ1bmN0aW9uIE5vdGlmaWNhdGlvblJlc3BvbnNlKHgpIHtcbiAgICBpZiAoIW9ubm90aWZ5KVxuICAgICAgcmV0dXJuXG5cbiAgICBsZXQgaW5kZXggPSA5XG4gICAgd2hpbGUgKHhbaW5kZXgrK10gIT09IDApO1xuICAgIG9ubm90aWZ5KFxuICAgICAgeC50b1N0cmluZygndXRmOCcsIDksIGluZGV4IC0gMSksXG4gICAgICB4LnRvU3RyaW5nKCd1dGY4JywgaW5kZXgsIHgubGVuZ3RoIC0gMSlcbiAgICApXG4gIH1cblxuICBhc3luYyBmdW5jdGlvbiBQb3J0YWxTdXNwZW5kZWQoKSB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHggPSBhd2FpdCBQcm9taXNlLnJlc29sdmUocXVlcnkuY3Vyc29yRm4ocmVzdWx0KSlcbiAgICAgIHJvd3MgPSAwXG4gICAgICB4ID09PSBDTE9TRVxuICAgICAgICA/IHdyaXRlKENsb3NlKHF1ZXJ5LnBvcnRhbCkpXG4gICAgICAgIDogKHJlc3VsdCA9IG5ldyBSZXN1bHQoKSwgd3JpdGUoRXhlY3V0ZSgnJywgcXVlcnkuY3Vyc29yUm93cykpKVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgd3JpdGUoU3luYylcbiAgICAgIHF1ZXJ5LnJlamVjdChlcnIpXG4gICAgfVxuICB9XG5cbiAgZnVuY3Rpb24gQ2xvc2VDb21wbGV0ZSgpIHtcbiAgICByZXN1bHQuY291bnQgJiYgcXVlcnkuY3Vyc29yRm4ocmVzdWx0KVxuICAgIHF1ZXJ5LnJlc29sdmUocmVzdWx0KVxuICB9XG5cbiAgZnVuY3Rpb24gQ29weUluUmVzcG9uc2UoKSB7XG4gICAgc3RyZWFtID0gbmV3IFN0cmVhbS5Xcml0YWJsZSh7XG4gICAgICBhdXRvRGVzdHJveTogdHJ1ZSxcbiAgICAgIHdyaXRlKGNodW5rLCBlbmNvZGluZywgY2FsbGJhY2spIHtcbiAgICAgICAgc29ja2V0LndyaXRlKGIoKS5kKCkucmF3KGNodW5rKS5lbmQoKSwgY2FsbGJhY2spXG4gICAgICB9LFxuICAgICAgZGVzdHJveShlcnJvciwgY2FsbGJhY2spIHtcbiAgICAgICAgY2FsbGJhY2soZXJyb3IpXG4gICAgICAgIHNvY2tldC53cml0ZShiKCkuZigpLnN0cihlcnJvciArIGIuTikuZW5kKCkpXG4gICAgICAgIHN0cmVhbSA9IG51bGxcbiAgICAgIH0sXG4gICAgICBmaW5hbChjYWxsYmFjaykge1xuICAgICAgICBzb2NrZXQud3JpdGUoYigpLmMoKS5lbmQoKSlcbiAgICAgICAgZmluYWwgPSBjYWxsYmFja1xuICAgICAgfVxuICAgIH0pXG4gICAgcXVlcnkucmVzb2x2ZShzdHJlYW0pXG4gIH1cblxuICBmdW5jdGlvbiBDb3B5T3V0UmVzcG9uc2UoKSB7XG4gICAgc3RyZWFtID0gbmV3IFN0cmVhbS5SZWFkYWJsZSh7XG4gICAgICByZWFkKCkgeyBzb2NrZXQucmVzdW1lKCkgfVxuICAgIH0pXG4gICAgcXVlcnkucmVzb2x2ZShzdHJlYW0pXG4gIH1cblxuICAvKiBjOCBpZ25vcmUgbmV4dCAzICovXG4gIGZ1bmN0aW9uIENvcHlCb3RoUmVzcG9uc2UoKSB7XG4gICAgc3RyZWFtID0gbmV3IFN0cmVhbS5EdXBsZXgoe1xuICAgICAgYXV0b0Rlc3Ryb3k6IHRydWUsXG4gICAgICByZWFkKCkgeyBzb2NrZXQucmVzdW1lKCkgfSxcbiAgICAgIC8qIGM4IGlnbm9yZSBuZXh0IDExICovXG4gICAgICB3cml0ZShjaHVuaywgZW5jb2RpbmcsIGNhbGxiYWNrKSB7XG4gICAgICAgIHNvY2tldC53cml0ZShiKCkuZCgpLnJhdyhjaHVuaykuZW5kKCksIGNhbGxiYWNrKVxuICAgICAgfSxcbiAgICAgIGRlc3Ryb3koZXJyb3IsIGNhbGxiYWNrKSB7XG4gICAgICAgIGNhbGxiYWNrKGVycm9yKVxuICAgICAgICBzb2NrZXQud3JpdGUoYigpLmYoKS5zdHIoZXJyb3IgKyBiLk4pLmVuZCgpKVxuICAgICAgICBzdHJlYW0gPSBudWxsXG4gICAgICB9LFxuICAgICAgZmluYWwoY2FsbGJhY2spIHtcbiAgICAgICAgc29ja2V0LndyaXRlKGIoKS5jKCkuZW5kKCkpXG4gICAgICAgIGZpbmFsID0gY2FsbGJhY2tcbiAgICAgIH1cbiAgICB9KVxuICAgIHF1ZXJ5LnJlc29sdmUoc3RyZWFtKVxuICB9XG5cbiAgZnVuY3Rpb24gQ29weURhdGEoeCkge1xuICAgIHN0cmVhbSAmJiAoc3RyZWFtLnB1c2goeC5zdWJhcnJheSg1KSkgfHwgc29ja2V0LnBhdXNlKCkpXG4gIH1cblxuICBmdW5jdGlvbiBDb3B5RG9uZSgpIHtcbiAgICBzdHJlYW0gJiYgc3RyZWFtLnB1c2gobnVsbClcbiAgICBzdHJlYW0gPSBudWxsXG4gIH1cblxuICBmdW5jdGlvbiBOb3RpY2VSZXNwb25zZSh4KSB7XG4gICAgb25ub3RpY2VcbiAgICAgID8gb25ub3RpY2UocGFyc2VFcnJvcih4KSlcbiAgICAgIDogY29uc29sZS5sb2cocGFyc2VFcnJvcih4KSkgLy8gZXNsaW50LWRpc2FibGUtbGluZVxuXG4gIH1cblxuICAvKiBjOCBpZ25vcmUgbmV4dCAzICovXG4gIGZ1bmN0aW9uIEVtcHR5UXVlcnlSZXNwb25zZSgpIHtcbiAgICAvKiBub29wICovXG4gIH1cblxuICAvKiBjOCBpZ25vcmUgbmV4dCAzICovXG4gIGZ1bmN0aW9uIEZ1bmN0aW9uQ2FsbFJlc3BvbnNlKCkge1xuICAgIGVycm9yZWQoRXJyb3JzLm5vdFN1cHBvcnRlZCgnRnVuY3Rpb25DYWxsUmVzcG9uc2UnKSlcbiAgfVxuXG4gIC8qIGM4IGlnbm9yZSBuZXh0IDMgKi9cbiAgZnVuY3Rpb24gTmVnb3RpYXRlUHJvdG9jb2xWZXJzaW9uKCkge1xuICAgIGVycm9yZWQoRXJyb3JzLm5vdFN1cHBvcnRlZCgnTmVnb3RpYXRlUHJvdG9jb2xWZXJzaW9uJykpXG4gIH1cblxuICAvKiBjOCBpZ25vcmUgbmV4dCAzICovXG4gIGZ1bmN0aW9uIFVua25vd25NZXNzYWdlKHgpIHtcbiAgICBjb25zb2xlLmVycm9yKCdQb3N0Z3Jlcy5qcyA6IFVua25vd24gTWVzc2FnZTonLCB4WzBdKSAvLyBlc2xpbnQtZGlzYWJsZS1saW5lXG4gIH1cblxuICAvKiBjOCBpZ25vcmUgbmV4dCAzICovXG4gIGZ1bmN0aW9uIFVua25vd25BdXRoKHgsIHR5cGUpIHtcbiAgICBjb25zb2xlLmVycm9yKCdQb3N0Z3Jlcy5qcyA6IFVua25vd24gQXV0aDonLCB0eXBlKSAvLyBlc2xpbnQtZGlzYWJsZS1saW5lXG4gIH1cblxuICAvKiBNZXNzYWdlcyAqL1xuICBmdW5jdGlvbiBCaW5kKHBhcmFtZXRlcnMsIHR5cGVzLCBzdGF0ZW1lbnQgPSAnJywgcG9ydGFsID0gJycpIHtcbiAgICBsZXQgcHJldlxuICAgICAgLCB0eXBlXG5cbiAgICBiKCkuQigpLnN0cihwb3J0YWwgKyBiLk4pLnN0cihzdGF0ZW1lbnQgKyBiLk4pLmkxNigwKS5pMTYocGFyYW1ldGVycy5sZW5ndGgpXG5cbiAgICBwYXJhbWV0ZXJzLmZvckVhY2goKHgsIGkpID0+IHtcbiAgICAgIGlmICh4ID09PSBudWxsKVxuICAgICAgICByZXR1cm4gYi5pMzIoMHhGRkZGRkZGRilcblxuICAgICAgdHlwZSA9IHR5cGVzW2ldXG4gICAgICBwYXJhbWV0ZXJzW2ldID0geCA9IHR5cGUgaW4gb3B0aW9ucy5zZXJpYWxpemVyc1xuICAgICAgICA/IG9wdGlvbnMuc2VyaWFsaXplcnNbdHlwZV0oeClcbiAgICAgICAgOiAnJyArIHhcblxuICAgICAgcHJldiA9IGIuaVxuICAgICAgYi5pbmMoNCkuc3RyKHgpLmkzMihiLmkgLSBwcmV2IC0gNCwgcHJldilcbiAgICB9KVxuXG4gICAgYi5pMTYoMClcblxuICAgIHJldHVybiBiLmVuZCgpXG4gIH1cblxuICBmdW5jdGlvbiBQYXJzZShzdHIsIHBhcmFtZXRlcnMsIHR5cGVzLCBuYW1lID0gJycpIHtcbiAgICBiKCkuUCgpLnN0cihuYW1lICsgYi5OKS5zdHIoc3RyICsgYi5OKS5pMTYocGFyYW1ldGVycy5sZW5ndGgpXG4gICAgcGFyYW1ldGVycy5mb3JFYWNoKCh4LCBpKSA9PiBiLmkzMih0eXBlc1tpXSB8fCAwKSlcbiAgICByZXR1cm4gYi5lbmQoKVxuICB9XG5cbiAgZnVuY3Rpb24gRGVzY3JpYmUoeCwgbmFtZSA9ICcnKSB7XG4gICAgcmV0dXJuIGIoKS5EKCkuc3RyKHgpLnN0cihuYW1lICsgYi5OKS5lbmQoKVxuICB9XG5cbiAgZnVuY3Rpb24gRXhlY3V0ZShwb3J0YWwgPSAnJywgcm93cyA9IDApIHtcbiAgICByZXR1cm4gQnVmZmVyLmNvbmNhdChbXG4gICAgICBiKCkuRSgpLnN0cihwb3J0YWwgKyBiLk4pLmkzMihyb3dzKS5lbmQoKSxcbiAgICAgIEZsdXNoXG4gICAgXSlcbiAgfVxuXG4gIGZ1bmN0aW9uIENsb3NlKHBvcnRhbCA9ICcnKSB7XG4gICAgcmV0dXJuIEJ1ZmZlci5jb25jYXQoW1xuICAgICAgYigpLkMoKS5zdHIoJ1AnKS5zdHIocG9ydGFsICsgYi5OKS5lbmQoKSxcbiAgICAgIGIoKS5TKCkuZW5kKClcbiAgICBdKVxuICB9XG5cbiAgZnVuY3Rpb24gU3RhcnR1cE1lc3NhZ2UoKSB7XG4gICAgcmV0dXJuIGNhbmNlbE1lc3NhZ2UgfHwgYigpLmluYyg0KS5pMTYoMykueigyKS5zdHIoXG4gICAgICBPYmplY3QuZW50cmllcyhPYmplY3QuYXNzaWduKHtcbiAgICAgICAgdXNlcixcbiAgICAgICAgZGF0YWJhc2UsXG4gICAgICAgIGNsaWVudF9lbmNvZGluZzogJ1VURjgnXG4gICAgICB9LFxuICAgICAgICBvcHRpb25zLmNvbm5lY3Rpb25cbiAgICAgICkpLmZpbHRlcigoWywgdl0pID0+IHYpLm1hcCgoW2ssIHZdKSA9PiBrICsgYi5OICsgdikuam9pbihiLk4pXG4gICAgKS56KDIpLmVuZCgwKVxuICB9XG5cbn1cblxuZnVuY3Rpb24gcGFyc2VFcnJvcih4KSB7XG4gIGNvbnN0IGVycm9yID0ge31cbiAgbGV0IHN0YXJ0ID0gNVxuICBmb3IgKGxldCBpID0gNTsgaSA8IHgubGVuZ3RoIC0gMTsgaSsrKSB7XG4gICAgaWYgKHhbaV0gPT09IDApIHtcbiAgICAgIGVycm9yW2Vycm9yRmllbGRzW3hbc3RhcnRdXV0gPSB4LnRvU3RyaW5nKCd1dGY4Jywgc3RhcnQgKyAxLCBpKVxuICAgICAgc3RhcnQgPSBpICsgMVxuICAgIH1cbiAgfVxuICByZXR1cm4gZXJyb3Jcbn1cblxuZnVuY3Rpb24gbWQ1KHgpIHtcbiAgcmV0dXJuIGNyeXB0by5jcmVhdGVIYXNoKCdtZDUnKS51cGRhdGUoeCkuZGlnZXN0KCdoZXgnKVxufVxuXG5mdW5jdGlvbiBobWFjKGtleSwgeCkge1xuICByZXR1cm4gY3J5cHRvLmNyZWF0ZUhtYWMoJ3NoYTI1NicsIGtleSkudXBkYXRlKHgpLmRpZ2VzdCgpXG59XG5cbmZ1bmN0aW9uIHNoYTI1Nih4KSB7XG4gIHJldHVybiBjcnlwdG8uY3JlYXRlSGFzaCgnc2hhMjU2JykudXBkYXRlKHgpLmRpZ2VzdCgpXG59XG5cbmZ1bmN0aW9uIHhvcihhLCBiKSB7XG4gIGNvbnN0IGxlbmd0aCA9IE1hdGgubWF4KGEubGVuZ3RoLCBiLmxlbmd0aClcbiAgY29uc3QgYnVmZmVyID0gQnVmZmVyLmFsbG9jVW5zYWZlKGxlbmd0aClcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBsZW5ndGg7IGkrKylcbiAgICBidWZmZXJbaV0gPSBhW2ldIF4gYltpXVxuICByZXR1cm4gYnVmZmVyXG59XG5cbmZ1bmN0aW9uIHRpbWVyKGZuLCBzZWNvbmRzKSB7XG4gIHNlY29uZHMgPSB0eXBlb2Ygc2Vjb25kcyA9PT0gJ2Z1bmN0aW9uJyA/IHNlY29uZHMoKSA6IHNlY29uZHNcbiAgaWYgKCFzZWNvbmRzKVxuICAgIHJldHVybiB7IGNhbmNlbDogbm9vcCwgc3RhcnQ6IG5vb3AgfVxuXG4gIGxldCB0aW1lclxuICByZXR1cm4ge1xuICAgIGNhbmNlbCgpIHtcbiAgICAgIHRpbWVyICYmIChjbGVhclRpbWVvdXQodGltZXIpLCB0aW1lciA9IG51bGwpXG4gICAgfSxcbiAgICBzdGFydCgpIHtcbiAgICAgIHRpbWVyICYmIGNsZWFyVGltZW91dCh0aW1lcilcbiAgICAgIHRpbWVyID0gc2V0VGltZW91dChkb25lLCBzZWNvbmRzICogMTAwMCwgYXJndW1lbnRzKVxuICAgIH1cbiAgfVxuXG4gIGZ1bmN0aW9uIGRvbmUoYXJncykge1xuICAgIGZuLmFwcGx5KG51bGwsIGFyZ3MpXG4gICAgdGltZXIgPSBudWxsXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/connection.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/errors.js":
/*!*********************************************!*\
  !*** ./node_modules/postgres/src/errors.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Errors: () => (/* binding */ Errors),\n/* harmony export */   PostgresError: () => (/* binding */ PostgresError)\n/* harmony export */ });\nclass PostgresError extends Error {\n  constructor(x) {\n    super(x.message)\n    this.name = this.constructor.name\n    Object.assign(this, x)\n  }\n}\n\nconst Errors = {\n  connection,\n  postgres,\n  generic,\n  notSupported\n}\n\nfunction connection(x, options, socket) {\n  const { host, port } = socket || options\n  const error = Object.assign(\n    new Error(('write ' + x + ' ' + (options.path || (host + ':' + port)))),\n    {\n      code: x,\n      errno: x,\n      address: options.path || host\n    }, options.path ? {} : { port: port }\n  )\n  Error.captureStackTrace(error, connection)\n  return error\n}\n\nfunction postgres(x) {\n  const error = new PostgresError(x)\n  Error.captureStackTrace(error, postgres)\n  return error\n}\n\nfunction generic(code, message) {\n  const error = Object.assign(new Error(code + ': ' + message), { code })\n  Error.captureStackTrace(error, generic)\n  return error\n}\n\n/* c8 ignore next 10 */\nfunction notSupported(x) {\n  const error = Object.assign(\n    new Error(x + ' (B) is not supported'),\n    {\n      code: 'MESSAGE_NOT_SUPPORTED',\n      name: x\n    }\n  )\n  Error.captureStackTrace(error, notSupported)\n  return error\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/index.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! os */ \"os\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types.js */ \"(rsc)/./node_modules/postgres/src/types.js\");\n/* harmony import */ var _connection_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./connection.js */ \"(rsc)/./node_modules/postgres/src/connection.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./query.js */ \"(rsc)/./node_modules/postgres/src/query.js\");\n/* harmony import */ var _queue_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./queue.js */ \"(rsc)/./node_modules/postgres/src/queue.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors.js */ \"(rsc)/./node_modules/postgres/src/errors.js\");\n/* harmony import */ var _subscribe_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./subscribe.js */ \"(rsc)/./node_modules/postgres/src/subscribe.js\");\n/* harmony import */ var _large_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./large.js */ \"(rsc)/./node_modules/postgres/src/large.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nObject.assign(Postgres, {\n  PostgresError: _errors_js__WEBPACK_IMPORTED_MODULE_6__.PostgresError,\n  toPascal: _types_js__WEBPACK_IMPORTED_MODULE_2__.toPascal,\n  pascal: _types_js__WEBPACK_IMPORTED_MODULE_2__.pascal,\n  toCamel: _types_js__WEBPACK_IMPORTED_MODULE_2__.toCamel,\n  camel: _types_js__WEBPACK_IMPORTED_MODULE_2__.camel,\n  toKebab: _types_js__WEBPACK_IMPORTED_MODULE_2__.toKebab,\n  kebab: _types_js__WEBPACK_IMPORTED_MODULE_2__.kebab,\n  fromPascal: _types_js__WEBPACK_IMPORTED_MODULE_2__.fromPascal,\n  fromCamel: _types_js__WEBPACK_IMPORTED_MODULE_2__.fromCamel,\n  fromKebab: _types_js__WEBPACK_IMPORTED_MODULE_2__.fromKebab,\n  BigInt: {\n    to: 20,\n    from: [20],\n    parse: x => BigInt(x), // eslint-disable-line\n    serialize: x => x.toString()\n  }\n})\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Postgres);\n\nfunction Postgres(a, b) {\n  const options = parseOptions(a, b)\n      , subscribe = options.no_subscribe || (0,_subscribe_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(Postgres, { ...options })\n\n  let ending = false\n\n  const queries = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , connecting = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , reserved = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , closed = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , ended = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , open = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , busy = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , full = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n      , queues = { connecting, reserved, closed, ended, open, busy, full }\n\n  const connections = [...Array(options.max)].map(() => (0,_connection_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options, queues, { onopen, onend, onclose }))\n\n  const sql = Sql(handler)\n\n  Object.assign(sql, {\n    get parameters() { return options.parameters },\n    largeObject: _large_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"].bind(null, sql),\n    subscribe,\n    CLOSE: _query_js__WEBPACK_IMPORTED_MODULE_4__.CLOSE,\n    END: _query_js__WEBPACK_IMPORTED_MODULE_4__.CLOSE,\n    PostgresError: _errors_js__WEBPACK_IMPORTED_MODULE_6__.PostgresError,\n    options,\n    reserve,\n    listen,\n    begin,\n    close,\n    end\n  })\n\n  return sql\n\n  function Sql(handler) {\n    handler.debug = options.debug\n\n    Object.entries(options.types).reduce((acc, [name, type]) => {\n      acc[name] = (x) => new _types_js__WEBPACK_IMPORTED_MODULE_2__.Parameter(x, type.to)\n      return acc\n    }, typed)\n\n    Object.assign(sql, {\n      types: typed,\n      typed,\n      unsafe,\n      notify,\n      array,\n      json,\n      file\n    })\n\n    return sql\n\n    function typed(value, type) {\n      return new _types_js__WEBPACK_IMPORTED_MODULE_2__.Parameter(value, type)\n    }\n\n    function sql(strings, ...args) {\n      const query = strings && Array.isArray(strings.raw)\n        ? new _query_js__WEBPACK_IMPORTED_MODULE_4__.Query(strings, args, handler, cancel)\n        : typeof strings === 'string' && !args.length\n          ? new _types_js__WEBPACK_IMPORTED_MODULE_2__.Identifier(options.transform.column.to ? options.transform.column.to(strings) : strings)\n          : new _types_js__WEBPACK_IMPORTED_MODULE_2__.Builder(strings, args)\n      return query\n    }\n\n    function unsafe(string, args = [], options = {}) {\n      arguments.length === 2 && !Array.isArray(args) && (options = args, args = [])\n      const query = new _query_js__WEBPACK_IMPORTED_MODULE_4__.Query([string], args, handler, cancel, {\n        prepare: false,\n        ...options,\n        simple: 'simple' in options ? options.simple : args.length === 0\n      })\n      return query\n    }\n\n    function file(path, args = [], options = {}) {\n      arguments.length === 2 && !Array.isArray(args) && (options = args, args = [])\n      const query = new _query_js__WEBPACK_IMPORTED_MODULE_4__.Query([], args, (query) => {\n        fs__WEBPACK_IMPORTED_MODULE_1__.readFile(path, 'utf8', (err, string) => {\n          if (err)\n            return query.reject(err)\n\n          query.strings = [string]\n          handler(query)\n        })\n      }, cancel, {\n        ...options,\n        simple: 'simple' in options ? options.simple : args.length === 0\n      })\n      return query\n    }\n  }\n\n  async function listen(name, fn, onlisten) {\n    const listener = { fn, onlisten }\n\n    const sql = listen.sql || (listen.sql = Postgres({\n      ...options,\n      max: 1,\n      idle_timeout: null,\n      max_lifetime: null,\n      fetch_types: false,\n      onclose() {\n        Object.entries(listen.channels).forEach(([name, { listeners }]) => {\n          delete listen.channels[name]\n          Promise.all(listeners.map(l => listen(name, l.fn, l.onlisten).catch(() => { /* noop */ })))\n        })\n      },\n      onnotify(c, x) {\n        c in listen.channels && listen.channels[c].listeners.forEach(l => l.fn(x))\n      }\n    }))\n\n    const channels = listen.channels || (listen.channels = {})\n        , exists = name in channels\n\n    if (exists) {\n      channels[name].listeners.push(listener)\n      const result = await channels[name].result\n      listener.onlisten && listener.onlisten()\n      return { state: result.state, unlisten }\n    }\n\n    channels[name] = { result: sql`listen ${\n      sql.unsafe('\"' + name.replace(/\"/g, '\"\"') + '\"')\n    }`, listeners: [listener] }\n    const result = await channels[name].result\n    listener.onlisten && listener.onlisten()\n    return { state: result.state, unlisten }\n\n    async function unlisten() {\n      if (name in channels === false)\n        return\n\n      channels[name].listeners = channels[name].listeners.filter(x => x !== listener)\n      if (channels[name].listeners.length)\n        return\n\n      delete channels[name]\n      return sql`unlisten ${\n        sql.unsafe('\"' + name.replace(/\"/g, '\"\"') + '\"')\n      }`\n    }\n  }\n\n  async function notify(channel, payload) {\n    return await sql`select pg_notify(${ channel }, ${ '' + payload })`\n  }\n\n  async function reserve() {\n    const queue = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n    const c = open.length\n      ? open.shift()\n      : await new Promise((resolve, reject) => {\n        const query = { reserve: resolve, reject }\n        queries.push(query)\n        closed.length && connect(closed.shift(), query)\n      })\n\n    move(c, reserved)\n    c.reserved = () => queue.length\n      ? c.execute(queue.shift())\n      : move(c, reserved)\n    c.reserved.release = true\n\n    const sql = Sql(handler)\n    sql.release = () => {\n      c.reserved = null\n      onopen(c)\n    }\n\n    return sql\n\n    function handler(q) {\n      c.queue === full\n        ? queue.push(q)\n        : c.execute(q) || move(c, full)\n    }\n  }\n\n  async function begin(options, fn) {\n    !fn && (fn = options, options = '')\n    const queries = (0,_queue_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])()\n    let savepoints = 0\n      , connection\n      , prepare = null\n\n    try {\n      await sql.unsafe('begin ' + options.replace(/[^a-z ]/ig, ''), [], { onexecute }).execute()\n      return await Promise.race([\n        scope(connection, fn),\n        new Promise((_, reject) => connection.onclose = reject)\n      ])\n    } catch (error) {\n      throw error\n    }\n\n    async function scope(c, fn, name) {\n      const sql = Sql(handler)\n      sql.savepoint = savepoint\n      sql.prepare = x => prepare = x.replace(/[^a-z0-9$-_. ]/gi)\n      let uncaughtError\n        , result\n\n      name && await sql`savepoint ${ sql(name) }`\n      try {\n        result = await new Promise((resolve, reject) => {\n          const x = fn(sql)\n          Promise.resolve(Array.isArray(x) ? Promise.all(x) : x).then(resolve, reject)\n        })\n\n        if (uncaughtError)\n          throw uncaughtError\n      } catch (e) {\n        await (name\n          ? sql`rollback to ${ sql(name) }`\n          : sql`rollback`\n        )\n        throw e instanceof _errors_js__WEBPACK_IMPORTED_MODULE_6__.PostgresError && e.code === '25P02' && uncaughtError || e\n      }\n\n      if (!name) {\n        prepare\n          ? await sql`prepare transaction '${ sql.unsafe(prepare) }'`\n          : await sql`commit`\n      }\n\n      return result\n\n      function savepoint(name, fn) {\n        if (name && Array.isArray(name.raw))\n          return savepoint(sql => sql.apply(sql, arguments))\n\n        arguments.length === 1 && (fn = name, name = null)\n        return scope(c, fn, 's' + savepoints++ + (name ? '_' + name : ''))\n      }\n\n      function handler(q) {\n        q.catch(e => uncaughtError || (uncaughtError = e))\n        c.queue === full\n          ? queries.push(q)\n          : c.execute(q) || move(c, full)\n      }\n    }\n\n    function onexecute(c) {\n      connection = c\n      move(c, reserved)\n      c.reserved = () => queries.length\n        ? c.execute(queries.shift())\n        : move(c, reserved)\n    }\n  }\n\n  function move(c, queue) {\n    c.queue.remove(c)\n    queue.push(c)\n    c.queue = queue\n    queue === open\n      ? c.idleTimer.start()\n      : c.idleTimer.cancel()\n    return c\n  }\n\n  function json(x) {\n    return new _types_js__WEBPACK_IMPORTED_MODULE_2__.Parameter(x, 3802)\n  }\n\n  function array(x, type) {\n    if (!Array.isArray(x))\n      return array(Array.from(arguments))\n\n    return new _types_js__WEBPACK_IMPORTED_MODULE_2__.Parameter(x, type || (x.length ? (0,_types_js__WEBPACK_IMPORTED_MODULE_2__.inferType)(x) || 25 : 0), options.shared.typeArrayMap)\n  }\n\n  function handler(query) {\n    if (ending)\n      return query.reject(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_ENDED', options, options))\n\n    if (open.length)\n      return go(open.shift(), query)\n\n    if (closed.length)\n      return connect(closed.shift(), query)\n\n    busy.length\n      ? go(busy.shift(), query)\n      : queries.push(query)\n  }\n\n  function go(c, query) {\n    return c.execute(query)\n      ? move(c, busy)\n      : move(c, full)\n  }\n\n  function cancel(query) {\n    return new Promise((resolve, reject) => {\n      query.state\n        ? query.active\n          ? (0,_connection_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options).cancel(query.state, resolve, reject)\n          : query.cancelled = { resolve, reject }\n        : (\n          queries.remove(query),\n          query.cancelled = true,\n          query.reject(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.generic('57014', 'canceling statement due to user request')),\n          resolve()\n        )\n    })\n  }\n\n  async function end({ timeout = null } = {}) {\n    if (ending)\n      return ending\n\n    await 1\n    let timer\n    return ending = Promise.race([\n      new Promise(r => timeout !== null && (timer = setTimeout(destroy, timeout * 1000, r))),\n      Promise.all(connections.map(c => c.end()).concat(\n        listen.sql ? listen.sql.end({ timeout: 0 }) : [],\n        subscribe.sql ? subscribe.sql.end({ timeout: 0 }) : []\n      ))\n    ]).then(() => clearTimeout(timer))\n  }\n\n  async function close() {\n    await Promise.all(connections.map(c => c.end()))\n  }\n\n  async function destroy(resolve) {\n    await Promise.all(connections.map(c => c.terminate()))\n    while (queries.length)\n      queries.shift().reject(_errors_js__WEBPACK_IMPORTED_MODULE_6__.Errors.connection('CONNECTION_DESTROYED', options))\n    resolve()\n  }\n\n  function connect(c, query) {\n    move(c, connecting)\n    c.connect(query)\n    return c\n  }\n\n  function onend(c) {\n    move(c, ended)\n  }\n\n  function onopen(c) {\n    if (queries.length === 0)\n      return move(c, open)\n\n    let max = Math.ceil(queries.length / (connecting.length + 1))\n      , ready = true\n\n    while (ready && queries.length && max-- > 0) {\n      const query = queries.shift()\n      if (query.reserve)\n        return query.reserve(c)\n\n      ready = c.execute(query)\n    }\n\n    ready\n      ? move(c, busy)\n      : move(c, full)\n  }\n\n  function onclose(c, e) {\n    move(c, closed)\n    c.reserved = null\n    c.onclose && (c.onclose(e), c.onclose = null)\n    options.onclose && options.onclose(c.id)\n    queries.length && connect(c, queries.shift())\n  }\n}\n\nfunction parseOptions(a, b) {\n  if (a && a.shared)\n    return a\n\n  const env = process.env // eslint-disable-line\n      , o = (!a || typeof a === 'string' ? b : a) || {}\n      , { url, multihost } = parseUrl(a)\n      , query = [...url.searchParams].reduce((a, [b, c]) => (a[b] = c, a), {})\n      , host = o.hostname || o.host || multihost || url.hostname || env.PGHOST || 'localhost'\n      , port = o.port || url.port || env.PGPORT || 5432\n      , user = o.user || o.username || url.username || env.PGUSERNAME || env.PGUSER || osUsername()\n\n  o.no_prepare && (o.prepare = false)\n  query.sslmode && (query.ssl = query.sslmode, delete query.sslmode)\n  'timeout' in o && (console.log('The timeout option is deprecated, use idle_timeout instead'), o.idle_timeout = o.timeout) // eslint-disable-line\n  query.sslrootcert === 'system' && (query.ssl = 'verify-full')\n\n  const ints = ['idle_timeout', 'connect_timeout', 'max_lifetime', 'max_pipeline', 'backoff', 'keep_alive']\n  const defaults = {\n    max             : 10,\n    ssl             : false,\n    idle_timeout    : null,\n    connect_timeout : 30,\n    max_lifetime    : max_lifetime,\n    max_pipeline    : 100,\n    backoff         : backoff,\n    keep_alive      : 60,\n    prepare         : true,\n    debug           : false,\n    fetch_types     : true,\n    publications    : 'alltables',\n    target_session_attrs: null\n  }\n\n  return {\n    host            : Array.isArray(host) ? host : host.split(',').map(x => x.split(':')[0]),\n    port            : Array.isArray(port) ? port : host.split(',').map(x => parseInt(x.split(':')[1] || port)),\n    path            : o.path || host.indexOf('/') > -1 && host + '/.s.PGSQL.' + port,\n    database        : o.database || o.db || (url.pathname || '').slice(1) || env.PGDATABASE || user,\n    user            : user,\n    pass            : o.pass || o.password || url.password || env.PGPASSWORD || '',\n    ...Object.entries(defaults).reduce(\n      (acc, [k, d]) => {\n        const value = k in o ? o[k] : k in query\n          ? (query[k] === 'disable' || query[k] === 'false' ? false : query[k])\n          : env['PG' + k.toUpperCase()] || d\n        acc[k] = typeof value === 'string' && ints.includes(k)\n          ? +value\n          : value\n        return acc\n      },\n      {}\n    ),\n    connection      : {\n      application_name: env.PGAPPNAME || 'postgres.js',\n      ...o.connection,\n      ...Object.entries(query).reduce((acc, [k, v]) => (k in defaults || (acc[k] = v), acc), {})\n    },\n    types           : o.types || {},\n    target_session_attrs: tsa(o, url, env),\n    onnotice        : o.onnotice,\n    onnotify        : o.onnotify,\n    onclose         : o.onclose,\n    onparameter     : o.onparameter,\n    socket          : o.socket,\n    transform       : parseTransform(o.transform || { undefined: undefined }),\n    parameters      : {},\n    shared          : { retries: 0, typeArrayMap: {} },\n    ...(0,_types_js__WEBPACK_IMPORTED_MODULE_2__.mergeUserTypes)(o.types)\n  }\n}\n\nfunction tsa(o, url, env) {\n  const x = o.target_session_attrs || url.searchParams.get('target_session_attrs') || env.PGTARGETSESSIONATTRS\n  if (!x || ['read-write', 'read-only', 'primary', 'standby', 'prefer-standby'].includes(x))\n    return x\n\n  throw new Error('target_session_attrs ' + x + ' is not supported')\n}\n\nfunction backoff(retries) {\n  return (0.5 + Math.random() / 2) * Math.min(3 ** retries / 100, 20)\n}\n\nfunction max_lifetime() {\n  return 60 * (30 + Math.random() * 30)\n}\n\nfunction parseTransform(x) {\n  return {\n    undefined: x.undefined,\n    column: {\n      from: typeof x.column === 'function' ? x.column : x.column && x.column.from,\n      to: x.column && x.column.to\n    },\n    value: {\n      from: typeof x.value === 'function' ? x.value : x.value && x.value.from,\n      to: x.value && x.value.to\n    },\n    row: {\n      from: typeof x.row === 'function' ? x.row : x.row && x.row.from,\n      to: x.row && x.row.to\n    }\n  }\n}\n\nfunction parseUrl(url) {\n  if (!url || typeof url !== 'string')\n    return { url: { searchParams: new Map() } }\n\n  let host = url\n  host = host.slice(host.indexOf('://') + 3).split(/[?/]/)[0]\n  host = decodeURIComponent(host.slice(host.indexOf('@') + 1))\n\n  const urlObj = new URL(url.replace(host, host.split(',')[0]))\n\n  return {\n    url: {\n      username: decodeURIComponent(urlObj.username),\n      password: decodeURIComponent(urlObj.password),\n      host: urlObj.host,\n      hostname: urlObj.hostname,\n      port: urlObj.port,\n      pathname: urlObj.pathname,\n      searchParams: urlObj.searchParams\n    },\n    multihost: host.indexOf(',') > -1 && host\n  }\n}\n\nfunction osUsername() {\n  try {\n    return os__WEBPACK_IMPORTED_MODULE_0__.userInfo().username // eslint-disable-line\n  } catch (_) {\n    return process.env.USERNAME || process.env.USER || process.env.LOGNAME  // eslint-disable-line\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/large.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/large.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ largeObject)\n/* harmony export */ });\n/* harmony import */ var stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stream */ \"stream\");\n\n\nfunction largeObject(sql, oid, mode = 0x00020000 | 0x00040000) {\n  return new Promise(async(resolve, reject) => {\n    await sql.begin(async sql => {\n      let finish\n      !oid && ([{ oid }] = await sql`select lo_creat(-1) as oid`)\n      const [{ fd }] = await sql`select lo_open(${ oid }, ${ mode }) as fd`\n\n      const lo = {\n        writable,\n        readable,\n        close     : () => sql`select lo_close(${ fd })`.then(finish),\n        tell      : () => sql`select lo_tell64(${ fd })`,\n        read      : (x) => sql`select loread(${ fd }, ${ x }) as data`,\n        write     : (x) => sql`select lowrite(${ fd }, ${ x })`,\n        truncate  : (x) => sql`select lo_truncate64(${ fd }, ${ x })`,\n        seek      : (x, whence = 0) => sql`select lo_lseek64(${ fd }, ${ x }, ${ whence })`,\n        size      : () => sql`\n          select\n            lo_lseek64(${ fd }, location, 0) as position,\n            seek.size\n          from (\n            select\n              lo_lseek64($1, 0, 2) as size,\n              tell.location\n            from (select lo_tell64($1) as location) tell\n          ) seek\n        `\n      }\n\n      resolve(lo)\n\n      return new Promise(async r => finish = r)\n\n      async function readable({\n        highWaterMark = 2048 * 8,\n        start = 0,\n        end = Infinity\n      } = {}) {\n        let max = end - start\n        start && await lo.seek(start)\n        return new stream__WEBPACK_IMPORTED_MODULE_0__.Readable({\n          highWaterMark,\n          async read(size) {\n            const l = size > max ? size - max : size\n            max -= size\n            const [{ data }] = await lo.read(l)\n            this.push(data)\n            if (data.length < size)\n              this.push(null)\n          }\n        })\n      }\n\n      async function writable({\n        highWaterMark = 2048 * 8,\n        start = 0\n      } = {}) {\n        start && await lo.seek(start)\n        return new stream__WEBPACK_IMPORTED_MODULE_0__.Writable({\n          highWaterMark,\n          write(chunk, encoding, callback) {\n            lo.write(chunk).then(() => callback(), callback)\n          }\n        })\n      }\n    }).catch(reject)\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/large.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/query.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/query.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CLOSE: () => (/* binding */ CLOSE),\n/* harmony export */   Query: () => (/* binding */ Query)\n/* harmony export */ });\nconst originCache = new Map()\n    , originStackCache = new Map()\n    , originError = Symbol('OriginError')\n\nconst CLOSE = {}\nclass Query extends Promise {\n  constructor(strings, args, handler, canceller, options = {}) {\n    let resolve\n      , reject\n\n    super((a, b) => {\n      resolve = a\n      reject = b\n    })\n\n    this.tagged = Array.isArray(strings.raw)\n    this.strings = strings\n    this.args = args\n    this.handler = handler\n    this.canceller = canceller\n    this.options = options\n\n    this.state = null\n    this.statement = null\n\n    this.resolve = x => (this.active = false, resolve(x))\n    this.reject = x => (this.active = false, reject(x))\n\n    this.active = false\n    this.cancelled = null\n    this.executed = false\n    this.signature = ''\n\n    this[originError] = this.handler.debug\n      ? new Error()\n      : this.tagged && cachedError(this.strings)\n  }\n\n  get origin() {\n    return (this.handler.debug\n      ? this[originError].stack\n      : this.tagged && originStackCache.has(this.strings)\n        ? originStackCache.get(this.strings)\n        : originStackCache.set(this.strings, this[originError].stack).get(this.strings)\n    ) || ''\n  }\n\n  static get [Symbol.species]() {\n    return Promise\n  }\n\n  cancel() {\n    return this.canceller && (this.canceller(this), this.canceller = null)\n  }\n\n  simple() {\n    this.options.simple = true\n    this.options.prepare = false\n    return this\n  }\n\n  async readable() {\n    this.simple()\n    this.streaming = true\n    return this\n  }\n\n  async writable() {\n    this.simple()\n    this.streaming = true\n    return this\n  }\n\n  cursor(rows = 1, fn) {\n    this.options.simple = false\n    if (typeof rows === 'function') {\n      fn = rows\n      rows = 1\n    }\n\n    this.cursorRows = rows\n\n    if (typeof fn === 'function')\n      return (this.cursorFn = fn, this)\n\n    let prev\n    return {\n      [Symbol.asyncIterator]: () => ({\n        next: () => {\n          if (this.executed && !this.active)\n            return { done: true }\n\n          prev && prev()\n          const promise = new Promise((resolve, reject) => {\n            this.cursorFn = value => {\n              resolve({ value, done: false })\n              return new Promise(r => prev = r)\n            }\n            this.resolve = () => (this.active = false, resolve({ done: true }))\n            this.reject = x => (this.active = false, reject(x))\n          })\n          this.execute()\n          return promise\n        },\n        return() {\n          prev && prev(CLOSE)\n          return { done: true }\n        }\n      })\n    }\n  }\n\n  describe() {\n    this.options.simple = false\n    this.onlyDescribe = this.options.prepare = true\n    return this\n  }\n\n  stream() {\n    throw new Error('.stream has been renamed to .forEach')\n  }\n\n  forEach(fn) {\n    this.forEachFn = fn\n    this.handle()\n    return this\n  }\n\n  raw() {\n    this.isRaw = true\n    return this\n  }\n\n  values() {\n    this.isRaw = 'values'\n    return this\n  }\n\n  async handle() {\n    !this.executed && (this.executed = true) && await 1 && this.handler(this)\n  }\n\n  execute() {\n    this.handle()\n    return this\n  }\n\n  then() {\n    this.handle()\n    return super.then.apply(this, arguments)\n  }\n\n  catch() {\n    this.handle()\n    return super.catch.apply(this, arguments)\n  }\n\n  finally() {\n    this.handle()\n    return super.finally.apply(this, arguments)\n  }\n}\n\nfunction cachedError(xs) {\n  if (originCache.has(xs))\n    return originCache.get(xs)\n\n  const x = Error.stackTraceLimit\n  Error.stackTraceLimit = 4\n  originCache.set(xs, new Error())\n  Error.stackTraceLimit = x\n  return originCache.get(xs)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/query.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/queue.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/queue.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Queue);\n\nfunction Queue(initial = []) {\n  let xs = initial.slice()\n  let index = 0\n\n  return {\n    get length() {\n      return xs.length - index\n    },\n    remove: (x) => {\n      const index = xs.indexOf(x)\n      return index === -1\n        ? null\n        : (xs.splice(index, 1), x)\n    },\n    push: (x) => (xs.push(x), x),\n    shift: () => {\n      const out = xs[index++]\n\n      if (index === xs.length) {\n        index = 0\n        xs = []\n      } else {\n        xs[index - 1] = undefined\n      }\n\n      return out\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcG9zdGdyZXMvc3JjL3F1ZXVlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxLQUFLOztBQUVwQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcVExEIFN5c3RlbVxcZW5oYW5jZWQtaWVsdHMtc3lzdGVtXFxub2RlX21vZHVsZXNcXHBvc3RncmVzXFxzcmNcXHF1ZXVlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFF1ZXVlXG5cbmZ1bmN0aW9uIFF1ZXVlKGluaXRpYWwgPSBbXSkge1xuICBsZXQgeHMgPSBpbml0aWFsLnNsaWNlKClcbiAgbGV0IGluZGV4ID0gMFxuXG4gIHJldHVybiB7XG4gICAgZ2V0IGxlbmd0aCgpIHtcbiAgICAgIHJldHVybiB4cy5sZW5ndGggLSBpbmRleFxuICAgIH0sXG4gICAgcmVtb3ZlOiAoeCkgPT4ge1xuICAgICAgY29uc3QgaW5kZXggPSB4cy5pbmRleE9mKHgpXG4gICAgICByZXR1cm4gaW5kZXggPT09IC0xXG4gICAgICAgID8gbnVsbFxuICAgICAgICA6ICh4cy5zcGxpY2UoaW5kZXgsIDEpLCB4KVxuICAgIH0sXG4gICAgcHVzaDogKHgpID0+ICh4cy5wdXNoKHgpLCB4KSxcbiAgICBzaGlmdDogKCkgPT4ge1xuICAgICAgY29uc3Qgb3V0ID0geHNbaW5kZXgrK11cblxuICAgICAgaWYgKGluZGV4ID09PSB4cy5sZW5ndGgpIHtcbiAgICAgICAgaW5kZXggPSAwXG4gICAgICAgIHhzID0gW11cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHhzW2luZGV4IC0gMV0gPSB1bmRlZmluZWRcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIG91dFxuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/queue.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/result.js":
/*!*********************************************!*\
  !*** ./node_modules/postgres/src/result.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Result)\n/* harmony export */ });\nclass Result extends Array {\n  constructor() {\n    super()\n    Object.defineProperties(this, {\n      count: { value: null, writable: true },\n      state: { value: null, writable: true },\n      command: { value: null, writable: true },\n      columns: { value: null, writable: true },\n      statement: { value: null, writable: true }\n    })\n  }\n\n  static get [Symbol.species]() {\n    return Array\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcG9zdGdyZXMvc3JjL3Jlc3VsdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSxlQUFlLDZCQUE2QjtBQUM1QyxlQUFlLDZCQUE2QjtBQUM1QyxpQkFBaUIsNkJBQTZCO0FBQzlDLGlCQUFpQiw2QkFBNkI7QUFDOUMsbUJBQW1CO0FBQ25CLEtBQUs7QUFDTDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXFRMRCBTeXN0ZW1cXGVuaGFuY2VkLWllbHRzLXN5c3RlbVxcbm9kZV9tb2R1bGVzXFxwb3N0Z3Jlc1xcc3JjXFxyZXN1bHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgY2xhc3MgUmVzdWx0IGV4dGVuZHMgQXJyYXkge1xuICBjb25zdHJ1Y3RvcigpIHtcbiAgICBzdXBlcigpXG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnRpZXModGhpcywge1xuICAgICAgY291bnQ6IHsgdmFsdWU6IG51bGwsIHdyaXRhYmxlOiB0cnVlIH0sXG4gICAgICBzdGF0ZTogeyB2YWx1ZTogbnVsbCwgd3JpdGFibGU6IHRydWUgfSxcbiAgICAgIGNvbW1hbmQ6IHsgdmFsdWU6IG51bGwsIHdyaXRhYmxlOiB0cnVlIH0sXG4gICAgICBjb2x1bW5zOiB7IHZhbHVlOiBudWxsLCB3cml0YWJsZTogdHJ1ZSB9LFxuICAgICAgc3RhdGVtZW50OiB7IHZhbHVlOiBudWxsLCB3cml0YWJsZTogdHJ1ZSB9XG4gICAgfSlcbiAgfVxuXG4gIHN0YXRpYyBnZXQgW1N5bWJvbC5zcGVjaWVzXSgpIHtcbiAgICByZXR1cm4gQXJyYXlcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/result.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/subscribe.js":
/*!************************************************!*\
  !*** ./node_modules/postgres/src/subscribe.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Subscribe)\n/* harmony export */ });\nconst noop = () => { /* noop */ }\n\nfunction Subscribe(postgres, options) {\n  const subscribers = new Map()\n      , slot = 'postgresjs_' + Math.random().toString(36).slice(2)\n      , state = {}\n\n  let connection\n    , stream\n    , ended = false\n\n  const sql = subscribe.sql = postgres({\n    ...options,\n    transform: { column: {}, value: {}, row: {} },\n    max: 1,\n    fetch_types: false,\n    idle_timeout: null,\n    max_lifetime: null,\n    connection: {\n      ...options.connection,\n      replication: 'database'\n    },\n    onclose: async function() {\n      if (ended)\n        return\n      stream = null\n      state.pid = state.secret = undefined\n      connected(await init(sql, slot, options.publications))\n      subscribers.forEach(event => event.forEach(({ onsubscribe }) => onsubscribe()))\n    },\n    no_subscribe: true\n  })\n\n  const end = sql.end\n      , close = sql.close\n\n  sql.end = async() => {\n    ended = true\n    stream && (await new Promise(r => (stream.once('close', r), stream.end())))\n    return end()\n  }\n\n  sql.close = async() => {\n    stream && (await new Promise(r => (stream.once('close', r), stream.end())))\n    return close()\n  }\n\n  return subscribe\n\n  async function subscribe(event, fn, onsubscribe = noop, onerror = noop) {\n    event = parseEvent(event)\n\n    if (!connection)\n      connection = init(sql, slot, options.publications)\n\n    const subscriber = { fn, onsubscribe }\n    const fns = subscribers.has(event)\n      ? subscribers.get(event).add(subscriber)\n      : subscribers.set(event, new Set([subscriber])).get(event)\n\n    const unsubscribe = () => {\n      fns.delete(subscriber)\n      fns.size === 0 && subscribers.delete(event)\n    }\n\n    return connection.then(x => {\n      connected(x)\n      onsubscribe()\n      stream && stream.on('error', onerror)\n      return { unsubscribe, state, sql }\n    })\n  }\n\n  function connected(x) {\n    stream = x.stream\n    state.pid = x.state.pid\n    state.secret = x.state.secret\n  }\n\n  async function init(sql, slot, publications) {\n    if (!publications)\n      throw new Error('Missing publication names')\n\n    const xs = await sql.unsafe(\n      `CREATE_REPLICATION_SLOT ${ slot } TEMPORARY LOGICAL pgoutput NOEXPORT_SNAPSHOT`\n    )\n\n    const [x] = xs\n\n    const stream = await sql.unsafe(\n      `START_REPLICATION SLOT ${ slot } LOGICAL ${\n        x.consistent_point\n      } (proto_version '1', publication_names '${ publications }')`\n    ).writable()\n\n    const state = {\n      lsn: Buffer.concat(x.consistent_point.split('/').map(x => Buffer.from(('00000000' + x).slice(-8), 'hex')))\n    }\n\n    stream.on('data', data)\n    stream.on('error', error)\n    stream.on('close', sql.close)\n\n    return { stream, state: xs.state }\n\n    function error(e) {\n      console.error('Unexpected error during logical streaming - reconnecting', e) // eslint-disable-line\n    }\n\n    function data(x) {\n      if (x[0] === 0x77) {\n        parse(x.subarray(25), state, sql.options.parsers, handle, options.transform)\n      } else if (x[0] === 0x6b && x[17]) {\n        state.lsn = x.subarray(1, 9)\n        pong()\n      }\n    }\n\n    function handle(a, b) {\n      const path = b.relation.schema + '.' + b.relation.table\n      call('*', a, b)\n      call('*:' + path, a, b)\n      b.relation.keys.length && call('*:' + path + '=' + b.relation.keys.map(x => a[x.name]), a, b)\n      call(b.command, a, b)\n      call(b.command + ':' + path, a, b)\n      b.relation.keys.length && call(b.command + ':' + path + '=' + b.relation.keys.map(x => a[x.name]), a, b)\n    }\n\n    function pong() {\n      const x = Buffer.alloc(34)\n      x[0] = 'r'.charCodeAt(0)\n      x.fill(state.lsn, 1)\n      x.writeBigInt64BE(BigInt(Date.now() - Date.UTC(2000, 0, 1)) * BigInt(1000), 25)\n      stream.write(x)\n    }\n  }\n\n  function call(x, a, b) {\n    subscribers.has(x) && subscribers.get(x).forEach(({ fn }) => fn(a, b, x))\n  }\n}\n\nfunction Time(x) {\n  return new Date(Date.UTC(2000, 0, 1) + Number(x / BigInt(1000)))\n}\n\nfunction parse(x, state, parsers, handle, transform) {\n  const char = (acc, [k, v]) => (acc[k.charCodeAt(0)] = v, acc)\n\n  Object.entries({\n    R: x => {  // Relation\n      let i = 1\n      const r = state[x.readUInt32BE(i)] = {\n        schema: x.toString('utf8', i += 4, i = x.indexOf(0, i)) || 'pg_catalog',\n        table: x.toString('utf8', i + 1, i = x.indexOf(0, i + 1)),\n        columns: Array(x.readUInt16BE(i += 2)),\n        keys: []\n      }\n      i += 2\n\n      let columnIndex = 0\n        , column\n\n      while (i < x.length) {\n        column = r.columns[columnIndex++] = {\n          key: x[i++],\n          name: transform.column.from\n            ? transform.column.from(x.toString('utf8', i, i = x.indexOf(0, i)))\n            : x.toString('utf8', i, i = x.indexOf(0, i)),\n          type: x.readUInt32BE(i += 1),\n          parser: parsers[x.readUInt32BE(i)],\n          atttypmod: x.readUInt32BE(i += 4)\n        }\n\n        column.key && r.keys.push(column)\n        i += 4\n      }\n    },\n    Y: () => { /* noop */ }, // Type\n    O: () => { /* noop */ }, // Origin\n    B: x => { // Begin\n      state.date = Time(x.readBigInt64BE(9))\n      state.lsn = x.subarray(1, 9)\n    },\n    I: x => { // Insert\n      let i = 1\n      const relation = state[x.readUInt32BE(i)]\n      const { row } = tuples(x, relation.columns, i += 7, transform)\n\n      handle(row, {\n        command: 'insert',\n        relation\n      })\n    },\n    D: x => { // Delete\n      let i = 1\n      const relation = state[x.readUInt32BE(i)]\n      i += 4\n      const key = x[i] === 75\n      handle(key || x[i] === 79\n        ? tuples(x, relation.columns, i += 3, transform).row\n        : null\n      , {\n        command: 'delete',\n        relation,\n        key\n      })\n    },\n    U: x => { // Update\n      let i = 1\n      const relation = state[x.readUInt32BE(i)]\n      i += 4\n      const key = x[i] === 75\n      const xs = key || x[i] === 79\n        ? tuples(x, relation.columns, i += 3, transform)\n        : null\n\n      xs && (i = xs.i)\n\n      const { row } = tuples(x, relation.columns, i + 3, transform)\n\n      handle(row, {\n        command: 'update',\n        relation,\n        key,\n        old: xs && xs.row\n      })\n    },\n    T: () => { /* noop */ }, // Truncate,\n    C: () => { /* noop */ }  // Commit\n  }).reduce(char, {})[x[0]](x)\n}\n\nfunction tuples(x, columns, xi, transform) {\n  let type\n    , column\n    , value\n\n  const row = transform.raw ? new Array(columns.length) : {}\n  for (let i = 0; i < columns.length; i++) {\n    type = x[xi++]\n    column = columns[i]\n    value = type === 110 // n\n      ? null\n      : type === 117 // u\n        ? undefined\n        : column.parser === undefined\n          ? x.toString('utf8', xi + 4, xi += 4 + x.readUInt32BE(xi))\n          : column.parser.array === true\n            ? column.parser(x.toString('utf8', xi + 5, xi += 4 + x.readUInt32BE(xi)))\n            : column.parser(x.toString('utf8', xi + 4, xi += 4 + x.readUInt32BE(xi)))\n\n    transform.raw\n      ? (row[i] = transform.raw === true\n        ? value\n        : transform.value.from ? transform.value.from(value, column) : value)\n      : (row[column.name] = transform.value.from\n        ? transform.value.from(value, column)\n        : value\n      )\n  }\n\n  return { i: xi, row: transform.row.from ? transform.row.from(row) : row }\n}\n\nfunction parseEvent(x) {\n  const xs = x.match(/^(\\*|insert|update|delete)?:?([^.]+?\\.?[^=]+)?=?(.+)?/i) || []\n\n  if (!xs)\n    throw new Error('Malformed subscribe pattern: ' + x)\n\n  const [, command, path, key] = xs\n\n  return (command || '*')\n       + (path ? ':' + (path.indexOf('.') === -1 ? 'public.' + path : path) : '')\n       + (key ? '=' + key : '')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/subscribe.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/postgres/src/types.js":
/*!********************************************!*\
  !*** ./node_modules/postgres/src/types.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Builder: () => (/* binding */ Builder),\n/* harmony export */   END: () => (/* binding */ END),\n/* harmony export */   Identifier: () => (/* binding */ Identifier),\n/* harmony export */   Parameter: () => (/* binding */ Parameter),\n/* harmony export */   arrayParser: () => (/* binding */ arrayParser),\n/* harmony export */   arraySerializer: () => (/* binding */ arraySerializer),\n/* harmony export */   camel: () => (/* binding */ camel),\n/* harmony export */   escapeIdentifier: () => (/* binding */ escapeIdentifier),\n/* harmony export */   fromCamel: () => (/* binding */ fromCamel),\n/* harmony export */   fromKebab: () => (/* binding */ fromKebab),\n/* harmony export */   fromPascal: () => (/* binding */ fromPascal),\n/* harmony export */   handleValue: () => (/* binding */ handleValue),\n/* harmony export */   inferType: () => (/* binding */ inferType),\n/* harmony export */   kebab: () => (/* binding */ kebab),\n/* harmony export */   mergeUserTypes: () => (/* binding */ mergeUserTypes),\n/* harmony export */   parsers: () => (/* binding */ parsers),\n/* harmony export */   pascal: () => (/* binding */ pascal),\n/* harmony export */   serializers: () => (/* binding */ serializers),\n/* harmony export */   stringify: () => (/* binding */ stringify),\n/* harmony export */   toCamel: () => (/* binding */ toCamel),\n/* harmony export */   toKebab: () => (/* binding */ toKebab),\n/* harmony export */   toPascal: () => (/* binding */ toPascal),\n/* harmony export */   types: () => (/* binding */ types)\n/* harmony export */ });\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./query.js */ \"(rsc)/./node_modules/postgres/src/query.js\");\n/* harmony import */ var _errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./errors.js */ \"(rsc)/./node_modules/postgres/src/errors.js\");\n\n\n\nconst types = {\n  string: {\n    to: 25,\n    from: null,             // defaults to string\n    serialize: x => '' + x\n  },\n  number: {\n    to: 0,\n    from: [21, 23, 26, 700, 701],\n    serialize: x => '' + x,\n    parse: x => +x\n  },\n  json: {\n    to: 114,\n    from: [114, 3802],\n    serialize: x => JSON.stringify(x),\n    parse: x => JSON.parse(x)\n  },\n  boolean: {\n    to: 16,\n    from: 16,\n    serialize: x => x === true ? 't' : 'f',\n    parse: x => x === 't'\n  },\n  date: {\n    to: 1184,\n    from: [1082, 1114, 1184],\n    serialize: x => (x instanceof Date ? x : new Date(x)).toISOString(),\n    parse: x => new Date(x)\n  },\n  bytea: {\n    to: 17,\n    from: 17,\n    serialize: x => '\\\\x' + Buffer.from(x).toString('hex'),\n    parse: x => Buffer.from(x.slice(2), 'hex')\n  }\n}\n\nclass NotTagged { then() { notTagged() } catch() { notTagged() } finally() { notTagged() }}\n\nclass Identifier extends NotTagged {\n  constructor(value) {\n    super()\n    this.value = escapeIdentifier(value)\n  }\n}\n\nclass Parameter extends NotTagged {\n  constructor(value, type, array) {\n    super()\n    this.value = value\n    this.type = type\n    this.array = array\n  }\n}\n\nclass Builder extends NotTagged {\n  constructor(first, rest) {\n    super()\n    this.first = first\n    this.rest = rest\n  }\n\n  build(before, parameters, types, options) {\n    const keyword = builders.map(([x, fn]) => ({ fn, i: before.search(x) })).sort((a, b) => a.i - b.i).pop()\n    return keyword.i === -1\n      ? escapeIdentifiers(this.first, options)\n      : keyword.fn(this.first, this.rest, parameters, types, options)\n  }\n}\n\nfunction handleValue(x, parameters, types, options) {\n  let value = x instanceof Parameter ? x.value : x\n  if (value === undefined) {\n    x instanceof Parameter\n      ? x.value = options.transform.undefined\n      : value = x = options.transform.undefined\n\n    if (value === undefined)\n      throw _errors_js__WEBPACK_IMPORTED_MODULE_1__.Errors.generic('UNDEFINED_VALUE', 'Undefined values are not allowed')\n  }\n\n  return '$' + (types.push(\n    x instanceof Parameter\n      ? (parameters.push(x.value), x.array\n        ? x.array[x.type || inferType(x.value)] || x.type || firstIsString(x.value)\n        : x.type\n      )\n      : (parameters.push(x), inferType(x))\n  ))\n}\n\nconst defaultHandlers = typeHandlers(types)\n\nfunction stringify(q, string, value, parameters, types, options) { // eslint-disable-line\n  for (let i = 1; i < q.strings.length; i++) {\n    string += (stringifyValue(string, value, parameters, types, options)) + q.strings[i]\n    value = q.args[i]\n  }\n\n  return string\n}\n\nfunction stringifyValue(string, value, parameters, types, o) {\n  return (\n    value instanceof Builder ? value.build(string, parameters, types, o) :\n    value instanceof _query_js__WEBPACK_IMPORTED_MODULE_0__.Query ? fragment(value, parameters, types, o) :\n    value instanceof Identifier ? value.value :\n    value && value[0] instanceof _query_js__WEBPACK_IMPORTED_MODULE_0__.Query ? value.reduce((acc, x) => acc + ' ' + fragment(x, parameters, types, o), '') :\n    handleValue(value, parameters, types, o)\n  )\n}\n\nfunction fragment(q, parameters, types, options) {\n  q.fragment = true\n  return stringify(q, q.strings[0], q.args[0], parameters, types, options)\n}\n\nfunction valuesBuilder(first, parameters, types, columns, options) {\n  return first.map(row =>\n    '(' + columns.map(column =>\n      stringifyValue('values', row[column], parameters, types, options)\n    ).join(',') + ')'\n  ).join(',')\n}\n\nfunction values(first, rest, parameters, types, options) {\n  const multi = Array.isArray(first[0])\n  const columns = rest.length ? rest.flat() : Object.keys(multi ? first[0] : first)\n  return valuesBuilder(multi ? first : [first], parameters, types, columns, options)\n}\n\nfunction select(first, rest, parameters, types, options) {\n  typeof first === 'string' && (first = [first].concat(rest))\n  if (Array.isArray(first))\n    return escapeIdentifiers(first, options)\n\n  let value\n  const columns = rest.length ? rest.flat() : Object.keys(first)\n  return columns.map(x => {\n    value = first[x]\n    return (\n      value instanceof _query_js__WEBPACK_IMPORTED_MODULE_0__.Query ? fragment(value, parameters, types, options) :\n      value instanceof Identifier ? value.value :\n      handleValue(value, parameters, types, options)\n    ) + ' as ' + escapeIdentifier(options.transform.column.to ? options.transform.column.to(x) : x)\n  }).join(',')\n}\n\nconst builders = Object.entries({\n  values,\n  in: (...xs) => {\n    const x = values(...xs)\n    return x === '()' ? '(null)' : x\n  },\n  select,\n  as: select,\n  returning: select,\n  '\\\\(': select,\n\n  update(first, rest, parameters, types, options) {\n    return (rest.length ? rest.flat() : Object.keys(first)).map(x =>\n      escapeIdentifier(options.transform.column.to ? options.transform.column.to(x) : x) +\n      '=' + stringifyValue('values', first[x], parameters, types, options)\n    )\n  },\n\n  insert(first, rest, parameters, types, options) {\n    const columns = rest.length ? rest.flat() : Object.keys(Array.isArray(first) ? first[0] : first)\n    return '(' + escapeIdentifiers(columns, options) + ')values' +\n    valuesBuilder(Array.isArray(first) ? first : [first], parameters, types, columns, options)\n  }\n}).map(([x, fn]) => ([new RegExp('((?:^|[\\\\s(])' + x + '(?:$|[\\\\s(]))(?![\\\\s\\\\S]*\\\\1)', 'i'), fn]))\n\nfunction notTagged() {\n  throw _errors_js__WEBPACK_IMPORTED_MODULE_1__.Errors.generic('NOT_TAGGED_CALL', 'Query not called as a tagged template literal')\n}\n\nconst serializers = defaultHandlers.serializers\nconst parsers = defaultHandlers.parsers\n\nconst END = {}\n\nfunction firstIsString(x) {\n  if (Array.isArray(x))\n    return firstIsString(x[0])\n  return typeof x === 'string' ? 1009 : 0\n}\n\nconst mergeUserTypes = function(types) {\n  const user = typeHandlers(types || {})\n  return {\n    serializers: Object.assign({}, serializers, user.serializers),\n    parsers: Object.assign({}, parsers, user.parsers)\n  }\n}\n\nfunction typeHandlers(types) {\n  return Object.keys(types).reduce((acc, k) => {\n    types[k].from && [].concat(types[k].from).forEach(x => acc.parsers[x] = types[k].parse)\n    if (types[k].serialize) {\n      acc.serializers[types[k].to] = types[k].serialize\n      types[k].from && [].concat(types[k].from).forEach(x => acc.serializers[x] = types[k].serialize)\n    }\n    return acc\n  }, { parsers: {}, serializers: {} })\n}\n\nfunction escapeIdentifiers(xs, { transform: { column } }) {\n  return xs.map(x => escapeIdentifier(column.to ? column.to(x) : x)).join(',')\n}\n\nconst escapeIdentifier = function escape(str) {\n  return '\"' + str.replace(/\"/g, '\"\"').replace(/\\./g, '\".\"') + '\"'\n}\n\nconst inferType = function inferType(x) {\n  return (\n    x instanceof Parameter ? x.type :\n    x instanceof Date ? 1184 :\n    x instanceof Uint8Array ? 17 :\n    (x === true || x === false) ? 16 :\n    typeof x === 'bigint' ? 20 :\n    Array.isArray(x) ? inferType(x[0]) :\n    0\n  )\n}\n\nconst escapeBackslash = /\\\\/g\nconst escapeQuote = /\"/g\n\nfunction arrayEscape(x) {\n  return x\n    .replace(escapeBackslash, '\\\\\\\\')\n    .replace(escapeQuote, '\\\\\"')\n}\n\nconst arraySerializer = function arraySerializer(xs, serializer, options, typarray) {\n  if (Array.isArray(xs) === false)\n    return xs\n\n  if (!xs.length)\n    return '{}'\n\n  const first = xs[0]\n  // Only _box (1020) has the ';' delimiter for arrays, all other types use the ',' delimiter\n  const delimiter = typarray === 1020 ? ';' : ','\n\n  if (Array.isArray(first) && !first.type)\n    return '{' + xs.map(x => arraySerializer(x, serializer, options, typarray)).join(delimiter) + '}'\n\n  return '{' + xs.map(x => {\n    if (x === undefined) {\n      x = options.transform.undefined\n      if (x === undefined)\n        throw _errors_js__WEBPACK_IMPORTED_MODULE_1__.Errors.generic('UNDEFINED_VALUE', 'Undefined values are not allowed')\n    }\n\n    return x === null\n      ? 'null'\n      : '\"' + arrayEscape(serializer ? serializer(x.type ? x.value : x) : '' + x) + '\"'\n  }).join(delimiter) + '}'\n}\n\nconst arrayParserState = {\n  i: 0,\n  char: null,\n  str: '',\n  quoted: false,\n  last: 0\n}\n\nconst arrayParser = function arrayParser(x, parser, typarray) {\n  arrayParserState.i = arrayParserState.last = 0\n  return arrayParserLoop(arrayParserState, x, parser, typarray)\n}\n\nfunction arrayParserLoop(s, x, parser, typarray) {\n  const xs = []\n  // Only _box (1020) has the ';' delimiter for arrays, all other types use the ',' delimiter\n  const delimiter = typarray === 1020 ? ';' : ','\n  for (; s.i < x.length; s.i++) {\n    s.char = x[s.i]\n    if (s.quoted) {\n      if (s.char === '\\\\') {\n        s.str += x[++s.i]\n      } else if (s.char === '\"') {\n        xs.push(parser ? parser(s.str) : s.str)\n        s.str = ''\n        s.quoted = x[s.i + 1] === '\"'\n        s.last = s.i + 2\n      } else {\n        s.str += s.char\n      }\n    } else if (s.char === '\"') {\n      s.quoted = true\n    } else if (s.char === '{') {\n      s.last = ++s.i\n      xs.push(arrayParserLoop(s, x, parser, typarray))\n    } else if (s.char === '}') {\n      s.quoted = false\n      s.last < s.i && xs.push(parser ? parser(x.slice(s.last, s.i)) : x.slice(s.last, s.i))\n      s.last = s.i + 1\n      break\n    } else if (s.char === delimiter && s.p !== '}' && s.p !== '\"') {\n      xs.push(parser ? parser(x.slice(s.last, s.i)) : x.slice(s.last, s.i))\n      s.last = s.i + 1\n    }\n    s.p = s.char\n  }\n  s.last < s.i && xs.push(parser ? parser(x.slice(s.last, s.i + 1)) : x.slice(s.last, s.i + 1))\n  return xs\n}\n\nconst toCamel = x => {\n  let str = x[0]\n  for (let i = 1; i < x.length; i++)\n    str += x[i] === '_' ? x[++i].toUpperCase() : x[i]\n  return str\n}\n\nconst toPascal = x => {\n  let str = x[0].toUpperCase()\n  for (let i = 1; i < x.length; i++)\n    str += x[i] === '_' ? x[++i].toUpperCase() : x[i]\n  return str\n}\n\nconst toKebab = x => x.replace(/_/g, '-')\n\nconst fromCamel = x => x.replace(/([A-Z])/g, '_$1').toLowerCase()\nconst fromPascal = x => (x.slice(0, 1) + x.slice(1).replace(/([A-Z])/g, '_$1')).toLowerCase()\nconst fromKebab = x => x.replace(/-/g, '_')\n\nfunction createJsonTransform(fn) {\n  return function jsonTransform(x, column) {\n    return typeof x === 'object' && x !== null && (column.type === 114 || column.type === 3802)\n      ? Array.isArray(x)\n        ? x.map(x => jsonTransform(x, column))\n        : Object.entries(x).reduce((acc, [k, v]) => Object.assign(acc, { [fn(k)]: jsonTransform(v, column) }), {})\n      : x\n  }\n}\n\ntoCamel.column = { from: toCamel }\ntoCamel.value = { from: createJsonTransform(toCamel) }\nfromCamel.column = { to: fromCamel }\n\nconst camel = { ...toCamel }\ncamel.column.to = fromCamel\n\ntoPascal.column = { from: toPascal }\ntoPascal.value = { from: createJsonTransform(toPascal) }\nfromPascal.column = { to: fromPascal }\n\nconst pascal = { ...toPascal }\npascal.column.to = fromPascal\n\ntoKebab.column = { from: toKebab }\ntoKebab.value = { from: createJsonTransform(toKebab) }\nfromKebab.column = { to: fromKebab }\n\nconst kebab = { ...toKebab }\nkebab.column.to = fromKebab\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/postgres/src/types.js\n");

/***/ })

};
;