import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { paymentTransactions, candidates } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';
import { checkFeatureAccess } from '@/lib/payments/utils';

const verifyPaymentSchema = z.object({
  transactionId: z.string().min(1),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { transactionId } = verifyPaymentSchema.parse(body);

    // Get transaction details
    const transaction = await db
      .select({
        transaction: paymentTransactions,
        candidate: candidates,
      })
      .from(paymentTransactions)
      .leftJoin(candidates, eq(paymentTransactions.candidateId, candidates.id))
      .where(
        and(
          eq(paymentTransactions.id, transactionId),
          eq(candidates.organizationId, session.user.organizationId)
        )
      )
      .limit(1);

    if (transaction.length === 0) {
      return NextResponse.json(
        { error: 'Transaction not found' },
        { status: 404 }
      );
    }

    const tx = transaction[0];

    // Check feature access
    const access = await checkFeatureAccess(
      tx.transaction.candidateId,
      tx.transaction.featureType as any,
      tx.transaction.resultId || undefined
    );

    return NextResponse.json({
      transactionId: tx.transaction.id,
      status: tx.transaction.status,
      amount: parseFloat(tx.transaction.amount),
      currency: tx.transaction.currency,
      gateway: tx.transaction.gateway,
      featureType: tx.transaction.featureType,
      resultId: tx.transaction.resultId,
      gatewayTransactionId: tx.transaction.gatewayTransactionId,
      createdAt: tx.transaction.createdAt,
      completedAt: tx.transaction.completedAt,
      hasAccess: access.hasAccess,
      accessType: access.accessType,
      expiresAt: access.expiresAt,
      daysRemaining: access.daysRemaining,
      candidate: {
        id: tx.candidate?.id,
        fullName: tx.candidate?.fullName,
        email: tx.candidate?.email,
      },
    });
  } catch (error) {
    console.error('Payment verification error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Payment verification failed' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const candidateId = searchParams.get('candidateId');
    const featureType = searchParams.get('featureType');
    const resultId = searchParams.get('resultId');

    if (!candidateId || !featureType) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Verify candidate belongs to organization
    const candidate = await db
      .select()
      .from(candidates)
      .where(
        and(
          eq(candidates.id, candidateId),
          eq(candidates.organizationId, session.user.organizationId)
        )
      )
      .limit(1);

    if (candidate.length === 0) {
      return NextResponse.json(
        { error: 'Candidate not found' },
        { status: 404 }
      );
    }

    // Check feature access
    const access = await checkFeatureAccess(
      candidateId,
      featureType as any,
      resultId || undefined
    );

    return NextResponse.json({
      candidateId,
      featureType,
      resultId,
      hasAccess: access.hasAccess,
      accessType: access.accessType,
      expiresAt: access.expiresAt,
      daysRemaining: access.daysRemaining,
    });
  } catch (error) {
    console.error('Access check error:', error);
    return NextResponse.json(
      { error: 'Access check failed' },
      { status: 500 }
    );
  }
}
