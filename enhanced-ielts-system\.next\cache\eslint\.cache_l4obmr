[{"C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(auth)\\login\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(public)\\layout.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(public)\\search\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(public)\\verify\\[serial]\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\candidates\\page.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\dashboard\\page.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\layout.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\payments\\page.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\results\\page.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\ai\\feedback\\route.ts": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\candidates\\route.ts": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\certificates\\download\\[id]\\route.ts": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\certificates\\generate\\route.ts": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\certificates\\route.ts": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\master\\organizations\\route.ts": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\initiate\\route.ts": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\manual\\route.ts": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\manual\\[id]\\approve\\route.ts": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\verify\\route.ts": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\webhook\\click\\route.ts": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\webhook\\payme\\route.ts": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\public\\validate-search\\route.ts": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\test-registrations\\route.ts": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\test-results\\route.ts": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\test-results\\[id]\\route.ts": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\checker\\dashboard\\page.tsx": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\checker\\layout.tsx": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\layout.tsx": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\master\\dashboard\\page.tsx": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\master\\layout.tsx": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\master\\organizations\\page.tsx": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\page.tsx": "33", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\admin\\manual-payment-approval-modal.tsx": "34", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\admin\\manual-payments-list.tsx": "35", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\admin\\payment-statistics.tsx": "36", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\forms\\test-registration-modal.tsx": "37", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\forms\\test-result-entry-modal.tsx": "38", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\layout\\header.tsx": "39", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\layout\\sidebar.tsx": "40", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\paywall\\manual-payment-modal.tsx": "41", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\paywall\\payment-gateway-selector.tsx": "42", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\paywall\\paywall-overlay.tsx": "43", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\public-results-interface.tsx": "44", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\public-search-form.tsx": "45", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\search-results.tsx": "46", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\tabs\\certificate-tab.tsx": "47", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\tabs\\feedback-tab.tsx": "48", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\tabs\\progress-tab.tsx": "49", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\tabs\\results-tab.tsx": "50", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\results\\feedback-tab.tsx": "51", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\candidate-card.tsx": "52", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\certificate-verification.tsx": "53", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\create-candidate-modal.tsx": "54", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\create-organization-modal.tsx": "55", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\organization-card.tsx": "56", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\recent-activity.tsx": "57", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\recent-results-table.tsx": "58", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\stats-card.tsx": "59", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\test-results-table.tsx": "60", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\ui\\badge.tsx": "61", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\ui\\button.tsx": "62", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\ui\\input.tsx": "63", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\ui\\modal.tsx": "64", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\ui\\tabs.tsx": "65", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\hooks\\use-paywall.ts": "66", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\ai\\claude.ts": "67", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\auth\\config.ts": "68", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\certificates\\generator.ts": "69", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\db\\index.ts": "70", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\db\\schema.ts": "71", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\payments\\click.ts": "72", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\payments\\payme.ts": "73", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\payments\\types.ts": "74", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\payments\\utils.ts": "75", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\utils\\cn.ts": "76", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\utils\\format.ts": "77", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\utils\\ielts-scoring.ts": "78", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\utils.ts": "79", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\middleware.ts": "80", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\types\\auth.ts": "81", "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\types\\database.ts": "82"}, {"size": 2688, "mtime": 1748590343221, "results": "83", "hashOfConfig": "84"}, {"size": 632, "mtime": 1748596745656, "results": "85", "hashOfConfig": "84"}, {"size": 7094, "mtime": 1748599759950, "results": "86", "hashOfConfig": "84"}, {"size": 2318, "mtime": 1748599822012, "results": "87", "hashOfConfig": "84"}, {"size": 6931, "mtime": 1748592228236, "results": "88", "hashOfConfig": "84"}, {"size": 5311, "mtime": 1748599855579, "results": "89", "hashOfConfig": "84"}, {"size": 794, "mtime": 1748591279344, "results": "90", "hashOfConfig": "84"}, {"size": 5570, "mtime": 1748595876229, "results": "91", "hashOfConfig": "84"}, {"size": 6342, "mtime": 1748595273002, "results": "92", "hashOfConfig": "84"}, {"size": 5323, "mtime": 1748598403183, "results": "93", "hashOfConfig": "84"}, {"size": 86, "mtime": 1748590319983, "results": "94", "hashOfConfig": "84"}, {"size": 4534, "mtime": 1748592287426, "results": "95", "hashOfConfig": "84"}, {"size": 1629, "mtime": 1748598701993, "results": "96", "hashOfConfig": "84"}, {"size": 979, "mtime": 1748598689578, "results": "97", "hashOfConfig": "84"}, {"size": 1415, "mtime": 1748598712698, "results": "98", "hashOfConfig": "84"}, {"size": 4726, "mtime": 1748592201663, "results": "99", "hashOfConfig": "84"}, {"size": 3842, "mtime": 1748595640243, "results": "100", "hashOfConfig": "84"}, {"size": 5582, "mtime": 1748595826918, "results": "101", "hashOfConfig": "84"}, {"size": 5394, "mtime": 1748595848932, "results": "102", "hashOfConfig": "84"}, {"size": 4326, "mtime": 1748595684952, "results": "103", "hashOfConfig": "84"}, {"size": 1963, "mtime": 1748595654102, "results": "104", "hashOfConfig": "84"}, {"size": 2154, "mtime": 1748595666207, "results": "105", "hashOfConfig": "84"}, {"size": 1690, "mtime": 1748596472498, "results": "106", "hashOfConfig": "84"}, {"size": 4480, "mtime": 1748595144686, "results": "107", "hashOfConfig": "84"}, {"size": 6816, "mtime": 1748595168724, "results": "108", "hashOfConfig": "84"}, {"size": 9295, "mtime": 1748595199955, "results": "109", "hashOfConfig": "84"}, {"size": 7105, "mtime": 1748592330592, "results": "110", "hashOfConfig": "84"}, {"size": 829, "mtime": 1748592301885, "results": "111", "hashOfConfig": "84"}, {"size": 689, "mtime": 1748589477060, "results": "112", "hashOfConfig": "84"}, {"size": 4584, "mtime": 1748591436418, "results": "113", "hashOfConfig": "84"}, {"size": 760, "mtime": 1748591414617, "results": "114", "hashOfConfig": "84"}, {"size": 5933, "mtime": 1748592047700, "results": "115", "hashOfConfig": "84"}, {"size": 3239, "mtime": 1748591521165, "results": "116", "hashOfConfig": "84"}, {"size": 12129, "mtime": 1748599660226, "results": "117", "hashOfConfig": "84"}, {"size": 9833, "mtime": 1748599624676, "results": "118", "hashOfConfig": "84"}, {"size": 2733, "mtime": 1748599550691, "results": "119", "hashOfConfig": "84"}, {"size": 5077, "mtime": 1748595018253, "results": "120", "hashOfConfig": "84"}, {"size": 13531, "mtime": 1748595091272, "results": "121", "hashOfConfig": "84"}, {"size": 1915, "mtime": 1748591291653, "results": "122", "hashOfConfig": "84"}, {"size": 2869, "mtime": 1748591305925, "results": "123", "hashOfConfig": "84"}, {"size": 12397, "mtime": 1748599672678, "results": "124", "hashOfConfig": "84"}, {"size": 8198, "mtime": 1748595745684, "results": "125", "hashOfConfig": "84"}, {"size": 9618, "mtime": 1748599685038, "results": "126", "hashOfConfig": "84"}, {"size": 8858, "mtime": 1748599363276, "results": "127", "hashOfConfig": "84"}, {"size": 5219, "mtime": 1748596458167, "results": "128", "hashOfConfig": "84"}, {"size": 5907, "mtime": 1748596497149, "results": "129", "hashOfConfig": "84"}, {"size": 23391, "mtime": 1748598872054, "results": "130", "hashOfConfig": "84"}, {"size": 20177, "mtime": 1748598618695, "results": "131", "hashOfConfig": "84"}, {"size": 12498, "mtime": 1748596635694, "results": "132", "hashOfConfig": "84"}, {"size": 10628, "mtime": 1748596584919, "results": "133", "hashOfConfig": "84"}, {"size": 8514, "mtime": 1748598439482, "results": "134", "hashOfConfig": "84"}, {"size": 2789, "mtime": 1748592244354, "results": "135", "hashOfConfig": "84"}, {"size": 6184, "mtime": 1748598749414, "results": "136", "hashOfConfig": "84"}, {"size": 5336, "mtime": 1748592265252, "results": "137", "hashOfConfig": "84"}, {"size": 6344, "mtime": 1748592074774, "results": "138", "hashOfConfig": "84"}, {"size": 3451, "mtime": 1748591587164, "results": "139", "hashOfConfig": "84"}, {"size": 3628, "mtime": 1748591353212, "results": "140", "hashOfConfig": "84"}, {"size": 3593, "mtime": 1748592347859, "results": "141", "hashOfConfig": "84"}, {"size": 899, "mtime": 1748591338477, "results": "142", "hashOfConfig": "84"}, {"size": 8684, "mtime": 1748595051089, "results": "143", "hashOfConfig": "84"}, {"size": 1644, "mtime": 1748595236407, "results": "144", "hashOfConfig": "84"}, {"size": 1659, "mtime": 1748595307811, "results": "145", "hashOfConfig": "84"}, {"size": 970, "mtime": 1748595325848, "results": "146", "hashOfConfig": "84"}, {"size": 2589, "mtime": 1748592088883, "results": "147", "hashOfConfig": "84"}, {"size": 2705, "mtime": 1748596545536, "results": "148", "hashOfConfig": "84"}, {"size": 1903, "mtime": 1748599454348, "results": "149", "hashOfConfig": "84"}, {"size": 4162, "mtime": 1748598378209, "results": "150", "hashOfConfig": "84"}, {"size": 2583, "mtime": 1748591572133, "results": "151", "hashOfConfig": "84"}, {"size": 9847, "mtime": 1748598678197, "results": "152", "hashOfConfig": "84"}, {"size": 590, "mtime": 1748591843246, "results": "153", "hashOfConfig": "84"}, {"size": 15798, "mtime": 1748596011559, "results": "154", "hashOfConfig": "84"}, {"size": 6547, "mtime": 1748595529569, "results": "155", "hashOfConfig": "84"}, {"size": 8635, "mtime": 1748595561703, "results": "156", "hashOfConfig": "84"}, {"size": 6176, "mtime": 1748595587970, "results": "157", "hashOfConfig": "84"}, {"size": 10037, "mtime": 1748595622006, "results": "158", "hashOfConfig": "84"}, {"size": 169, "mtime": 1748590257309, "results": "159", "hashOfConfig": "84"}, {"size": 985, "mtime": 1748599541450, "results": "160", "hashOfConfig": "84"}, {"size": 7195, "mtime": 1748595125726, "results": "161", "hashOfConfig": "84"}, {"size": 72, "mtime": 1748599409907, "results": "162", "hashOfConfig": "84"}, {"size": 1448, "mtime": 1748590331310, "results": "163", "hashOfConfig": "84"}, {"size": 817, "mtime": 1748591559355, "results": "164", "hashOfConfig": "84"}, {"size": 2916, "mtime": 1748590220283, "results": "165", "hashOfConfig": "84"}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "19nb1km", {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 35, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(auth)\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(public)\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(public)\\search\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\(public)\\verify\\[serial]\\page.tsx", ["412"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\candidates\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\payments\\page.tsx", ["413", "414"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\admin\\results\\page.tsx", ["415"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\ai\\feedback\\route.ts", ["416", "417"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\candidates\\route.ts", ["418", "419", "420"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\certificates\\download\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\certificates\\generate\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\certificates\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\master\\organizations\\route.ts", ["421", "422", "423"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\initiate\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\manual\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\manual\\[id]\\approve\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\verify\\route.ts", ["424", "425"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\webhook\\click\\route.ts", ["426"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\payments\\webhook\\payme\\route.ts", ["427"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\public\\validate-search\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\test-registrations\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\test-results\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\api\\test-results\\[id]\\route.ts", ["428"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\checker\\dashboard\\page.tsx", ["429"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\checker\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\master\\dashboard\\page.tsx", ["430"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\master\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\master\\organizations\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\admin\\manual-payment-approval-modal.tsx", ["431", "432", "433", "434"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\admin\\manual-payments-list.tsx", ["435", "436", "437", "438"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\admin\\payment-statistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\forms\\test-registration-modal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\forms\\test-result-entry-modal.tsx", ["439"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\layout\\sidebar.tsx", ["440"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\paywall\\manual-payment-modal.tsx", ["441", "442"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\paywall\\payment-gateway-selector.tsx", ["443"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\paywall\\paywall-overlay.tsx", ["444", "445"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\public-results-interface.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\public-search-form.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\search-results.tsx", ["446", "447"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\tabs\\certificate-tab.tsx", ["448", "449", "450"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\tabs\\feedback-tab.tsx", ["451", "452"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\tabs\\progress-tab.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\public\\tabs\\results-tab.tsx", ["453", "454", "455", "456", "457"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\results\\feedback-tab.tsx", ["458", "459"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\candidate-card.tsx", ["460"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\certificate-verification.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\create-candidate-modal.tsx", ["461"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\create-organization-modal.tsx", ["462"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\organization-card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\recent-activity.tsx", ["463"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\recent-results-table.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\stats-card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\specialized\\test-results-table.tsx", ["464", "465"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\ui\\modal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\hooks\\use-paywall.ts", ["466"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\ai\\claude.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\auth\\config.ts", ["467"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\certificates\\generator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\db\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\db\\schema.ts", ["468", "469"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\payments\\click.ts", ["470", "471", "472", "473"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\payments\\payme.ts", ["474", "475", "476", "477", "478", "479", "480", "481", "482", "483", "484", "485", "486", "487", "488", "489", "490", "491", "492", "493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504", "505", "506", "507", "508"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\payments\\types.ts", ["509", "510"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\payments\\utils.ts", ["511", "512", "513", "514", "515", "516", "517", "518"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\utils\\cn.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\utils\\format.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\utils\\ielts-scoring.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\middleware.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\types\\auth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\TLD System\\enhanced-ielts-system\\src\\types\\database.ts", [], [], {"ruleId": "519", "severity": 2, "message": "520", "line": 4, "column": 10, "nodeType": null, "messageId": "521", "endLine": 4, "endColumn": 17}, {"ruleId": "519", "severity": 2, "message": "522", "line": 4, "column": 14, "nodeType": null, "messageId": "521", "endLine": 4, "endColumn": 17}, {"ruleId": "519", "severity": 2, "message": "523", "line": 9, "column": 30, "nodeType": null, "messageId": "521", "endLine": 9, "endColumn": 36}, {"ruleId": "519", "severity": 2, "message": "522", "line": 4, "column": 20, "nodeType": null, "messageId": "521", "endLine": 4, "endColumn": 23}, {"ruleId": "524", "severity": 2, "message": "525", "line": 72, "column": 68, "nodeType": "526", "messageId": "527", "endLine": 72, "endColumn": 71, "suggestions": "528"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 72, "column": 84, "nodeType": "526", "messageId": "527", "endLine": 72, "endColumn": 87, "suggestions": "529"}, {"ruleId": "519", "severity": 2, "message": "530", "line": 95, "column": 11, "nodeType": null, "messageId": "521", "endLine": 95, "endColumn": 17}, {"ruleId": "531", "severity": 2, "message": "532", "line": 101, "column": 9, "nodeType": "533", "messageId": "534", "endLine": 101, "endColumn": 14, "fix": "535"}, {"ruleId": "519", "severity": 2, "message": "536", "line": 101, "column": 9, "nodeType": null, "messageId": "521", "endLine": 101, "endColumn": 14}, {"ruleId": "531", "severity": 2, "message": "532", "line": 130, "column": 9, "nodeType": "533", "messageId": "534", "endLine": 130, "endColumn": 14, "fix": "537"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 141, "column": 58, "nodeType": "526", "messageId": "527", "endLine": 141, "endColumn": 61, "suggestions": "538"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 145, "column": 61, "nodeType": "526", "messageId": "527", "endLine": 145, "endColumn": 64, "suggestions": "539"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 52, "column": 37, "nodeType": "526", "messageId": "527", "endLine": 52, "endColumn": 40, "suggestions": "540"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 136, "column": 22, "nodeType": "526", "messageId": "527", "endLine": 136, "endColumn": 25, "suggestions": "541"}, {"ruleId": "519", "severity": 2, "message": "542", "line": 62, "column": 27, "nodeType": null, "messageId": "521", "endLine": 62, "endColumn": 34}, {"ruleId": "519", "severity": 2, "message": "542", "line": 72, "column": 27, "nodeType": null, "messageId": "521", "endLine": 72, "endColumn": 34}, {"ruleId": "524", "severity": 2, "message": "525", "line": 91, "column": 23, "nodeType": "526", "messageId": "527", "endLine": 91, "endColumn": 26, "suggestions": "543"}, {"ruleId": "544", "severity": 2, "message": "545", "line": 114, "column": 56, "nodeType": "546", "messageId": "547", "suggestions": "548"}, {"ruleId": "519", "severity": 2, "message": "549", "line": 3, "column": 44, "nodeType": null, "messageId": "521", "endLine": 3, "endColumn": 61}, {"ruleId": "519", "severity": 2, "message": "550", "line": 14, "column": 3, "nodeType": null, "messageId": "521", "endLine": 14, "endColumn": 13}, {"ruleId": "519", "severity": 2, "message": "551", "line": 15, "column": 3, "nodeType": null, "messageId": "521", "endLine": 15, "endColumn": 11}, {"ruleId": "519", "severity": 2, "message": "552", "line": 16, "column": 3, "nodeType": null, "messageId": "521", "endLine": 16, "endColumn": 11}, {"ruleId": "524", "severity": 2, "message": "525", "line": 40, "column": 15, "nodeType": "526", "messageId": "527", "endLine": 40, "endColumn": 18, "suggestions": "553"}, {"ruleId": "519", "severity": 2, "message": "551", "line": 10, "column": 3, "nodeType": null, "messageId": "521", "endLine": 10, "endColumn": 11}, {"ruleId": "519", "severity": 2, "message": "552", "line": 11, "column": 3, "nodeType": null, "messageId": "521", "endLine": 11, "endColumn": 11}, {"ruleId": "524", "severity": 2, "message": "525", "line": 31, "column": 15, "nodeType": "526", "messageId": "527", "endLine": 31, "endColumn": 18, "suggestions": "554"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 68, "column": 39, "nodeType": "526", "messageId": "527", "endLine": 68, "endColumn": 42, "suggestions": "555"}, {"ruleId": "519", "severity": 2, "message": "556", "line": 69, "column": 5, "nodeType": null, "messageId": "521", "endLine": 69, "endColumn": 13}, {"ruleId": "519", "severity": 2, "message": "557", "line": 14, "column": 3, "nodeType": null, "messageId": "521", "endLine": 14, "endColumn": 12}, {"ruleId": "519", "severity": 2, "message": "558", "line": 10, "column": 10, "nodeType": null, "messageId": "521", "endLine": 10, "endColumn": 15}, {"ruleId": "519", "severity": 2, "message": "559", "line": 13, "column": 3, "nodeType": null, "messageId": "521", "endLine": 13, "endColumn": 9}, {"ruleId": "519", "severity": 2, "message": "560", "line": 4, "column": 10, "nodeType": null, "messageId": "521", "endLine": 4, "endColumn": 16}, {"ruleId": "519", "severity": 2, "message": "561", "line": 15, "column": 3, "nodeType": null, "messageId": "521", "endLine": 15, "endColumn": 14}, {"ruleId": "544", "severity": 2, "message": "545", "line": 190, "column": 27, "nodeType": "546", "messageId": "547", "suggestions": "562"}, {"ruleId": "544", "severity": 2, "message": "545", "line": 40, "column": 24, "nodeType": "546", "messageId": "547", "suggestions": "563"}, {"ruleId": "544", "severity": 2, "message": "545", "line": 84, "column": 49, "nodeType": "546", "messageId": "547", "suggestions": "564"}, {"ruleId": "519", "severity": 2, "message": "551", "line": 14, "column": 3, "nodeType": null, "messageId": "521", "endLine": 14, "endColumn": 11}, {"ruleId": "565", "severity": 1, "message": "566", "line": 81, "column": 6, "nodeType": "567", "endLine": 81, "endColumn": 31, "suggestions": "568"}, {"ruleId": "519", "severity": 2, "message": "569", "line": 156, "column": 14, "nodeType": null, "messageId": "521", "endLine": 156, "endColumn": 17}, {"ruleId": "524", "severity": 2, "message": "525", "line": 54, "column": 14, "nodeType": "526", "messageId": "527", "endLine": 54, "endColumn": 17, "suggestions": "570"}, {"ruleId": "565", "severity": 1, "message": "571", "line": 81, "column": 6, "nodeType": "567", "endLine": 81, "endColumn": 31, "suggestions": "572"}, {"ruleId": "519", "severity": 2, "message": "573", "line": 54, "column": 30, "nodeType": null, "messageId": "521", "endLine": 54, "endColumn": 39}, {"ruleId": "524", "severity": 2, "message": "525", "line": 81, "column": 39, "nodeType": "526", "messageId": "527", "endLine": 81, "endColumn": 42, "suggestions": "574"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 109, "column": 50, "nodeType": "526", "messageId": "527", "endLine": 109, "endColumn": 53, "suggestions": "575"}, {"ruleId": "544", "severity": 2, "message": "545", "line": 129, "column": 18, "nodeType": "546", "messageId": "547", "suggestions": "576"}, {"ruleId": "519", "severity": 2, "message": "577", "line": 159, "column": 34, "nodeType": null, "messageId": "521", "endLine": 159, "endColumn": 39}, {"ruleId": "524", "severity": 2, "message": "525", "line": 24, "column": 14, "nodeType": "526", "messageId": "527", "endLine": 24, "endColumn": 17, "suggestions": "578"}, {"ruleId": "565", "severity": 1, "message": "571", "line": 39, "column": 6, "nodeType": "567", "endLine": 39, "endColumn": 31, "suggestions": "579"}, {"ruleId": "519", "severity": 2, "message": "580", "line": 4, "column": 41, "nodeType": null, "messageId": "521", "endLine": 4, "endColumn": 54}, {"ruleId": "519", "severity": 2, "message": "581", "line": 60, "column": 13, "nodeType": null, "messageId": "521", "endLine": 60, "endColumn": 22}, {"ruleId": "519", "severity": 2, "message": "582", "line": 67, "column": 13, "nodeType": null, "messageId": "521", "endLine": 67, "endColumn": 25}, {"ruleId": "519", "severity": 2, "message": "522", "line": 3, "column": 20, "nodeType": null, "messageId": "521", "endLine": 3, "endColumn": 23}, {"ruleId": "524", "severity": 2, "message": "525", "line": 53, "column": 39, "nodeType": "526", "messageId": "527", "endLine": 53, "endColumn": 42, "suggestions": "583"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 73, "column": 39, "nodeType": "526", "messageId": "527", "endLine": 73, "endColumn": 42, "suggestions": "584"}, {"ruleId": "565", "severity": 1, "message": "585", "line": 72, "column": 6, "nodeType": "567", "endLine": 72, "endColumn": 42, "suggestions": "586"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 87, "column": 43, "nodeType": "526", "messageId": "527", "endLine": 87, "endColumn": 46, "suggestions": "587"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 136, "column": 23, "nodeType": "526", "messageId": "527", "endLine": 136, "endColumn": 26, "suggestions": "588"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 182, "column": 24, "nodeType": "526", "messageId": "527", "endLine": 182, "endColumn": 27, "suggestions": "589"}, {"ruleId": "590", "severity": 2, "message": "591", "line": 75, "column": 18, "nodeType": "592", "messageId": "593", "endLine": 75, "endColumn": 35}, {"ruleId": "524", "severity": 2, "message": "525", "line": 140, "column": 52, "nodeType": "526", "messageId": "527", "endLine": 140, "endColumn": 55, "suggestions": "594"}, {"ruleId": "519", "severity": 2, "message": "595", "line": 237, "column": 49, "nodeType": null, "messageId": "521", "endLine": 237, "endColumn": 62}, {"ruleId": "519", "severity": 2, "message": "595", "line": 261, "column": 46, "nodeType": null, "messageId": "521", "endLine": 261, "endColumn": 59}, {"ruleId": "519", "severity": 2, "message": "596", "line": 84, "column": 10, "nodeType": null, "messageId": "521", "endLine": 84, "endColumn": 27}, {"ruleId": "524", "severity": 2, "message": "525", "line": 125, "column": 52, "nodeType": "526", "messageId": "527", "endLine": 125, "endColumn": 55, "suggestions": "597"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 127, "column": 12, "nodeType": "526", "messageId": "527", "endLine": 127, "endColumn": 15, "suggestions": "598"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 131, "column": 12, "nodeType": "526", "messageId": "527", "endLine": 131, "endColumn": 15, "suggestions": "599"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 176, "column": 48, "nodeType": "526", "messageId": "527", "endLine": 176, "endColumn": 51, "suggestions": "600"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 178, "column": 12, "nodeType": "526", "messageId": "527", "endLine": 178, "endColumn": 15, "suggestions": "601"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 179, "column": 11, "nodeType": "526", "messageId": "527", "endLine": 179, "endColumn": 14, "suggestions": "602"}, {"ruleId": "519", "severity": 2, "message": "603", "line": 182, "column": 13, "nodeType": null, "messageId": "521", "endLine": 182, "endColumn": 20}, {"ruleId": "519", "severity": 2, "message": "604", "line": 182, "column": 22, "nodeType": null, "messageId": "521", "endLine": 182, "endColumn": 28}, {"ruleId": "519", "severity": 2, "message": "605", "line": 193, "column": 12, "nodeType": null, "messageId": "521", "endLine": 193, "endColumn": 17}, {"ruleId": "524", "severity": 2, "message": "525", "line": 207, "column": 53, "nodeType": "526", "messageId": "527", "endLine": 207, "endColumn": 56, "suggestions": "606"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 209, "column": 12, "nodeType": "526", "messageId": "527", "endLine": 209, "endColumn": 15, "suggestions": "607"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 210, "column": 11, "nodeType": "526", "messageId": "527", "endLine": 210, "endColumn": 14, "suggestions": "608"}, {"ruleId": "519", "severity": 2, "message": "604", "line": 213, "column": 23, "nodeType": null, "messageId": "521", "endLine": 213, "endColumn": 29}, {"ruleId": "519", "severity": 2, "message": "603", "line": 213, "column": 31, "nodeType": null, "messageId": "521", "endLine": 213, "endColumn": 38}, {"ruleId": "519", "severity": 2, "message": "605", "line": 226, "column": 12, "nodeType": null, "messageId": "521", "endLine": 226, "endColumn": 17}, {"ruleId": "524", "severity": 2, "message": "525", "line": 240, "column": 54, "nodeType": "526", "messageId": "527", "endLine": 240, "endColumn": 57, "suggestions": "609"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 242, "column": 12, "nodeType": "526", "messageId": "527", "endLine": 242, "endColumn": 15, "suggestions": "610"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 243, "column": 11, "nodeType": "526", "messageId": "527", "endLine": 243, "endColumn": 14, "suggestions": "611"}, {"ruleId": "519", "severity": 2, "message": "605", "line": 260, "column": 12, "nodeType": null, "messageId": "521", "endLine": 260, "endColumn": 17}, {"ruleId": "524", "severity": 2, "message": "525", "line": 274, "column": 53, "nodeType": "526", "messageId": "527", "endLine": 274, "endColumn": 56, "suggestions": "612"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 276, "column": 12, "nodeType": "526", "messageId": "527", "endLine": 276, "endColumn": 15, "suggestions": "613"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 277, "column": 11, "nodeType": "526", "messageId": "527", "endLine": 277, "endColumn": 14, "suggestions": "614"}, {"ruleId": "519", "severity": 2, "message": "605", "line": 294, "column": 12, "nodeType": null, "messageId": "521", "endLine": 294, "endColumn": 17}, {"ruleId": "524", "severity": 2, "message": "525", "line": 308, "column": 52, "nodeType": "526", "messageId": "527", "endLine": 308, "endColumn": 55, "suggestions": "615"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 310, "column": 12, "nodeType": "526", "messageId": "527", "endLine": 310, "endColumn": 15, "suggestions": "616"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 311, "column": 11, "nodeType": "526", "messageId": "527", "endLine": 311, "endColumn": 14, "suggestions": "617"}, {"ruleId": "519", "severity": 2, "message": "605", "line": 330, "column": 12, "nodeType": null, "messageId": "521", "endLine": 330, "endColumn": 17}, {"ruleId": "524", "severity": 2, "message": "525", "line": 344, "column": 48, "nodeType": "526", "messageId": "527", "endLine": 344, "endColumn": 51, "suggestions": "618"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 346, "column": 12, "nodeType": "526", "messageId": "527", "endLine": 346, "endColumn": 15, "suggestions": "619"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 347, "column": 11, "nodeType": "526", "messageId": "527", "endLine": 347, "endColumn": 14, "suggestions": "620"}, {"ruleId": "519", "severity": 2, "message": "621", "line": 350, "column": 13, "nodeType": null, "messageId": "521", "endLine": 350, "endColumn": 17}, {"ruleId": "519", "severity": 2, "message": "622", "line": 350, "column": 19, "nodeType": null, "messageId": "521", "endLine": 350, "endColumn": 21}, {"ruleId": "519", "severity": 2, "message": "605", "line": 361, "column": 12, "nodeType": null, "messageId": "521", "endLine": 361, "endColumn": 17}, {"ruleId": "519", "severity": 2, "message": "595", "line": 375, "column": 49, "nodeType": null, "messageId": "521", "endLine": 375, "endColumn": 62}, {"ruleId": "524", "severity": 2, "message": "525", "line": 23, "column": 23, "nodeType": "526", "messageId": "527", "endLine": 23, "endColumn": 26, "suggestions": "623"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 59, "column": 14, "nodeType": "526", "messageId": "527", "endLine": 59, "endColumn": 17, "suggestions": "624"}, {"ruleId": "519", "severity": 2, "message": "625", "line": 6, "column": 62, "nodeType": null, "messageId": "521", "endLine": 6, "endColumn": 73}, {"ruleId": "519", "severity": 2, "message": "626", "line": 13, "column": 3, "nodeType": null, "messageId": "521", "endLine": 13, "endColumn": 19}, {"ruleId": "519", "severity": 2, "message": "627", "line": 78, "column": 12, "nodeType": null, "messageId": "521", "endLine": 78, "endColumn": 23}, {"ruleId": "524", "severity": 2, "message": "525", "line": 141, "column": 14, "nodeType": "526", "messageId": "527", "endLine": 141, "endColumn": 17, "suggestions": "628"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 144, "column": 23, "nodeType": "526", "messageId": "527", "endLine": 144, "endColumn": 26, "suggestions": "629"}, {"ruleId": "524", "severity": 2, "message": "525", "line": 310, "column": 32, "nodeType": "526", "messageId": "527", "endLine": 310, "endColumn": 35, "suggestions": "630"}, {"ruleId": "519", "severity": 2, "message": "631", "line": 392, "column": 3, "nodeType": null, "messageId": "521", "endLine": 392, "endColumn": 17}, {"ruleId": "519", "severity": 2, "message": "632", "line": 393, "column": 3, "nodeType": null, "messageId": "521", "endLine": 393, "endColumn": 9}, "@typescript-eslint/no-unused-vars", "'headers' is defined but never used.", "unusedVar", "'and' is defined but never used.", "'Filter' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["633", "634"], ["635", "636"], "'search' is assigned a value but never used.", "prefer-const", "'query' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "637", "text": "638"}, "'query' is assigned a value but never used.", {"range": "639", "text": "640"}, ["641", "642"], ["643", "644"], ["645", "646"], ["647", "648"], "'request' is defined but never used.", ["649", "650"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["651", "652", "653", "654"], "'testRegistrations' is defined but never used.", "'CreditCard' is defined but never used.", "'Calendar' is defined but never used.", "'FileText' is defined but never used.", ["655", "656"], ["657", "658"], ["659", "660"], "'setValue' is assigned a value but never used.", "'UserCheck' is defined but never used.", "'Badge' is defined but never used.", "'Upload' is defined but never used.", "'Button' is defined but never used.", "'AlertCircle' is defined but never used.", ["661", "662", "663", "664"], ["665", "666", "667", "668"], ["669", "670", "671", "672"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCertificate'. Either include it or remove the dependency array.", "ArrayExpression", ["673"], "'err' is defined but never used.", ["674", "675"], "React Hook useEffect has a missing dependency: 'fetchFeedback'. Either include it or remove the dependency array.", ["676"], "'candidate' is defined but never used.", ["677", "678"], ["679", "680"], ["681", "682", "683", "684"], "'index' is defined but never used.", ["685", "686"], ["687"], "'GraduationCap' is defined but never used.", "'candidate' is assigned a value but never used.", "'organization' is assigned a value but never used.", ["688", "689"], ["690", "691"], "React Hook useEffect has a missing dependency: 'checkAccess'. Either include it or remove the dependency array.", ["692"], ["693", "694"], ["695", "696"], ["697", "698"], "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["699", "700"], "'transactionId' is defined but never used.", "'generatePaymeAuth' is defined but never used.", ["701", "702"], ["703", "704"], ["705", "706"], ["707", "708"], ["709", "710"], ["711", "712"], "'account' is assigned a value but never used.", "'amount' is assigned a value but never used.", "'error' is defined but never used.", ["713", "714"], ["715", "716"], ["717", "718"], ["719", "720"], ["721", "722"], ["723", "724"], ["725", "726"], ["727", "728"], ["729", "730"], ["731", "732"], ["733", "734"], ["735", "736"], ["737", "738"], ["739", "740"], ["741", "742"], "'from' is assigned a value but never used.", "'to' is assigned a value but never used.", ["743", "744"], ["745", "746"], "'testResults' is defined but never used.", "'AccessPermission' is defined but never used.", "'transaction' is assigned a value but never used.", ["747", "748"], ["749", "750"], ["751", "752"], "'organizationId' is defined but never used.", "'amount' is defined but never used.", {"messageId": "753", "fix": "754", "desc": "755"}, {"messageId": "756", "fix": "757", "desc": "758"}, {"messageId": "753", "fix": "759", "desc": "755"}, {"messageId": "756", "fix": "760", "desc": "758"}, [3242, 3370], "const query = db\n      .select()\n      .from(candidates)\n      .where(eq(candidates.organizationId, session.user.organizationId));", [3693, 3737], "const query = db.select().from(organizations);", {"messageId": "753", "fix": "761", "desc": "755"}, {"messageId": "756", "fix": "762", "desc": "758"}, {"messageId": "753", "fix": "763", "desc": "755"}, {"messageId": "756", "fix": "764", "desc": "758"}, {"messageId": "753", "fix": "765", "desc": "755"}, {"messageId": "756", "fix": "766", "desc": "758"}, {"messageId": "753", "fix": "767", "desc": "755"}, {"messageId": "756", "fix": "768", "desc": "758"}, {"messageId": "753", "fix": "769", "desc": "755"}, {"messageId": "756", "fix": "770", "desc": "758"}, {"messageId": "771", "data": "772", "fix": "773", "desc": "774"}, {"messageId": "771", "data": "775", "fix": "776", "desc": "777"}, {"messageId": "771", "data": "778", "fix": "779", "desc": "780"}, {"messageId": "771", "data": "781", "fix": "782", "desc": "783"}, {"messageId": "753", "fix": "784", "desc": "755"}, {"messageId": "756", "fix": "785", "desc": "758"}, {"messageId": "753", "fix": "786", "desc": "755"}, {"messageId": "756", "fix": "787", "desc": "758"}, {"messageId": "753", "fix": "788", "desc": "755"}, {"messageId": "756", "fix": "789", "desc": "758"}, {"messageId": "771", "data": "790", "fix": "791", "desc": "774"}, {"messageId": "771", "data": "792", "fix": "793", "desc": "777"}, {"messageId": "771", "data": "794", "fix": "795", "desc": "780"}, {"messageId": "771", "data": "796", "fix": "797", "desc": "783"}, {"messageId": "771", "data": "798", "fix": "799", "desc": "774"}, {"messageId": "771", "data": "800", "fix": "801", "desc": "777"}, {"messageId": "771", "data": "802", "fix": "803", "desc": "780"}, {"messageId": "771", "data": "804", "fix": "805", "desc": "783"}, {"messageId": "771", "data": "806", "fix": "807", "desc": "774"}, {"messageId": "771", "data": "808", "fix": "809", "desc": "777"}, {"messageId": "771", "data": "810", "fix": "811", "desc": "780"}, {"messageId": "771", "data": "812", "fix": "813", "desc": "783"}, {"desc": "814", "fix": "815"}, {"messageId": "753", "fix": "816", "desc": "755"}, {"messageId": "756", "fix": "817", "desc": "758"}, {"desc": "818", "fix": "819"}, {"messageId": "753", "fix": "820", "desc": "755"}, {"messageId": "756", "fix": "821", "desc": "758"}, {"messageId": "753", "fix": "822", "desc": "755"}, {"messageId": "756", "fix": "823", "desc": "758"}, {"messageId": "771", "data": "824", "fix": "825", "desc": "774"}, {"messageId": "771", "data": "826", "fix": "827", "desc": "777"}, {"messageId": "771", "data": "828", "fix": "829", "desc": "780"}, {"messageId": "771", "data": "830", "fix": "831", "desc": "783"}, {"messageId": "753", "fix": "832", "desc": "755"}, {"messageId": "756", "fix": "833", "desc": "758"}, {"desc": "834", "fix": "835"}, {"messageId": "753", "fix": "836", "desc": "755"}, {"messageId": "756", "fix": "837", "desc": "758"}, {"messageId": "753", "fix": "838", "desc": "755"}, {"messageId": "756", "fix": "839", "desc": "758"}, {"desc": "840", "fix": "841"}, {"messageId": "753", "fix": "842", "desc": "755"}, {"messageId": "756", "fix": "843", "desc": "758"}, {"messageId": "753", "fix": "844", "desc": "755"}, {"messageId": "756", "fix": "845", "desc": "758"}, {"messageId": "753", "fix": "846", "desc": "755"}, {"messageId": "756", "fix": "847", "desc": "758"}, {"messageId": "753", "fix": "848", "desc": "755"}, {"messageId": "756", "fix": "849", "desc": "758"}, {"messageId": "753", "fix": "850", "desc": "755"}, {"messageId": "756", "fix": "851", "desc": "758"}, {"messageId": "753", "fix": "852", "desc": "755"}, {"messageId": "756", "fix": "853", "desc": "758"}, {"messageId": "753", "fix": "854", "desc": "755"}, {"messageId": "756", "fix": "855", "desc": "758"}, {"messageId": "753", "fix": "856", "desc": "755"}, {"messageId": "756", "fix": "857", "desc": "758"}, {"messageId": "753", "fix": "858", "desc": "755"}, {"messageId": "756", "fix": "859", "desc": "758"}, {"messageId": "753", "fix": "860", "desc": "755"}, {"messageId": "756", "fix": "861", "desc": "758"}, {"messageId": "753", "fix": "862", "desc": "755"}, {"messageId": "756", "fix": "863", "desc": "758"}, {"messageId": "753", "fix": "864", "desc": "755"}, {"messageId": "756", "fix": "865", "desc": "758"}, {"messageId": "753", "fix": "866", "desc": "755"}, {"messageId": "756", "fix": "867", "desc": "758"}, {"messageId": "753", "fix": "868", "desc": "755"}, {"messageId": "756", "fix": "869", "desc": "758"}, {"messageId": "753", "fix": "870", "desc": "755"}, {"messageId": "756", "fix": "871", "desc": "758"}, {"messageId": "753", "fix": "872", "desc": "755"}, {"messageId": "756", "fix": "873", "desc": "758"}, {"messageId": "753", "fix": "874", "desc": "755"}, {"messageId": "756", "fix": "875", "desc": "758"}, {"messageId": "753", "fix": "876", "desc": "755"}, {"messageId": "756", "fix": "877", "desc": "758"}, {"messageId": "753", "fix": "878", "desc": "755"}, {"messageId": "756", "fix": "879", "desc": "758"}, {"messageId": "753", "fix": "880", "desc": "755"}, {"messageId": "756", "fix": "881", "desc": "758"}, {"messageId": "753", "fix": "882", "desc": "755"}, {"messageId": "756", "fix": "883", "desc": "758"}, {"messageId": "753", "fix": "884", "desc": "755"}, {"messageId": "756", "fix": "885", "desc": "758"}, {"messageId": "753", "fix": "886", "desc": "755"}, {"messageId": "756", "fix": "887", "desc": "758"}, {"messageId": "753", "fix": "888", "desc": "755"}, {"messageId": "756", "fix": "889", "desc": "758"}, {"messageId": "753", "fix": "890", "desc": "755"}, {"messageId": "756", "fix": "891", "desc": "758"}, {"messageId": "753", "fix": "892", "desc": "755"}, {"messageId": "756", "fix": "893", "desc": "758"}, {"messageId": "753", "fix": "894", "desc": "755"}, {"messageId": "756", "fix": "895", "desc": "758"}, {"messageId": "753", "fix": "896", "desc": "755"}, {"messageId": "756", "fix": "897", "desc": "758"}, {"messageId": "753", "fix": "898", "desc": "755"}, {"messageId": "756", "fix": "899", "desc": "758"}, {"messageId": "753", "fix": "900", "desc": "755"}, {"messageId": "756", "fix": "901", "desc": "758"}, "suggestUnknown", {"range": "902", "text": "903"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "904", "text": "905"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "906", "text": "903"}, {"range": "907", "text": "905"}, {"range": "908", "text": "903"}, {"range": "909", "text": "905"}, {"range": "910", "text": "903"}, {"range": "911", "text": "905"}, {"range": "912", "text": "903"}, {"range": "913", "text": "905"}, {"range": "914", "text": "903"}, {"range": "915", "text": "905"}, {"range": "916", "text": "903"}, {"range": "917", "text": "905"}, "replaceWithAlt", {"alt": "918"}, {"range": "919", "text": "920"}, "Replace with `&apos;`.", {"alt": "921"}, {"range": "922", "text": "923"}, "Replace with `&lsquo;`.", {"alt": "924"}, {"range": "925", "text": "926"}, "Replace with `&#39;`.", {"alt": "927"}, {"range": "928", "text": "929"}, "Replace with `&rsquo;`.", {"range": "930", "text": "903"}, {"range": "931", "text": "905"}, {"range": "932", "text": "903"}, {"range": "933", "text": "905"}, {"range": "934", "text": "903"}, {"range": "935", "text": "905"}, {"alt": "918"}, {"range": "936", "text": "937"}, {"alt": "921"}, {"range": "938", "text": "939"}, {"alt": "924"}, {"range": "940", "text": "941"}, {"alt": "927"}, {"range": "942", "text": "943"}, {"alt": "918"}, {"range": "944", "text": "945"}, {"alt": "921"}, {"range": "946", "text": "947"}, {"alt": "924"}, {"range": "948", "text": "949"}, {"alt": "927"}, {"range": "950", "text": "951"}, {"alt": "918"}, {"range": "952", "text": "953"}, {"alt": "921"}, {"range": "954", "text": "955"}, {"alt": "924"}, {"range": "956", "text": "957"}, {"alt": "927"}, {"range": "958", "text": "959"}, "Update the dependencies array to be: [fetchCertificate, hasAccess, selectedTest]", {"range": "960", "text": "961"}, {"range": "962", "text": "903"}, {"range": "963", "text": "905"}, "Update the dependencies array to be: [fetchFeedback, hasAccess, selectedTest]", {"range": "964", "text": "965"}, {"range": "966", "text": "903"}, {"range": "967", "text": "905"}, {"range": "968", "text": "903"}, {"range": "969", "text": "905"}, {"alt": "918"}, {"range": "970", "text": "971"}, {"alt": "921"}, {"range": "972", "text": "973"}, {"alt": "924"}, {"range": "974", "text": "975"}, {"alt": "927"}, {"range": "976", "text": "977"}, {"range": "978", "text": "903"}, {"range": "979", "text": "905"}, "Update the dependencies array to be: [testResultId, hasAccess, fetchFeedback]", {"range": "980", "text": "981"}, {"range": "982", "text": "903"}, {"range": "983", "text": "905"}, {"range": "984", "text": "903"}, {"range": "985", "text": "905"}, "Update the dependencies array to be: [candidateId, checkAccess, featureType, resultId]", {"range": "986", "text": "987"}, {"range": "988", "text": "903"}, {"range": "989", "text": "905"}, {"range": "990", "text": "903"}, {"range": "991", "text": "905"}, {"range": "992", "text": "903"}, {"range": "993", "text": "905"}, {"range": "994", "text": "903"}, {"range": "995", "text": "905"}, {"range": "996", "text": "903"}, {"range": "997", "text": "905"}, {"range": "998", "text": "903"}, {"range": "999", "text": "905"}, {"range": "1000", "text": "903"}, {"range": "1001", "text": "905"}, {"range": "1002", "text": "903"}, {"range": "1003", "text": "905"}, {"range": "1004", "text": "903"}, {"range": "1005", "text": "905"}, {"range": "1006", "text": "903"}, {"range": "1007", "text": "905"}, {"range": "1008", "text": "903"}, {"range": "1009", "text": "905"}, {"range": "1010", "text": "903"}, {"range": "1011", "text": "905"}, {"range": "1012", "text": "903"}, {"range": "1013", "text": "905"}, {"range": "1014", "text": "903"}, {"range": "1015", "text": "905"}, {"range": "1016", "text": "903"}, {"range": "1017", "text": "905"}, {"range": "1018", "text": "903"}, {"range": "1019", "text": "905"}, {"range": "1020", "text": "903"}, {"range": "1021", "text": "905"}, {"range": "1022", "text": "903"}, {"range": "1023", "text": "905"}, {"range": "1024", "text": "903"}, {"range": "1025", "text": "905"}, {"range": "1026", "text": "903"}, {"range": "1027", "text": "905"}, {"range": "1028", "text": "903"}, {"range": "1029", "text": "905"}, {"range": "1030", "text": "903"}, {"range": "1031", "text": "905"}, {"range": "1032", "text": "903"}, {"range": "1033", "text": "905"}, {"range": "1034", "text": "903"}, {"range": "1035", "text": "905"}, {"range": "1036", "text": "903"}, {"range": "1037", "text": "905"}, {"range": "1038", "text": "903"}, {"range": "1039", "text": "905"}, {"range": "1040", "text": "903"}, {"range": "1041", "text": "905"}, {"range": "1042", "text": "903"}, {"range": "1043", "text": "905"}, {"range": "1044", "text": "903"}, {"range": "1045", "text": "905"}, {"range": "1046", "text": "903"}, {"range": "1047", "text": "905"}, [2315, 2318], "unknown", [2315, 2318], "never", [2331, 2334], [2331, 2334], [4008, 4011], [4008, 4011], [4102, 4105], [4102, 4105], [1514, 1517], [1514, 1517], [3868, 3871], [3868, 3871], [3494, 3497], [3494, 3497], "&apos;", [3462, 3515], "Welcome back! Here&apos;s your test result entry overview.", "&lsquo;", [3462, 3515], "Welcome back! Here&lsquo;s your test result entry overview.", "&#39;", [3462, 3515], "Welcome back! Here&#39;s your test result entry overview.", "&rsquo;", [3462, 3515], "Welcome back! Here&rsquo;s your test result entry overview.", [909, 912], [909, 912], [654, 657], [654, 657], [1746, 1749], [1746, 1749], [5766, 5818], "\n                  What you&apos;ll get:\n                ", [5766, 5818], "\n                  What you&lsquo;ll get:\n                ", [5766, 5818], "\n                  What you&#39;ll get:\n                ", [5766, 5818], "\n                  What you&rsquo;ll get:\n                ", [1455, 1586], "\n              We couldn&apos;t find any test results for the provided passport/birth certificate number and date of birth.\n            ", [1455, 1586], "\n              We couldn&lsquo;t find any test results for the provided passport/birth certificate number and date of birth.\n            ", [1455, 1586], "\n              We couldn&#39;t find any test results for the provided passport/birth certificate number and date of birth.\n            ", [1455, 1586], "\n              We couldn&rsquo;t find any test results for the provided passport/birth certificate number and date of birth.\n            ", [3541, 3638], "\n              We found your profile, but you don&apos;t have any test registrations yet.\n            ", [3541, 3638], "\n              We found your profile, but you don&lsquo;t have any test registrations yet.\n            ", [3541, 3638], "\n              We found your profile, but you don&#39;t have any test registrations yet.\n            ", [3541, 3638], "\n              We found your profile, but you don&rsquo;t have any test registrations yet.\n            ", [2015, 2040], "[fetchCertificate, hasAccess, selectedTest]", [1116, 1119], [1116, 1119], [1950, 1975], "[fetch<PERSON><PERSON><PERSON>, hasAccess, selectedTest]", [2064, 2067], [2064, 2067], [3102, 3105], [3102, 3105], [3757, 3823], "\n          You don&apos;t have any completed test results yet.\n        ", [3757, 3823], "\n          You don&lsquo;t have any completed test results yet.\n        ", [3757, 3823], "\n          You don&#39;t have any completed test results yet.\n        ", [3757, 3823], "\n          You don&rsquo;t have any completed test results yet.\n        ", [603, 606], [603, 606], [1011, 1036], "[testR<PERSON><PERSON>Id, hasAccess, fetchFeedback]", [1532, 1535], [1532, 1535], [2114, 2117], [2114, 2117], [1661, 1697], "[candidateId, checkAccess, featureType, resultId]", [2390, 2393], [2390, 2393], [7110, 7113], [7110, 7113], [9395, 9398], [9395, 9398], [3560, 3563], [3560, 3563], [3024, 3027], [3024, 3027], [3071, 3074], [3071, 3074], [3138, 3141], [3138, 3141], [4441, 4444], [4441, 4444], [4488, 4491], [4488, 4491], [4503, 4506], [4503, 4506], [5002, 5005], [5002, 5005], [5049, 5052], [5049, 5052], [5064, 5067], [5064, 5067], [5633, 5636], [5633, 5636], [5680, 5683], [5680, 5683], [5695, 5698], [5695, 5698], [6272, 6275], [6272, 6275], [6319, 6322], [6319, 6322], [6334, 6337], [6334, 6337], [6963, 6966], [6963, 6966], [7010, 7013], [7010, 7013], [7025, 7028], [7025, 7028], [7606, 7609], [7606, 7609], [7653, 7656], [7653, 7656], [7668, 7671], [7668, 7671], [577, 580], [577, 580], [1337, 1340], [1337, 1340], [3638, 3641], [3638, 3641], [3694, 3697], [3694, 3697], [7993, 7996], [7993, 7996]]