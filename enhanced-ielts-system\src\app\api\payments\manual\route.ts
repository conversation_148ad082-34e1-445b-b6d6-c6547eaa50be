import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { paymentTransactions, candidates } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';
import { createId } from '@paralleldrive/cuid2';
import { FeatureType, PAYMENT_PRICING } from '@/lib/payments/types';

const manualPaymentSchema = z.object({
  candidateId: z.string().min(1),
  featureType: z.enum(['feedback', 'certificate', 'progress']),
  resultId: z.string().optional(),
  amount: z.number().positive(),
  currency: z.string().min(1),
  paymentMethod: z.enum(['bank_transfer', 'cash']),
  referenceNumber: z.string().min(1),
  paymentDate: z.string().min(1),
  notes: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = manualPaymentSchema.parse(body);

    // Verify candidate belongs to organization
    const candidate = await db
      .select()
      .from(candidates)
      .where(
        and(
          eq(candidates.id, validatedData.candidateId),
          eq(candidates.organizationId, session.user.organizationId)
        )
      )
      .limit(1);

    if (candidate.length === 0) {
      return NextResponse.json(
        { error: 'Candidate not found or does not belong to your organization' },
        { status: 404 }
      );
    }

    // Verify amount matches feature pricing
    const pricing = PAYMENT_PRICING[validatedData.featureType as FeatureType];
    if (validatedData.amount !== pricing.amount || validatedData.currency !== pricing.currency) {
      return NextResponse.json(
        { error: 'Invalid amount or currency for this feature' },
        { status: 400 }
      );
    }

    // Create manual payment transaction
    const transactionId = createId();
    const [transaction] = await db
      .insert(paymentTransactions)
      .values({
        id: transactionId,
        candidateId: validatedData.candidateId,
        organizationId: session.user.organizationId,
        amount: validatedData.amount.toString(),
        currency: validatedData.currency,
        gateway: 'manual',
        status: 'pending',
        featureType: validatedData.featureType,
        resultId: validatedData.resultId,
        metadata: {
          paymentMethod: validatedData.paymentMethod,
          referenceNumber: validatedData.referenceNumber,
          paymentDate: validatedData.paymentDate,
          notes: validatedData.notes,
          submittedBy: session.user.id,
          submittedAt: new Date().toISOString(),
        },
      })
      .returning();

    // TODO: Send notification to admins about pending manual payment

    return NextResponse.json({
      transactionId: transaction.id,
      status: 'pending',
      message: 'Manual payment submitted successfully. It will be reviewed by an admin.',
      estimatedProcessingTime: '1-3 business days',
    }, { status: 201 });
  } catch (error) {
    console.error('Manual payment submission error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to submit manual payment' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can view manual payments
    if (session.user.role !== 'admin' && !session.user.masterAdmin) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');

    let query = db
      .select({
        transaction: paymentTransactions,
        candidate: candidates,
      })
      .from(paymentTransactions)
      .leftJoin(candidates, eq(paymentTransactions.candidateId, candidates.id))
      .where(
        and(
          eq(paymentTransactions.gateway, 'manual'),
          eq(candidates.organizationId, session.user.organizationId)
        )
      );

    if (status) {
      query = query.where(
        and(
          eq(paymentTransactions.gateway, 'manual'),
          eq(candidates.organizationId, session.user.organizationId),
          eq(paymentTransactions.status, status)
        )
      );
    }

    const manualPayments = await query;

    return NextResponse.json(
      manualPayments.map(mp => ({
        id: mp.transaction.id,
        candidate: {
          id: mp.candidate?.id,
          fullName: mp.candidate?.fullName,
          email: mp.candidate?.email,
        },
        amount: parseFloat(mp.transaction.amount),
        currency: mp.transaction.currency,
        featureType: mp.transaction.featureType,
        resultId: mp.transaction.resultId,
        status: mp.transaction.status,
        metadata: mp.transaction.metadata,
        createdAt: mp.transaction.createdAt,
        completedAt: mp.transaction.completedAt,
      }))
    );
  } catch (error) {
    console.error('Manual payments fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch manual payments' },
      { status: 500 }
    );
  }
}
