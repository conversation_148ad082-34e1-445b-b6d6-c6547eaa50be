import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { testRegistrations, testResults, candidates } from '@/lib/db/schema';
import { eq, desc, and } from 'drizzle-orm';
import { TestRegistrationModal } from '@/components/forms/test-registration-modal';
import { TestResultsTable } from '@/components/specialized/test-results-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Search, FileText, Calendar, Users } from 'lucide-react';

export default async function ResultsPage() {
  const session = await auth();

  if (!session?.user?.organizationId) {
    return <div>Access denied</div>;
  }

  // Get test registrations with candidate info and results
  const registrationsWithDetails = await db
    .select({
      registration: testRegistrations,
      candidate: candidates,
      result: testResults,
    })
    .from(testRegistrations)
    .leftJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
    .leftJoin(testResults, eq(testRegistrations.id, testResults.testRegistrationId))
    .where(eq(candidates.organizationId, session.user.organizationId))
    .orderBy(desc(testRegistrations.testDate));

  // Get all candidates for the registration modal
  const allCandidates = await db
    .select({
      id: candidates.id,
      fullName: candidates.fullName,
      passportNumber: candidates.passportNumber,
    })
    .from(candidates)
    .where(eq(candidates.organizationId, session.user.organizationId))
    .orderBy(candidates.fullName);

  // Calculate statistics
  const totalRegistrations = registrationsWithDetails.length;
  const completedTests = registrationsWithDetails.filter(r => r.result?.status === 'completed').length;
  const pendingResults = registrationsWithDetails.filter(r =>
    r.registration.status === 'completed' && !r.result
  ).length;
  const upcomingTests = registrationsWithDetails.filter(r =>
    r.registration.status === 'registered' && new Date(r.registration.testDate) > new Date()
  ).length;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Test Results</h1>
          <p className="text-gray-600">Manage test registrations and enter results</p>
        </div>
        <TestRegistrationModal candidates={allCandidates}>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Register Test
          </Button>
        </TestRegistrationModal>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Calendar className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Registrations</p>
              <p className="text-2xl font-bold text-gray-900">{totalRegistrations}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <FileText className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completed Results</p>
              <p className="text-2xl font-bold text-gray-900">{completedTests}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <FileText className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending Results</p>
              <p className="text-2xl font-bold text-gray-900">{pendingResults}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Upcoming Tests</p>
              <p className="text-2xl font-bold text-gray-900">{upcomingTests}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search by candidate name or test center..."
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
              <option value="">All Status</option>
              <option value="registered">Registered</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
            <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
              <option value="">All Results</option>
              <option value="with-results">With Results</option>
              <option value="pending-results">Pending Results</option>
            </select>
          </div>
        </div>
      </div>

      {/* Test Results Table */}
      <TestResultsTable registrations={registrationsWithDetails} />

      {registrationsWithDetails.length === 0 && (
        <div className="text-center py-12">
          <Calendar className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No test registrations</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by registering your first test.</p>
          <div className="mt-6">
            <TestRegistrationModal candidates={allCandidates}>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Register Test
              </Button>
            </TestRegistrationModal>
          </div>
        </div>
      )}
    </div>
  );
}
