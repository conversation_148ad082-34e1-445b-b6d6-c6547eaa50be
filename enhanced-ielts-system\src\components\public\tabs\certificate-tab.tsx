'use client';

import { useState, useEffect } from 'react';
import { PaywallOverlay } from '@/components/paywall/paywall-overlay';
import { usePaywall } from '@/hooks/use-paywall';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Award,
  Download,
  Lock,
  Shield,
  QrCode,
  Calendar,
  FileText,
  CheckCircle,
  ExternalLink,
  Share2,
  Loader2,
  AlertCircle
} from 'lucide-react';

interface Candidate {
  id: string;
  fullName: string;
  passportNumber: string;
  nationality: string;
}

interface TestData {
  registration: {
    id: string;
    candidateNumber: string;
    testDate: Date;
    testCenter: string;
  };
  result: {
    id: string;
    listeningBandScore: string | null;
    readingBandScore: string | null;
    writingBandScore: string | null;
    speakingBandScore: string | null;
    overallBandScore: string | null;
    createdAt: Date;
  };
}

interface Certificate {
  id: string;
  serialNumber: string;
  generatedAt: string;
  expiresAt: string;
  status: 'active' | 'expired' | 'deleted' | 'revoked';
  downloadUrl: string;
}

interface CertificateTabProps {
  candidate: Candidate;
  testData: TestData[];
}

export function CertificateTab({ candidate, testData }: CertificateTabProps) {
  const [selectedTest, setSelectedTest] = useState(testData[0]?.result.id || '');
  const [showPaywall, setShowPaywall] = useState(false);
  const [certificate, setCertificate] = useState<Certificate | null>(null);
  const [certificateLoading, setCertificateLoading] = useState(false);
  const [certificateError, setCertificateError] = useState<string | null>(null);
  const [generating, setGenerating] = useState(false);

  const { hasAccess, isLoading } = usePaywall({
    candidateId: candidate.id,
    featureType: 'certificate',
    resultId: selectedTest,
  });

  // Fetch certificate when access is granted and test is selected
  useEffect(() => {
    if (hasAccess && selectedTest) {
      fetchCertificate();
    }
  }, [hasAccess, selectedTest]);

  const fetchCertificate = async () => {
    try {
      setCertificateLoading(true);
      setCertificateError(null);

      const response = await fetch(`/api/certificates?resultId=${selectedTest}`);

      if (response.status === 404) {
        setCertificate(null);
        return;
      }

      if (!response.ok) {
        throw new Error('Failed to fetch certificate');
      }

      const data = await response.json();
      setCertificate(data);
    } catch (err) {
      setCertificateError(err instanceof Error ? err.message : 'Failed to load certificate');
    } finally {
      setCertificateLoading(false);
    }
  };

  const generateCertificate = async () => {
    try {
      setGenerating(true);
      setCertificateError(null);

      const response = await fetch('/api/certificates/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ resultId: selectedTest }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate certificate');
      }

      const data = await response.json();
      setCertificate({
        id: data.certificateId,
        serialNumber: data.serialNumber,
        generatedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(), // 6 months
        status: 'active',
        downloadUrl: data.downloadUrl,
      });
    } catch (err) {
      setCertificateError(err instanceof Error ? err.message : 'Failed to generate certificate');
    } finally {
      setGenerating(false);
    }
  };

  const downloadCertificate = async () => {
    if (!certificate) return;

    try {
      const response = await fetch(certificate.downloadUrl);
      if (!response.ok) throw new Error('Download failed');

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `IELTS_Certificate_${certificate.serialNumber}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setCertificateError('Failed to download certificate');
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const renderBasicCertificate = () => {
    if (testData.length === 0) {
      return (
        <div className="text-center py-12">
          <Award className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Certificates Available</h3>
          <p className="text-gray-600">
            Complete a test to generate your official IELTS certificate.
          </p>
        </div>
      );
    }

    const selectedTestData = testData.find(test => test.result.id === selectedTest) || testData[0];

    return (
      <div className="space-y-6">
        {/* Test Selection */}
        {testData.length > 1 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Test for Certificate
            </label>
            <select
              value={selectedTest}
              onChange={(e) => setSelectedTest(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {testData.map((test) => (
                <option key={test.result.id} value={test.result.id}>
                  {formatDate(test.registration.testDate)} - Band {test.result.overallBandScore}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Certificate Preview */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-lg p-8">
          <div className="text-center">
            <div className="bg-blue-600 p-3 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Award className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">IELTS Test Certificate</h3>
            <p className="text-gray-600 mb-6">Official certification of your English proficiency</p>

            <div className="bg-white rounded-lg p-6 mb-6 shadow-sm">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">{candidate.fullName}</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Test Date:</span>
                  <div className="font-medium">{formatDate(selectedTestData.registration.testDate)}</div>
                </div>
                <div>
                  <span className="text-gray-600">Test Center:</span>
                  <div className="font-medium">{selectedTestData.registration.testCenter}</div>
                </div>
                <div>
                  <span className="text-gray-600">Candidate Number:</span>
                  <div className="font-medium">{selectedTestData.registration.candidateNumber}</div>
                </div>
                <div>
                  <span className="text-gray-600">Overall Band Score:</span>
                  <div className="font-bold text-blue-600 text-lg">{selectedTestData.result.overallBandScore}</div>
                </div>
              </div>
            </div>

            <div className="text-sm text-gray-600">
              This is a preview. Unlock the full certificate with verification features.
            </div>
          </div>
        </div>

        {/* Certificate Features */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-900 mb-4">Certificate Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start space-x-3">
              <Shield className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <div className="font-medium text-yellow-900">Official Verification</div>
                <div className="text-sm text-yellow-800">Digitally signed and verifiable</div>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <QrCode className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <div className="font-medium text-yellow-900">QR Code Verification</div>
                <div className="text-sm text-yellow-800">Instant verification via QR code</div>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Download className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <div className="font-medium text-yellow-900">PDF Download</div>
                <div className="text-sm text-yellow-800">High-quality PDF format</div>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Share2 className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <div className="font-medium text-yellow-900">Shareable Link</div>
                <div className="text-sm text-yellow-800">Share with institutions</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderPremiumCertificate = () => {
    const selectedTestData = testData.find(test => test.result.id === selectedTest) || testData[0];

    // Show loading state
    if (certificateLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">Loading certificate information...</p>
          </div>
        </div>
      );
    }

    // Show error state
    if (certificateError) {
      return (
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{certificateError}</p>
          <Button onClick={fetchCertificate} variant="outline">
            Try Again
          </Button>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">Official Certificate</h2>
          <Badge variant="green">Premium Feature</Badge>
        </div>

        {/* Test Selection */}
        {testData.length > 1 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Test for Certificate
            </label>
            <select
              value={selectedTest}
              onChange={(e) => setSelectedTest(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {testData.map((test) => (
                <option key={test.result.id} value={test.result.id}>
                  {formatDate(test.registration.testDate)} - Band {test.result.overallBandScore}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Official Certificate */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 border-2 border-blue-200 rounded-lg p-8">
          <div className="text-center mb-6">
            <div className="bg-blue-600 p-3 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Award className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">Official IELTS Certificate</h3>
            <Badge variant="green" className="mb-4">Verified & Authentic</Badge>
          </div>

          <div className="bg-white rounded-lg p-8 shadow-lg border-2 border-gray-200">
            {/* Certificate Header */}
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-blue-900 mb-2">IELTS</h2>
              <h3 className="text-xl text-gray-700">International English Language Testing System</h3>
              <div className="w-24 h-1 bg-blue-600 mx-auto mt-4"></div>
            </div>

            {/* Certificate Body */}
            <div className="text-center mb-8">
              <p className="text-lg text-gray-700 mb-4">This is to certify that</p>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">{candidate.fullName}</h2>
              <p className="text-lg text-gray-700 mb-2">has achieved an overall band score of</p>
              <div className="text-4xl font-bold text-blue-600 mb-4">{selectedTestData.result.overallBandScore}</div>
              <p className="text-lg text-gray-700">in the IELTS test</p>
            </div>

            {/* Score Breakdown */}
            <div className="grid grid-cols-4 gap-4 mb-8">
              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">{selectedTestData.result.listeningBandScore}</div>
                <div className="text-sm text-gray-600">Listening</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">{selectedTestData.result.readingBandScore}</div>
                <div className="text-sm text-gray-600">Reading</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">{selectedTestData.result.writingBandScore}</div>
                <div className="text-sm text-gray-600">Writing</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">{selectedTestData.result.speakingBandScore}</div>
                <div className="text-sm text-gray-600">Speaking</div>
              </div>
            </div>

            {/* Certificate Details */}
            <div className="border-t border-gray-200 pt-6">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Test Date:</span>
                  <div className="font-medium">{formatDate(selectedTestData.registration.testDate)}</div>
                </div>
                <div>
                  <span className="text-gray-600">Test Center:</span>
                  <div className="font-medium">{selectedTestData.registration.testCenter}</div>
                </div>
                <div>
                  <span className="text-gray-600">Candidate Number:</span>
                  <div className="font-medium">{selectedTestData.registration.candidateNumber}</div>
                </div>
                <div>
                  <span className="text-gray-600">Certificate ID:</span>
                  <div className="font-medium">CERT-{selectedTestData.result.id.slice(-8).toUpperCase()}</div>
                </div>
              </div>
            </div>

            {/* QR Code and Verification */}
            <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
              <div className="flex items-center space-x-3">
                <QrCode className="h-8 w-8 text-gray-600" />
                <div className="text-xs text-gray-600">
                  <div>Scan to verify</div>
                  <div>authenticity</div>
                </div>
              </div>
              <div className="text-right text-xs text-gray-600">
                <div>Issued: {formatDate(selectedTestData.result.createdAt)}</div>
                <div>Valid for 2 years</div>
              </div>
            </div>
          </div>
        </div>

        {/* Certificate Status and Actions */}
        {certificate ? (
          <div className="space-y-4">
            {/* Certificate Info */}
            <div className="bg-white rounded-lg p-6 border">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Certificate Information</h3>
                <Badge
                  variant={certificate.status === 'active' ? "default" : "destructive"}
                  className={certificate.status === 'active' ? "bg-green-100 text-green-800" : ""}
                >
                  {certificate.status.toUpperCase()}
                </Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <label className="text-gray-500">Serial Number</label>
                  <p className="font-mono text-lg">{certificate.serialNumber}</p>
                </div>
                <div>
                  <label className="text-gray-500">Issue Date</label>
                  <p className="text-lg">{new Date(certificate.generatedAt).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="text-gray-500">Expiry Date</label>
                  <p className="text-lg">{new Date(certificate.expiresAt).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="text-gray-500">Validity</label>
                  <p className={`text-lg ${new Date() > new Date(certificate.expiresAt) ? 'text-orange-600' : 'text-green-600'}`}>
                    {new Date() > new Date(certificate.expiresAt) ? 'Expired' : 'Valid'}
                  </p>
                </div>
              </div>
            </div>

            {/* Certificate Actions */}
            <div className="flex flex-wrap gap-4">
              <Button
                onClick={downloadCertificate}
                className="flex-1 sm:flex-none"
                disabled={certificate.status === 'deleted'}
              >
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
              <Button
                variant="outline"
                className="flex-1 sm:flex-none"
                onClick={() => window.open(`/verify/${certificate.serialNumber}`, '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Verify Online
              </Button>
              <Button
                variant="outline"
                className="flex-1 sm:flex-none"
                onClick={() => {
                  const url = `${window.location.origin}/verify/${certificate.serialNumber}`;
                  navigator.clipboard.writeText(url);
                  // You could add a toast notification here
                }}
              >
                <Share2 className="h-4 w-4 mr-2" />
                Copy Link
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Certificate Generated</h3>
            <p className="text-gray-600 mb-6">
              Generate an official IELTS certificate for your test results.
            </p>
            <Button
              onClick={generateCertificate}
              disabled={generating}
              className="flex items-center gap-2"
            >
              {generating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4" />
                  Generate Certificate
                </>
              )}
            </Button>
          </div>
        )}

        {/* Verification Info */}
        {certificate && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-900 mb-4 flex items-center">
              <CheckCircle className="h-5 w-5 mr-2" />
              Certificate Verification
            </h3>
            <div className="space-y-3 text-sm text-green-800">
              <p>
                <strong>Verification URL:</strong>
                <span className="ml-2 font-mono bg-white px-2 py-1 rounded">
                  {window.location.origin}/verify/{certificate.serialNumber}
                </span>
              </p>
              <p>
                <strong>Serial Number:</strong> {certificate.serialNumber}
              </p>
              <p>
                This certificate is digitally signed and can be verified by institutions worldwide.
                The QR code provides instant verification of authenticity.
              </p>
            </div>
          </div>
        )}

        {/* Certificate Information */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">Certificate Information</h3>
          <div className="space-y-3 text-sm text-blue-800">
            <p>• This certificate is an official document verifying your IELTS test results.</p>
            <p>• The certificate is valid for 6 months from the test date.</p>
            <p>• You can verify the authenticity of this certificate using the QR code or serial number.</p>
            <p>• The certificate includes a secure verification system to prevent fraud.</p>
            <p>• After expiration, the certificate will be automatically deleted from our system.</p>
          </div>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!hasAccess) {
    return (
      <PaywallOverlay
        isOpen={showPaywall}
        onClose={() => setShowPaywall(false)}
        featureType="certificate"
        candidateId={candidate.id}
        resultId={selectedTest}
        onPaymentSuccess={() => window.location.reload()}
      >
        <div className="relative">
          <div className="filter blur-sm pointer-events-none">
            {renderBasicCertificate()}
          </div>
          <div className="absolute inset-0 bg-white/80 flex items-center justify-center">
            <div className="text-center p-6">
              <Lock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Official IELTS Certificate
              </h3>
              <p className="text-gray-600 mb-4">
                Download your official certificate with verification QR code and digital signature
              </p>
              <Button onClick={() => setShowPaywall(true)}>
                <Award className="h-4 w-4 mr-2" />
                Get Certificate - 30,000 UZS
              </Button>
            </div>
          </div>
        </div>
      </PaywallOverlay>
    );
  }

  return renderPremiumCertificate();
}
