import { Inter } from 'next/font/google';
import '../globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'IELTS Results Portal - Search Your Test Results',
  description: 'Search and view your IELTS test results, progress analytics, AI feedback, and official certificates.',
  keywords: 'IELTS, test results, English proficiency, band score, certificate',
};

export default function PublicLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <main>{children}</main>
      </body>
    </html>
  );
}
