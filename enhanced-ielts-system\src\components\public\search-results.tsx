import { db } from '@/lib/db';
import { candidates, testRegistrations, testResults, organizations } from '@/lib/db/schema';
import { eq, and, desc } from 'drizzle-orm';
import { PublicResultsInterface } from './public-results-interface';
import { Search, AlertCircle, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

interface SearchResultsProps {
  passport: string;
  dateOfBirth: string;
}

export async function SearchResults({ passport, dateOfBirth }: SearchResultsProps) {
  try {
    // Get candidate with test results
    const candidateData = await db
      .select({
        candidate: candidates,
        organization: organizations,
      })
      .from(candidates)
      .leftJoin(organizations, eq(candidates.organizationId, organizations.id))
      .where(
        and(
          eq(candidates.passportNumber, passport.trim()),
          eq(candidates.dateOfBirth, new Date(dateOfBirth))
        )
      )
      .limit(1);

    if (candidateData.length === 0) {
      return (
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="bg-red-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">No Results Found</h2>
            <p className="text-gray-600 mb-6">
              We couldn't find any test results for the provided passport/birth certificate number and date of birth.
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-yellow-800 mb-2">Please check:</h3>
              <ul className="text-sm text-yellow-700 text-left space-y-1">
                <li>• Your passport/birth certificate number is entered correctly</li>
                <li>• Your date of birth matches your registration details</li>
                <li>• You have taken a test at one of our participating centers</li>
                <li>• Your results have been processed (usually within 24 hours)</li>
              </ul>
            </div>
            <Link href="/search">
              <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Try Another Search
              </button>
            </Link>
          </div>
        </div>
      );
    }

    const { candidate, organization } = candidateData[0];

    // Get all test registrations and results for this candidate
    const testData = await db
      .select({
        registration: testRegistrations,
        result: testResults,
      })
      .from(testRegistrations)
      .leftJoin(testResults, eq(testRegistrations.id, testResults.testRegistrationId))
      .where(eq(testRegistrations.candidateId, candidate.id))
      .orderBy(desc(testRegistrations.testDate));

    if (testData.length === 0) {
      return (
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Search className="h-8 w-8 text-blue-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Candidate Found</h2>
            <p className="text-gray-600 mb-6">
              We found your profile, but you don't have any test registrations yet.
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-blue-800 mb-2">Next Steps:</h3>
              <ul className="text-sm text-blue-700 text-left space-y-1">
                <li>• Contact your test center to register for an IELTS test</li>
                <li>• Results will appear here after test completion</li>
                <li>• Check back after your test date for results</li>
              </ul>
            </div>
            <div className="text-sm text-gray-600">
              <p><strong>Test Center:</strong> {organization?.name || 'Unknown'}</p>
              <p><strong>Candidate:</strong> {candidate.fullName}</p>
            </div>
            <Link href="/search" className="mt-4 inline-block">
              <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <ArrowLeft className="h-4 w-4 mr-2" />
                New Search
              </button>
            </Link>
          </div>
        </div>
      );
    }

    // Render the full results interface
    return (
      <PublicResultsInterface
        candidate={candidate}
        organization={organization}
        testData={testData}
      />
    );
  } catch (error) {
    console.error('Search results error:', error);
    
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="bg-red-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Search Error</h2>
          <p className="text-gray-600 mb-6">
            We encountered an error while searching for your results. Please try again.
          </p>
          <Link href="/search">
            <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Try Again
            </button>
          </Link>
        </div>
      </div>
    );
  }
}
