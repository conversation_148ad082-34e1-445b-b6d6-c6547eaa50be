'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Modal } from '@/components/ui/modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { calculateBandScore, calculateOverallBandScore } from '@/lib/utils/ielts-scoring';
import { FileText, Loader2, Calculator } from 'lucide-react';

const testResultSchema = z.object({
  listeningScore: z.number().min(0).max(40),
  readingScore: z.number().min(0).max(40),
  writingTask1Score: z.number().min(0).max(9).step(0.5),
  writingTask2Score: z.number().min(0).max(9).step(0.5),
  speakingFluencyScore: z.number().min(0).max(9).step(0.5),
  speakingLexicalScore: z.number().min(0).max(9).step(0.5),
  speakingGrammarScore: z.number().min(0).max(9).step(0.5),
  speakingPronunciationScore: z.number().min(0).max(9).step(0.5),
});

type TestResultForm = z.infer<typeof testResultSchema>;

interface TestResultEntryModalProps {
  isOpen: boolean;
  onClose: () => void;
  registration: {
    registration: {
      id: string;
      candidateNumber: string;
      testDate: Date;
      testCenter: string;
    };
    candidate: {
      fullName: string;
      passportNumber: string;
    } | null;
    result?: {
      id: string;
      listeningScore: number | null;
      readingScore: number | null;
      writingTask1Score: string | null;
      writingTask2Score: string | null;
      speakingFluencyScore: string | null;
      speakingLexicalScore: string | null;
      speakingGrammarScore: string | null;
      speakingPronunciationScore: string | null;
    } | null;
  };
}

export function TestResultEntryModal({ isOpen, onClose, registration }: TestResultEntryModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [calculatedScores, setCalculatedScores] = useState({
    listeningBand: 0,
    readingBand: 0,
    writingBand: 0,
    speakingBand: 0,
    overallBand: 0,
  });

  const {
    register,
    handleSubmit,
    watch,
    reset,
    setValue,
    formState: { errors },
  } = useForm<TestResultForm>({
    resolver: zodResolver(testResultSchema),
    defaultValues: registration.result ? {
      listeningScore: registration.result.listeningScore || 0,
      readingScore: registration.result.readingScore || 0,
      writingTask1Score: parseFloat(registration.result.writingTask1Score || '0'),
      writingTask2Score: parseFloat(registration.result.writingTask2Score || '0'),
      speakingFluencyScore: parseFloat(registration.result.speakingFluencyScore || '0'),
      speakingLexicalScore: parseFloat(registration.result.speakingLexicalScore || '0'),
      speakingGrammarScore: parseFloat(registration.result.speakingGrammarScore || '0'),
      speakingPronunciationScore: parseFloat(registration.result.speakingPronunciationScore || '0'),
    } : undefined,
  });

  // Watch form values for real-time calculation
  const watchedValues = watch();

  // Calculate band scores in real-time
  useEffect(() => {
    const listeningBand = calculateBandScore('listening', watchedValues.listeningScore || 0);
    const readingBand = calculateBandScore('reading', watchedValues.readingScore || 0);
    
    // Writing band score is average of task 1 and task 2
    const writingBand = ((watchedValues.writingTask1Score || 0) + (watchedValues.writingTask2Score || 0)) / 2;
    
    // Speaking band score is average of all speaking components
    const speakingBand = (
      (watchedValues.speakingFluencyScore || 0) +
      (watchedValues.speakingLexicalScore || 0) +
      (watchedValues.speakingGrammarScore || 0) +
      (watchedValues.speakingPronunciationScore || 0)
    ) / 4;

    const overallBand = calculateOverallBandScore(listeningBand, readingBand, writingBand, speakingBand);

    setCalculatedScores({
      listeningBand,
      readingBand,
      writingBand,
      speakingBand,
      overallBand,
    });
  }, [watchedValues]);

  const onSubmit = async (data: TestResultForm) => {
    setIsLoading(true);
    try {
      const resultData = {
        testRegistrationId: registration.registration.id,
        ...data,
        listeningBandScore: calculatedScores.listeningBand,
        readingBandScore: calculatedScores.readingBand,
        writingBandScore: calculatedScores.writingBand,
        speakingBandScore: calculatedScores.speakingBand,
        overallBandScore: calculatedScores.overallBand,
      };

      const url = registration.result 
        ? `/api/test-results/${registration.result.id}`
        : '/api/test-results';
      
      const method = registration.result ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(resultData),
      });

      if (!response.ok) {
        throw new Error('Failed to save test result');
      }

      reset();
      onClose();
      window.location.reload(); // Refresh to show updated results
    } catch (error) {
      console.error('Error saving test result:', error);
      alert('Failed to save test result. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="large">
      <div className="p-6">
        <div className="flex items-center mb-6">
          <FileText className="h-6 w-6 text-blue-600 mr-2" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {registration.result ? 'Edit Test Result' : 'Enter Test Result'}
            </h2>
            <p className="text-sm text-gray-600">
              {registration.candidate?.fullName} - {registration.registration.candidateNumber}
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Listening Section */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Listening</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Raw Score (0-40)
                </label>
                <Input
                  type="number"
                  min="0"
                  max="40"
                  {...register('listeningScore', { valueAsNumber: true })}
                  error={errors.listeningScore?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Band Score (Calculated)
                </label>
                <div className="px-3 py-2 bg-white border border-gray-300 rounded-md text-lg font-bold text-blue-600">
                  {calculatedScores.listeningBand.toFixed(1)}
                </div>
              </div>
            </div>
          </div>

          {/* Reading Section */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Reading</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Raw Score (0-40)
                </label>
                <Input
                  type="number"
                  min="0"
                  max="40"
                  {...register('readingScore', { valueAsNumber: true })}
                  error={errors.readingScore?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Band Score (Calculated)
                </label>
                <div className="px-3 py-2 bg-white border border-gray-300 rounded-md text-lg font-bold text-blue-600">
                  {calculatedScores.readingBand.toFixed(1)}
                </div>
              </div>
            </div>
          </div>

          {/* Writing Section */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Writing</h3>
            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Task 1 Score (0-9)
                </label>
                <Input
                  type="number"
                  min="0"
                  max="9"
                  step="0.5"
                  {...register('writingTask1Score', { valueAsNumber: true })}
                  error={errors.writingTask1Score?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Task 2 Score (0-9)
                </label>
                <Input
                  type="number"
                  min="0"
                  max="9"
                  step="0.5"
                  {...register('writingTask2Score', { valueAsNumber: true })}
                  error={errors.writingTask2Score?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Band Score (Average)
                </label>
                <div className="px-3 py-2 bg-white border border-gray-300 rounded-md text-lg font-bold text-blue-600">
                  {calculatedScores.writingBand.toFixed(1)}
                </div>
              </div>
            </div>
          </div>

          {/* Speaking Section */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Speaking</h3>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fluency & Coherence (0-9)
                </label>
                <Input
                  type="number"
                  min="0"
                  max="9"
                  step="0.5"
                  {...register('speakingFluencyScore', { valueAsNumber: true })}
                  error={errors.speakingFluencyScore?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Lexical Resource (0-9)
                </label>
                <Input
                  type="number"
                  min="0"
                  max="9"
                  step="0.5"
                  {...register('speakingLexicalScore', { valueAsNumber: true })}
                  error={errors.speakingLexicalScore?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Grammatical Range (0-9)
                </label>
                <Input
                  type="number"
                  min="0"
                  max="9"
                  step="0.5"
                  {...register('speakingGrammarScore', { valueAsNumber: true })}
                  error={errors.speakingGrammarScore?.message}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Pronunciation (0-9)
                </label>
                <Input
                  type="number"
                  min="0"
                  max="9"
                  step="0.5"
                  {...register('speakingPronunciationScore', { valueAsNumber: true })}
                  error={errors.speakingPronunciationScore?.message}
                />
              </div>
            </div>
            <div className="text-center">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Speaking Band Score (Average)
              </label>
              <div className="inline-block px-4 py-2 bg-white border border-gray-300 rounded-md text-lg font-bold text-blue-600">
                {calculatedScores.speakingBand.toFixed(1)}
              </div>
            </div>
          </div>

          {/* Overall Score */}
          <div className="bg-blue-50 p-4 rounded-lg text-center">
            <div className="flex items-center justify-center mb-2">
              <Calculator className="h-5 w-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Overall Band Score</h3>
            </div>
            <div className="text-3xl font-bold text-blue-600">
              {calculatedScores.overallBand.toFixed(1)}
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                registration.result ? 'Update Result' : 'Save Result'
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
