import { NextRequest, NextResponse } from 'next/server';
import { processClickWebhook } from '@/lib/payments/click';
import { updatePaymentStatus } from '@/lib/payments/utils';

export async function POST(request: NextRequest) {
  try {
    // Get the raw body for signature verification
    const body = await request.json();
    
    console.log('Click webhook received:', body);

    // Process the webhook
    const result = await processClickWebhook(body);

    if (!result.success) {
      console.error('Click webhook processing failed:', result.error);
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    // Update payment status in database
    if (result.transactionId) {
      const status = body.error === 0 ? 'completed' : 'failed';
      const gatewayTransactionId = body.click_trans_id;
      
      await updatePaymentStatus(
        result.transactionId,
        status,
        gatewayTransactionId,
        {
          gatewayResponse: body,
          failureReason: body.error !== 0 ? body.error_note : undefined,
        }
      );
    }

    // Return success response expected by Click
    return NextResponse.json({
      click_trans_id: body.click_trans_id,
      merchant_trans_id: body.merchant_trans_id,
      merchant_prepare_id: body.merchant_prepare_id,
      error: 0,
      error_note: 'Success',
    });
  } catch (error) {
    console.error('Click webhook error:', error);
    
    // Return error response expected by Click
    return NextResponse.json({
      click_trans_id: '',
      merchant_trans_id: '',
      merchant_prepare_id: '',
      error: -1,
      error_note: 'Internal server error',
    }, { status: 500 });
  }
}

// Handle GET requests (Click sometimes sends test requests)
export async function GET(request: NextRequest) {
  return NextResponse.json({
    status: 'Click webhook endpoint is active',
    timestamp: new Date().toISOString(),
  });
}
