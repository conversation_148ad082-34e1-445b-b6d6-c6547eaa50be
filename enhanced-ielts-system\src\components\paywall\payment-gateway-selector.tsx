'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ManualPaymentModal } from './manual-payment-modal';
import { 
  CreditCard, 
  Smartphone, 
  Building2, 
  Clock, 
  CheckCircle,
  ExternalLink,
  Loader2
} from 'lucide-react';
import { PaymentGateway, FeatureType } from '@/lib/payments/types';

interface PaymentGatewaySelectorProps {
  candidateId: string;
  featureType: FeatureType;
  resultId?: string;
  amount: number;
  currency: string;
  onPaymentComplete: () => void;
  onPaymentError: (error: string) => void;
  onProcessingChange: (processing: boolean) => void;
}

export function PaymentGatewaySelector({
  candidateId,
  featureType,
  resultId,
  amount,
  currency,
  onPaymentComplete,
  onPaymentError,
  onProcessingChange,
}: PaymentGatewaySelectorProps) {
  const [selectedGateway, setSelectedGateway] = useState<PaymentGateway | null>(null);
  const [showManualPayment, setShowManualPayment] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const gateways = [
    {
      id: 'click' as PaymentGateway,
      name: 'Click',
      icon: <CreditCard className="h-6 w-6" />,
      description: 'Pay with Click - Fast and secure',
      processingTime: 'Instant',
      badge: 'Recommended',
      badgeColor: 'green' as const,
    },
    {
      id: 'payme' as PaymentGateway,
      name: 'Payme',
      icon: <Smartphone className="h-6 w-6" />,
      description: 'Pay with Payme - Convenient mobile payments',
      processingTime: 'Instant',
      badge: 'Popular',
      badgeColor: 'blue' as const,
    },
    {
      id: 'manual' as PaymentGateway,
      name: 'Manual Payment',
      icon: <Building2 className="h-6 w-6" />,
      description: 'Bank transfer or cash payment with admin approval',
      processingTime: '1-3 business days',
      badge: 'Manual Approval',
      badgeColor: 'orange' as const,
    },
  ];

  const handleGatewaySelect = async (gateway: PaymentGateway) => {
    if (gateway === 'manual') {
      setShowManualPayment(true);
      return;
    }

    setSelectedGateway(gateway);
    setIsProcessing(true);
    onProcessingChange(true);

    try {
      // Initiate payment
      const response = await fetch('/api/payments/initiate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          candidateId,
          featureType,
          gateway,
          resultId,
          returnUrl: `${window.location.origin}/payment/return`,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Payment initiation failed');
      }

      if (data.paymentUrl) {
        // Redirect to payment gateway
        window.location.href = data.paymentUrl;
      } else {
        throw new Error('No payment URL received');
      }
    } catch (error) {
      console.error('Payment initiation error:', error);
      onPaymentError(error instanceof Error ? error.message : 'Payment failed');
      setIsProcessing(false);
      onProcessingChange(false);
      setSelectedGateway(null);
    }
  };

  const handleManualPaymentSubmit = () => {
    setShowManualPayment(false);
    onPaymentComplete();
  };

  return (
    <>
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Select Payment Method
        </h3>

        {gateways.map((gateway) => (
          <div
            key={gateway.id}
            className={`
              relative border rounded-lg p-4 cursor-pointer transition-all
              ${selectedGateway === gateway.id 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }
              ${isProcessing && selectedGateway !== gateway.id ? 'opacity-50 pointer-events-none' : ''}
            `}
            onClick={() => !isProcessing && handleGatewaySelect(gateway.id)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className={`
                  p-2 rounded-lg
                  ${selectedGateway === gateway.id ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'}
                `}>
                  {gateway.icon}
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <h4 className="font-medium text-gray-900">{gateway.name}</h4>
                    <Badge variant={gateway.badgeColor}>
                      {gateway.badge}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{gateway.description}</p>
                  <div className="flex items-center mt-1">
                    <Clock className="h-4 w-4 text-gray-400 mr-1" />
                    <span className="text-xs text-gray-500">{gateway.processingTime}</span>
                  </div>
                </div>
              </div>

              <div className="flex items-center">
                {isProcessing && selectedGateway === gateway.id ? (
                  <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
                ) : selectedGateway === gateway.id ? (
                  <CheckCircle className="h-5 w-5 text-blue-600" />
                ) : (
                  <ExternalLink className="h-5 w-5 text-gray-400" />
                )}
              </div>
            </div>

            {/* Gateway-specific information */}
            {gateway.id === 'click' && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <div className="text-xs text-gray-500">
                  • Supports all major cards and Click wallet
                  • Instant confirmation
                  • Secure 3D authentication
                </div>
              </div>
            )}

            {gateway.id === 'payme' && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <div className="text-xs text-gray-500">
                  • Mobile-first payment experience
                  • QR code and SMS payments
                  • Instant confirmation
                </div>
              </div>
            )}

            {gateway.id === 'manual' && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <div className="text-xs text-gray-500">
                  • Bank transfer or cash payment
                  • Requires admin approval
                  • Upload payment proof
                </div>
              </div>
            )}
          </div>
        ))}

        {/* Security Notice */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-start space-x-3">
            <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
            <div className="text-sm text-gray-600">
              <div className="font-medium text-gray-900 mb-1">Secure Payment</div>
              <div>
                All payments are processed securely with 256-bit SSL encryption. 
                Your payment information is never stored on our servers.
              </div>
            </div>
          </div>
        </div>

        {/* Terms */}
        <div className="text-xs text-gray-500 text-center">
          By proceeding with payment, you agree to our{' '}
          <a href="/terms" className="text-blue-600 hover:underline">
            Terms of Service
          </a>{' '}
          and{' '}
          <a href="/privacy" className="text-blue-600 hover:underline">
            Privacy Policy
          </a>
        </div>
      </div>

      {/* Manual Payment Modal */}
      <ManualPaymentModal
        isOpen={showManualPayment}
        onClose={() => setShowManualPayment(false)}
        candidateId={candidateId}
        featureType={featureType}
        resultId={resultId}
        amount={amount}
        currency={currency}
        onSubmit={handleManualPaymentSubmit}
      />
    </>
  );
}
