/**
 * Click Payment Gateway Integration
 * Official Click API for Uzbekistan
 */

import { z } from 'zod';

// Click API Configuration
const CLICK_CONFIG = {
  baseUrl: process.env.CLICK_BASE_URL || 'https://api.click.uz/v2',
  merchantId: process.env.CLICK_MERCHANT_ID || '',
  serviceId: process.env.CLICK_SERVICE_ID || '',
  secretKey: process.env.CLICK_SECRET_KEY || '',
  userId: process.env.CLICK_USER_ID || '',
};

// Click API Types
export interface ClickPaymentRequest {
  amount: number;
  merchant_trans_id: string;
  return_url: string;
  merchant_prepare_id?: string;
  merchant_user_id?: string;
}

export interface ClickPaymentResponse {
  click_trans_id: string;
  merchant_trans_id: string;
  merchant_prepare_id: string;
  error: number;
  error_note: string;
}

export interface ClickWebhookPayload {
  click_trans_id: string;
  service_id: string;
  click_paydoc_id: string;
  merchant_trans_id: string;
  merchant_prepare_id: string;
  amount: number;
  action: number;
  error: number;
  error_note: string;
  sign_time: string;
  sign_string: string;
}

// Validation schemas
const clickPaymentRequestSchema = z.object({
  amount: z.number().positive(),
  merchant_trans_id: z.string().min(1),
  return_url: z.string().url(),
  merchant_prepare_id: z.string().optional(),
  merchant_user_id: z.string().optional(),
});

const clickWebhookSchema = z.object({
  click_trans_id: z.string(),
  service_id: z.string(),
  click_paydoc_id: z.string(),
  merchant_trans_id: z.string(),
  merchant_prepare_id: z.string(),
  amount: z.number(),
  action: z.number(),
  error: z.number(),
  error_note: z.string(),
  sign_time: z.string(),
  sign_string: z.string(),
});

/**
 * Generate MD5 hash for Click API signature
 */
function generateMD5(data: string): string {
  const crypto = require('crypto');
  return crypto.createHash('md5').update(data).digest('hex');
}

/**
 * Generate Click API signature
 */
function generateClickSignature(
  clickTransId: string,
  serviceId: string,
  secretKey: string,
  merchantTransId: string,
  merchantPrepareId: string,
  amount: number,
  action: number,
  signTime: string
): string {
  const signString = `${clickTransId}${serviceId}${secretKey}${merchantTransId}${merchantPrepareId}${amount}${action}${signTime}`;
  return generateMD5(signString);
}

/**
 * Verify Click webhook signature
 */
export function verifyClickSignature(payload: ClickWebhookPayload): boolean {
  const expectedSignature = generateClickSignature(
    payload.click_trans_id,
    payload.service_id,
    CLICK_CONFIG.secretKey,
    payload.merchant_trans_id,
    payload.merchant_prepare_id,
    payload.amount,
    payload.action,
    payload.sign_time
  );
  
  return expectedSignature === payload.sign_string;
}

/**
 * Create Click payment URL
 */
export async function createClickPayment(request: ClickPaymentRequest): Promise<string> {
  // Validate request
  const validatedRequest = clickPaymentRequestSchema.parse(request);
  
  // Generate payment URL
  const params = new URLSearchParams({
    service_id: CLICK_CONFIG.serviceId,
    merchant_id: CLICK_CONFIG.merchantId,
    amount: validatedRequest.amount.toString(),
    transaction_param: validatedRequest.merchant_trans_id,
    return_url: validatedRequest.return_url,
  });

  if (validatedRequest.merchant_user_id) {
    params.append('merchant_user_id', validatedRequest.merchant_user_id);
  }

  return `${CLICK_CONFIG.baseUrl}/services/pay?${params.toString()}`;
}

/**
 * Process Click webhook
 */
export async function processClickWebhook(payload: any): Promise<{
  success: boolean;
  error?: string;
  transactionId?: string;
}> {
  try {
    // Validate payload
    const validatedPayload = clickWebhookSchema.parse(payload);
    
    // Verify signature
    if (!verifyClickSignature(validatedPayload)) {
      return {
        success: false,
        error: 'Invalid signature',
      };
    }

    // Check if it's our service
    if (validatedPayload.service_id !== CLICK_CONFIG.serviceId) {
      return {
        success: false,
        error: 'Invalid service ID',
      };
    }

    // Process based on action
    switch (validatedPayload.action) {
      case 0: // Prepare
        return await handleClickPrepare(validatedPayload);
      case 1: // Complete
        return await handleClickComplete(validatedPayload);
      default:
        return {
          success: false,
          error: 'Unknown action',
        };
    }
  } catch (error) {
    console.error('Click webhook processing error:', error);
    return {
      success: false,
      error: 'Processing failed',
    };
  }
}

/**
 * Handle Click prepare action
 */
async function handleClickPrepare(payload: ClickWebhookPayload): Promise<{
  success: boolean;
  error?: string;
  transactionId?: string;
}> {
  // Check if transaction exists and is valid
  // This should validate against your database
  
  if (payload.error !== 0) {
    return {
      success: false,
      error: payload.error_note,
    };
  }

  return {
    success: true,
    transactionId: payload.merchant_trans_id,
  };
}

/**
 * Handle Click complete action
 */
async function handleClickComplete(payload: ClickWebhookPayload): Promise<{
  success: boolean;
  error?: string;
  transactionId?: string;
}> {
  if (payload.error !== 0) {
    return {
      success: false,
      error: payload.error_note,
    };
  }

  // Mark transaction as completed in database
  // Grant access to premium features
  
  return {
    success: true,
    transactionId: payload.merchant_trans_id,
  };
}

/**
 * Get Click transaction status
 */
export async function getClickTransactionStatus(transactionId: string): Promise<{
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  amount?: number;
  error?: string;
}> {
  try {
    // This would typically make an API call to Click to check status
    // For now, return a placeholder implementation
    
    return {
      status: 'pending',
    };
  } catch (error) {
    console.error('Click status check error:', error);
    return {
      status: 'failed',
      error: 'Status check failed',
    };
  }
}

/**
 * Cancel Click transaction
 */
export async function cancelClickTransaction(transactionId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Implementation for canceling Click transaction
    
    return {
      success: true,
    };
  } catch (error) {
    console.error('Click cancellation error:', error);
    return {
      success: false,
      error: 'Cancellation failed',
    };
  }
}
