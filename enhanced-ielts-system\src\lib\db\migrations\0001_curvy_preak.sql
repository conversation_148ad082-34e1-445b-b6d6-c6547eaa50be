CREATE TABLE "promotional_code_usage" (
	"id" text PRIMARY KEY NOT NULL,
	"code_id" text NOT NULL,
	"candidate_id" text NOT NULL,
	"result_id" text,
	"feature_type" text NOT NULL,
	"original_amount" numeric(10, 2) NOT NULL,
	"discount_amount" numeric(10, 2) NOT NULL,
	"final_amount" numeric(10, 2) NOT NULL,
	"payment_transaction_id" text,
	"used_at" timestamp DEFAULT now() NOT NULL,
	"metadata" json
);
--> statement-breakpoint
CREATE TABLE "promotional_codes" (
	"id" text PRIMARY KEY NOT NULL,
	"organization_id" text NOT NULL,
	"code" text NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"type" text NOT NULL,
	"value" numeric(10, 2) NOT NULL,
	"feature_type" text NOT NULL,
	"status" text DEFAULT 'active',
	"valid_from" timestamp NOT NULL,
	"valid_until" timestamp NOT NULL,
	"usage_limit" integer,
	"usage_count" integer DEFAULT 0,
	"min_purchase_amount" numeric(10, 2),
	"max_discount_amount" numeric(10, 2),
	"is_public" boolean DEFAULT false,
	"created_by" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "org_code_unique" UNIQUE("organization_id","code")
);
--> statement-breakpoint
CREATE TABLE "referral_conversions" (
	"id" text PRIMARY KEY NOT NULL,
	"referral_link_id" text NOT NULL,
	"referred_candidate_id" text NOT NULL,
	"payment_transaction_id" text,
	"commission_amount" numeric(10, 2) NOT NULL,
	"commission_rate" numeric(5, 2) NOT NULL,
	"status" text DEFAULT 'pending',
	"converted_at" timestamp DEFAULT now() NOT NULL,
	"paid_at" timestamp,
	"metadata" json
);
--> statement-breakpoint
CREATE TABLE "referral_links" (
	"id" text PRIMARY KEY NOT NULL,
	"organization_id" text NOT NULL,
	"candidate_id" text NOT NULL,
	"referral_code" text NOT NULL,
	"click_count" integer DEFAULT 0,
	"conversion_count" integer DEFAULT 0,
	"total_earnings" numeric(10, 2) DEFAULT '0',
	"status" text DEFAULT 'active',
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "referral_links_referral_code_unique" UNIQUE("referral_code")
);
--> statement-breakpoint
ALTER TABLE "promotional_code_usage" ADD CONSTRAINT "promotional_code_usage_code_id_promotional_codes_id_fk" FOREIGN KEY ("code_id") REFERENCES "public"."promotional_codes"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "promotional_code_usage" ADD CONSTRAINT "promotional_code_usage_candidate_id_candidates_id_fk" FOREIGN KEY ("candidate_id") REFERENCES "public"."candidates"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "promotional_code_usage" ADD CONSTRAINT "promotional_code_usage_result_id_test_results_id_fk" FOREIGN KEY ("result_id") REFERENCES "public"."test_results"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "promotional_code_usage" ADD CONSTRAINT "promotional_code_usage_payment_transaction_id_payment_transactions_id_fk" FOREIGN KEY ("payment_transaction_id") REFERENCES "public"."payment_transactions"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "promotional_codes" ADD CONSTRAINT "promotional_codes_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "promotional_codes" ADD CONSTRAINT "promotional_codes_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "referral_conversions" ADD CONSTRAINT "referral_conversions_referral_link_id_referral_links_id_fk" FOREIGN KEY ("referral_link_id") REFERENCES "public"."referral_links"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "referral_conversions" ADD CONSTRAINT "referral_conversions_referred_candidate_id_candidates_id_fk" FOREIGN KEY ("referred_candidate_id") REFERENCES "public"."candidates"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "referral_conversions" ADD CONSTRAINT "referral_conversions_payment_transaction_id_payment_transactions_id_fk" FOREIGN KEY ("payment_transaction_id") REFERENCES "public"."payment_transactions"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "referral_links" ADD CONSTRAINT "referral_links_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "referral_links" ADD CONSTRAINT "referral_links_candidate_id_candidates_id_fk" FOREIGN KEY ("candidate_id") REFERENCES "public"."candidates"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "promo_usage_code_idx" ON "promotional_code_usage" USING btree ("code_id");--> statement-breakpoint
CREATE INDEX "promo_usage_candidate_idx" ON "promotional_code_usage" USING btree ("candidate_id");--> statement-breakpoint
CREATE INDEX "promo_usage_date_idx" ON "promotional_code_usage" USING btree ("used_at");--> statement-breakpoint
CREATE INDEX "promo_code_idx" ON "promotional_codes" USING btree ("code");--> statement-breakpoint
CREATE INDEX "promo_code_status_idx" ON "promotional_codes" USING btree ("status");--> statement-breakpoint
CREATE INDEX "promo_code_validity_idx" ON "promotional_codes" USING btree ("valid_from","valid_until");--> statement-breakpoint
CREATE INDEX "promo_code_public_idx" ON "promotional_codes" USING btree ("is_public");--> statement-breakpoint
CREATE INDEX "conversion_referral_idx" ON "referral_conversions" USING btree ("referral_link_id");--> statement-breakpoint
CREATE INDEX "conversion_candidate_idx" ON "referral_conversions" USING btree ("referred_candidate_id");--> statement-breakpoint
CREATE INDEX "conversion_status_idx" ON "referral_conversions" USING btree ("status");--> statement-breakpoint
CREATE INDEX "conversion_date_idx" ON "referral_conversions" USING btree ("converted_at");--> statement-breakpoint
CREATE INDEX "referral_org_idx" ON "referral_links" USING btree ("organization_id");--> statement-breakpoint
CREATE INDEX "referral_candidate_idx" ON "referral_links" USING btree ("candidate_id");--> statement-breakpoint
CREATE INDEX "referral_code_idx" ON "referral_links" USING btree ("referral_code");--> statement-breakpoint
CREATE INDEX "referral_status_idx" ON "referral_links" USING btree ("status");