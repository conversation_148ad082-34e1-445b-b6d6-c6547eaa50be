import jsPDF from 'jspdf';
import { db } from '@/lib/db';
import { certificateLifecycle, testResults, candidates, organizations, testRegistrations } from '@/lib/db/schema';
import { eq, and, gte, lte, sql } from 'drizzle-orm';
import QRCode from 'qrcode';

export interface CertificateData {
  candidateName: string;
  passportNumber: string;
  testDate: string;
  testCenter: string;
  listeningBandScore: number;
  readingBandScore: number;
  writingBandScore: number;
  speakingBandScore: number;
  overallBandScore: number;
  organizationName: string;
  serialNumber: string;
  issueDate: string;
  expiryDate: string;
  candidatePhoto?: string;
}

export class CertificateGenerator {
  private static readonly CERTIFICATE_WIDTH = 297; // A4 landscape width in mm
  private static readonly CERTIFICATE_HEIGHT = 210; // A4 landscape height in mm
  
  static async generateCertificate(resultId: string): Promise<{
    certificateId: string;
    serialNumber: string;
    pdfBuffer: Buffer;
  }> {
    // Get test result with related data
    const result = await db
      .select({
        testResult: testResults,
        candidate: candidates,
        organization: organizations,
        testRegistration: testRegistrations,
      })
      .from(testResults)
      .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .innerJoin(organizations, eq(candidates.organizationId, organizations.id))
      .where(eq(testResults.id, resultId))
      .limit(1);

    if (result.length === 0) {
      throw new Error('Test result not found');
    }

    const { testResult, candidate, organization, testRegistration } = result[0];

    // Check if certificate already exists
    const existingCert = await db
      .select()
      .from(certificateLifecycle)
      .where(eq(certificateLifecycle.resultId, resultId))
      .limit(1);

    if (existingCert.length > 0 && existingCert[0].status === 'active') {
      throw new Error('Certificate already exists for this result');
    }

    // Generate serial number
    const serialNumber = await this.generateSerialNumber();
    
    // Calculate expiry date (6 months from test date)
    const testDate = new Date(testRegistration.testDate);
    const expiryDate = new Date(testDate);
    expiryDate.setMonth(expiryDate.getMonth() + 6);

    // Create certificate record
    const [certificate] = await db.insert(certificateLifecycle).values({
      resultId,
      serialNumber,
      expiresAt: expiryDate,
      metadata: {
        candidateName: candidate.fullName,
        testDate: testRegistration.testDate.toISOString().split('T')[0],
        overallBandScore: parseFloat(testResult.overallBandScore || '0'),
        organizationName: organization.name,
        certificateType: 'IELTS Academic',
      },
    }).returning();

    // Generate PDF
    const certificateData: CertificateData = {
      candidateName: candidate.fullName,
      passportNumber: candidate.passportNumber,
      testDate: testRegistration.testDate.toISOString().split('T')[0],
      testCenter: organization.name,
      listeningBandScore: parseFloat(testResult.listeningBandScore || '0'),
      readingBandScore: parseFloat(testResult.readingBandScore || '0'),
      writingBandScore: parseFloat(testResult.writingBandScore || '0'),
      speakingBandScore: parseFloat(testResult.speakingBandScore || '0'),
      overallBandScore: parseFloat(testResult.overallBandScore || '0'),
      organizationName: organization.name,
      serialNumber,
      issueDate: new Date().toISOString().split('T')[0],
      expiryDate: expiryDate.toISOString().split('T')[0],
      candidatePhoto: candidate.photoData,
    };

    const pdfBuffer = await this.createPDF(certificateData);

    return {
      certificateId: certificate.id,
      serialNumber,
      pdfBuffer,
    };
  }

  private static async generateSerialNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    // Get count of certificates this month
    const startOfMonth = new Date(year, new Date().getMonth(), 1);
    const endOfMonth = new Date(year, new Date().getMonth() + 1, 0);
    
    const count = await db
      .select({ count: sql<number>`count(*)` })
      .from(certificateLifecycle)
      .where(
        and(
          gte(certificateLifecycle.generatedAt, startOfMonth),
          lte(certificateLifecycle.generatedAt, endOfMonth)
        )
      );

    const sequence = String((count[0]?.count || 0) + 1).padStart(4, '0');
    
    return `IELTS-${year}${month}-${sequence}`;
  }

  private static async createPDF(data: CertificateData): Promise<Buffer> {
    const pdf = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4',
    });

    // Set up fonts and colors
    pdf.setFont('helvetica', 'normal');
    
    // Background and border
    pdf.setFillColor(248, 250, 252); // Light gray background
    pdf.rect(0, 0, this.CERTIFICATE_WIDTH, this.CERTIFICATE_HEIGHT, 'F');
    
    pdf.setDrawColor(59, 130, 246); // Blue border
    pdf.setLineWidth(2);
    pdf.rect(10, 10, this.CERTIFICATE_WIDTH - 20, this.CERTIFICATE_HEIGHT - 20);

    // Header
    pdf.setFontSize(24);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(59, 130, 246);
    pdf.text('IELTS CERTIFICATE', this.CERTIFICATE_WIDTH / 2, 35, { align: 'center' });

    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(75, 85, 99);
    pdf.text('International English Language Testing System', this.CERTIFICATE_WIDTH / 2, 45, { align: 'center' });

    // Certificate content
    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(0, 0, 0);
    
    const leftColumn = 30;
    const rightColumn = 180;
    let yPosition = 70;

    // Candidate information
    pdf.setFont('helvetica', 'bold');
    pdf.text('Candidate Name:', leftColumn, yPosition);
    pdf.setFont('helvetica', 'normal');
    pdf.text(data.candidateName, leftColumn + 50, yPosition);

    pdf.setFont('helvetica', 'bold');
    pdf.text('Passport/ID Number:', rightColumn, yPosition);
    pdf.setFont('helvetica', 'normal');
    pdf.text(data.passportNumber, rightColumn + 50, yPosition);

    yPosition += 15;

    pdf.setFont('helvetica', 'bold');
    pdf.text('Test Date:', leftColumn, yPosition);
    pdf.setFont('helvetica', 'normal');
    pdf.text(data.testDate, leftColumn + 30, yPosition);

    pdf.setFont('helvetica', 'bold');
    pdf.text('Test Center:', rightColumn, yPosition);
    pdf.setFont('helvetica', 'normal');
    pdf.text(data.testCenter, rightColumn + 30, yPosition);

    yPosition += 25;

    // Test scores section
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(16);
    pdf.text('Test Results', this.CERTIFICATE_WIDTH / 2, yPosition, { align: 'center' });

    yPosition += 15;

    // Scores table
    const scoreData = [
      ['Skill', 'Band Score'],
      ['Listening', data.listeningBandScore.toString()],
      ['Reading', data.readingBandScore.toString()],
      ['Writing', data.writingBandScore.toString()],
      ['Speaking', data.speakingBandScore.toString()],
      ['Overall Band Score', data.overallBandScore.toString()],
    ];

    const tableX = this.CERTIFICATE_WIDTH / 2 - 40;
    const tableY = yPosition;
    const cellWidth = 40;
    const cellHeight = 8;

    scoreData.forEach((row, rowIndex) => {
      row.forEach((cell, colIndex) => {
        const x = tableX + (colIndex * cellWidth);
        const y = tableY + (rowIndex * cellHeight);
        
        if (rowIndex === 0) {
          pdf.setFillColor(59, 130, 246);
          pdf.setTextColor(255, 255, 255);
          pdf.setFont('helvetica', 'bold');
        } else if (rowIndex === scoreData.length - 1) {
          pdf.setFillColor(34, 197, 94);
          pdf.setTextColor(255, 255, 255);
          pdf.setFont('helvetica', 'bold');
        } else {
          pdf.setFillColor(248, 250, 252);
          pdf.setTextColor(0, 0, 0);
          pdf.setFont('helvetica', 'normal');
        }
        
        pdf.rect(x, y, cellWidth, cellHeight, 'F');
        pdf.setDrawColor(0, 0, 0);
        pdf.rect(x, y, cellWidth, cellHeight);
        
        pdf.text(cell, x + cellWidth / 2, y + cellHeight / 2 + 1, { align: 'center' });
      });
    });

    yPosition += (scoreData.length * cellHeight) + 20;

    // Certificate details
    pdf.setTextColor(0, 0, 0);
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(10);

    pdf.text(`Certificate Serial Number: ${data.serialNumber}`, leftColumn, yPosition);
    pdf.text(`Issue Date: ${data.issueDate}`, rightColumn, yPosition);

    yPosition += 8;

    pdf.text(`Valid Until: ${data.expiryDate}`, leftColumn, yPosition);
    pdf.text(`Issued by: ${data.organizationName}`, rightColumn, yPosition);

    // Generate QR code for verification
    const verificationUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/verify/${data.serialNumber}`;
    const qrCodeDataUrl = await QRCode.toDataURL(verificationUrl, {
      width: 100,
      margin: 1,
    });

    // Add QR code
    pdf.addImage(qrCodeDataUrl, 'PNG', this.CERTIFICATE_WIDTH - 40, this.CERTIFICATE_HEIGHT - 40, 25, 25);
    
    pdf.setFontSize(8);
    pdf.text('Scan to verify', this.CERTIFICATE_WIDTH - 27, this.CERTIFICATE_HEIGHT - 10, { align: 'center' });

    // Footer
    pdf.setFontSize(8);
    pdf.setTextColor(107, 114, 128);
    pdf.text(
      'This certificate is valid for 6 months from the test date. For verification, visit our website or scan the QR code.',
      this.CERTIFICATE_WIDTH / 2,
      this.CERTIFICATE_HEIGHT - 5,
      { align: 'center' }
    );

    return Buffer.from(pdf.output('arraybuffer'));
  }
}
