import { Suspense } from 'react';
import { PublicSearchForm } from '@/components/public/public-search-form';
import { SearchResults } from '@/components/public/search-results';
import { Search, FileText, Award, TrendingUp } from 'lucide-react';

interface SearchPageProps {
  searchParams: {
    passport?: string;
    dob?: string;
  };
}

export default function SearchPage({ searchParams }: SearchPageProps) {
  const hasSearchParams = searchParams.passport && searchParams.dob;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-600 p-2 rounded-lg">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">IELTS Results Portal</h1>
                <p className="text-gray-600">Search and view your test results</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!hasSearchParams ? (
          // Search Form
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="text-center mb-8">
                <div className="bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <Search className="h-8 w-8 text-blue-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Find Your Results</h2>
                <p className="text-gray-600">
                  Enter your passport/birth certificate number and date of birth to view your IELTS test results
                </p>
              </div>

              <PublicSearchForm />

              {/* Features Preview */}
              <div className="mt-8 pt-8 border-t border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">What you can access:</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <FileText className="h-5 w-5 text-blue-600" />
                    <div>
                      <div className="font-medium text-gray-900">Test Results</div>
                      <div className="text-sm text-gray-600">Detailed score breakdown</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <TrendingUp className="h-5 w-5 text-green-600" />
                    <div>
                      <div className="font-medium text-gray-900">Progress Tracking</div>
                      <div className="text-sm text-gray-600">Historical performance</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <Award className="h-5 w-5 text-purple-600" />
                    <div>
                      <div className="font-medium text-gray-900">Premium Features</div>
                      <div className="text-sm text-gray-600">AI feedback & certificates</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Information Cards */}
            <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">How to Search</h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-start">
                    <span className="bg-blue-100 text-blue-600 rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium mr-2 mt-0.5">1</span>
                    Enter your passport or birth certificate number exactly as registered
                  </li>
                  <li className="flex items-start">
                    <span className="bg-blue-100 text-blue-600 rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium mr-2 mt-0.5">2</span>
                    Select your date of birth from the calendar
                  </li>
                  <li className="flex items-start">
                    <span className="bg-blue-100 text-blue-600 rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium mr-2 mt-0.5">3</span>
                    Click search to view all your test results
                  </li>
                </ul>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Need Help?</h3>
                <div className="space-y-3 text-sm text-gray-600">
                  <p>
                    <strong>Can&apos;t find your results?</strong><br />
                    Make sure you&apos;re using the exact passport/birth certificate number used during registration.
                  </p>
                  <p>
                    <strong>Multiple test centers?</strong><br />
                    Our system searches across all participating IELTS centers automatically.
                  </p>
                  <p>
                    <strong>Contact Support:</strong><br />
                    <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                      <EMAIL>
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Search Results
          <Suspense fallback={
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          }>
            <SearchResults
              passport={searchParams.passport}
              dateOfBirth={searchParams.dob}
            />
          </Suspense>
        )}
      </div>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>&copy; 2024 IELTS Results Portal. All rights reserved.</p>
            <div className="mt-2 space-x-4">
              <a href="/privacy" className="text-blue-600 hover:underline">Privacy Policy</a>
              <a href="/terms" className="text-blue-600 hover:underline">Terms of Service</a>
              <a href="/contact" className="text-blue-600 hover:underline">Contact Us</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
