import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { testRegistrations, candidates } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';

const createTestRegistrationSchema = z.object({
  candidateId: z.string().min(1),
  testDate: z.string().min(1),
  testCenter: z.string().min(1),
  candidateNumber: z.string().min(1),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (session.user.role !== 'admin' && !session.user.masterAdmin) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createTestRegistrationSchema.parse(body);

    // Verify candidate belongs to the organization
    const candidate = await db
      .select()
      .from(candidates)
      .where(
        and(
          eq(candidates.id, validatedData.candidateId),
          eq(candidates.organizationId, session.user.organizationId)
        )
      )
      .limit(1);

    if (candidate.length === 0) {
      return NextResponse.json(
        { error: 'Candidate not found or does not belong to your organization' },
        { status: 404 }
      );
    }

    // Check if candidate number is already used for this test date
    const existingRegistration = await db
      .select()
      .from(testRegistrations)
      .where(
        and(
          eq(testRegistrations.candidateNumber, validatedData.candidateNumber),
          eq(testRegistrations.testDate, new Date(validatedData.testDate))
        )
      )
      .limit(1);

    if (existingRegistration.length > 0) {
      return NextResponse.json(
        { error: 'Candidate number already exists for this test date' },
        { status: 409 }
      );
    }

    // Create test registration
    const [registration] = await db
      .insert(testRegistrations)
      .values({
        candidateId: validatedData.candidateId,
        candidateNumber: validatedData.candidateNumber,
        testDate: new Date(validatedData.testDate),
        testCenter: validatedData.testCenter,
        status: 'registered',
      })
      .returning();

    // Update candidate's total tests count
    await db
      .update(candidates)
      .set({
        totalTests: candidate[0].totalTests + 1,
        updatedAt: new Date(),
      })
      .where(eq(candidates.id, validatedData.candidateId));

    return NextResponse.json(registration, { status: 201 });
  } catch (error) {
    console.error('Error creating test registration:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to create test registration' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const candidateId = searchParams.get('candidateId');
    const status = searchParams.get('status');

    let query = db
      .select({
        registration: testRegistrations,
        candidate: candidates,
      })
      .from(testRegistrations)
      .leftJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(eq(candidates.organizationId, session.user.organizationId));

    // Apply filters
    if (candidateId) {
      query = query.where(
        and(
          eq(candidates.organizationId, session.user.organizationId),
          eq(testRegistrations.candidateId, candidateId)
        )
      );
    }

    if (status) {
      query = query.where(
        and(
          eq(candidates.organizationId, session.user.organizationId),
          eq(testRegistrations.status, status)
        )
      );
    }

    const registrations = await query;

    return NextResponse.json(registrations);
  } catch (error) {
    console.error('Error fetching test registrations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch test registrations' },
      { status: 500 }
    );
  }
}
