import { db } from '@/lib/db';
import {
  promotionalCodes,
  promotionalCodeUsage,
  promotionalRules,
  referralLinks,
  referralConversions,
  candidates,
  testResults,
} from '@/lib/db/schema';
import { eq, and, gte, lte, desc, count, sum, sql } from 'drizzle-orm';
import { createId } from '@paralleldrive/cuid2';
import {
  PromotionalCodeValidation,
  ApplyPromotionalCodeRequest,
  CreatePromotionalCodeRequest,
  DiscountCalculation,
  PromotionalAnalytics,
  PromotionalEligibility,
  ReferralStats,
  CreateReferralLinkRequest,
  PROMOTIONAL_ERROR_CODES,
  PROMO_CODE_CONFIG,
  REFERRAL_CONFIG,
  FeatureType,
  DiscountType,
} from './types';

/**
 * Validate and apply a promotional code
 */
export async function validatePromotionalCode(
  code: string,
  candidateId: string,
  featureType: FeatureType,
  originalAmount: number,
  organizationId: string,
  resultId?: string
): Promise<PromotionalCodeValidation> {
  try {
    // Find the promotional code
    const promoCode = await db
      .select()
      .from(promotionalCodes)
      .where(
        and(
          eq(promotionalCodes.code, code.toUpperCase()),
          eq(promotionalCodes.organizationId, organizationId)
        )
      )
      .limit(1);

    if (promoCode.length === 0) {
      return {
        isValid: false,
        error: 'Promotional code not found',
      };
    }

    const codeData = promoCode[0];

    // Check if code is active
    if (codeData.status !== 'active') {
      return {
        isValid: false,
        error: 'Promotional code is not active',
      };
    }

    // Check validity dates
    const now = new Date();
    if (now < codeData.validFrom || now > codeData.validUntil) {
      return {
        isValid: false,
        error: 'Promotional code has expired',
      };
    }

    // Check usage limit
    if (codeData.usageLimit && codeData.usageCount >= codeData.usageLimit) {
      return {
        isValid: false,
        error: 'Promotional code usage limit exceeded',
      };
    }

    // Check if candidate has already used this code
    const existingUsage = await db
      .select()
      .from(promotionalCodeUsage)
      .where(
        and(
          eq(promotionalCodeUsage.codeId, codeData.id),
          eq(promotionalCodeUsage.candidateId, candidateId),
          resultId ? eq(promotionalCodeUsage.resultId, resultId) : undefined
        )
      )
      .limit(1);

    if (existingUsage.length > 0) {
      return {
        isValid: false,
        error: 'You have already used this promotional code',
      };
    }

    // Check feature type compatibility
    if (codeData.featureType !== 'all' && codeData.featureType !== featureType) {
      return {
        isValid: false,
        error: 'Promotional code is not valid for this feature',
      };
    }

    // Check minimum purchase amount
    if (codeData.minPurchaseAmount && originalAmount < parseFloat(codeData.minPurchaseAmount)) {
      return {
        isValid: false,
        error: `Minimum purchase amount is ${codeData.minPurchaseAmount} UZS`,
      };
    }

    // Calculate discount
    const discount = calculateDiscount(
      originalAmount,
      codeData.type as DiscountType,
      parseFloat(codeData.value),
      codeData.maxDiscountAmount ? parseFloat(codeData.maxDiscountAmount) : undefined
    );

    if (!discount.isValid) {
      return {
        isValid: false,
        error: discount.error,
      };
    }

    return {
      isValid: true,
      code: codeData,
      discountAmount: discount.discountAmount,
      finalAmount: discount.finalAmount,
      savings: discount.savings,
    };
  } catch (error) {
    console.error('Promotional code validation error:', error);
    return {
      isValid: false,
      error: 'Failed to validate promotional code',
    };
  }
}

/**
 * Apply a promotional code and record usage
 */
export async function applyPromotionalCode(
  request: ApplyPromotionalCodeRequest
): Promise<{ success: boolean; usage?: any; error?: string }> {
  try {
    // Validate the code first
    const validation = await validatePromotionalCode(
      request.code,
      request.candidateId,
      request.featureType,
      request.originalAmount,
      request.organizationId,
      request.resultId
    );

    if (!validation.isValid || !validation.code) {
      return {
        success: false,
        error: validation.error,
      };
    }

    // Record the usage
    const [usage] = await db
      .insert(promotionalCodeUsage)
      .values({
        codeId: validation.code.id,
        candidateId: request.candidateId,
        resultId: request.resultId,
        featureType: request.featureType,
        originalAmount: request.originalAmount.toString(),
        discountAmount: validation.discountAmount!.toString(),
        finalAmount: validation.finalAmount!.toString(),
      })
      .returning();

    // Update usage count
    await db
      .update(promotionalCodes)
      .set({
        usageCount: sql`${promotionalCodes.usageCount} + 1`,
        updatedAt: new Date(),
      })
      .where(eq(promotionalCodes.id, validation.code.id));

    return {
      success: true,
      usage,
    };
  } catch (error) {
    console.error('Apply promotional code error:', error);
    return {
      success: false,
      error: 'Failed to apply promotional code',
    };
  }
}

/**
 * Calculate discount amount based on type and value
 */
export function calculateDiscount(
  originalAmount: number,
  discountType: DiscountType,
  discountValue: number,
  maxDiscountAmount?: number
): DiscountCalculation {
  let discountAmount = 0;

  switch (discountType) {
    case 'percentage':
      if (discountValue < 0 || discountValue > 100) {
        return {
          originalAmount,
          discountAmount: 0,
          finalAmount: originalAmount,
          discountType,
          discountValue,
          savings: 0,
          isValid: false,
          error: 'Invalid discount percentage',
        };
      }
      discountAmount = (originalAmount * discountValue) / 100;
      if (maxDiscountAmount && discountAmount > maxDiscountAmount) {
        discountAmount = maxDiscountAmount;
      }
      break;

    case 'fixed_amount':
      if (discountValue < 0) {
        return {
          originalAmount,
          discountAmount: 0,
          finalAmount: originalAmount,
          discountType,
          discountValue,
          savings: 0,
          isValid: false,
          error: 'Invalid discount amount',
        };
      }
      discountAmount = Math.min(discountValue, originalAmount);
      break;

    case 'free_access':
      discountAmount = originalAmount;
      break;

    default:
      return {
        originalAmount,
        discountAmount: 0,
        finalAmount: originalAmount,
        discountType,
        discountValue,
        savings: 0,
        isValid: false,
        error: 'Invalid discount type',
      };
  }

  const finalAmount = Math.max(0, originalAmount - discountAmount);

  return {
    originalAmount,
    discountAmount,
    finalAmount,
    discountType,
    discountValue,
    savings: discountAmount,
    isValid: true,
  };
}

/**
 * Create a new promotional code
 */
export async function createPromotionalCode(
  request: CreatePromotionalCodeRequest
): Promise<{ success: boolean; code?: any; error?: string }> {
  try {
    // Check if code already exists in organization
    const existingCode = await db
      .select()
      .from(promotionalCodes)
      .where(
        and(
          eq(promotionalCodes.code, request.code.toUpperCase()),
          eq(promotionalCodes.organizationId, request.organizationId)
        )
      )
      .limit(1);

    if (existingCode.length > 0) {
      return {
        success: false,
        error: 'Promotional code already exists',
      };
    }

    // Create the promotional code
    const [newCode] = await db
      .insert(promotionalCodes)
      .values({
        organizationId: request.organizationId,
        code: request.code.toUpperCase(),
        name: request.name,
        description: request.description,
        type: request.type,
        value: request.value.toString(),
        featureType: request.featureType,
        validFrom: request.validFrom,
        validUntil: request.validUntil,
        usageLimit: request.usageLimit,
        minPurchaseAmount: request.minPurchaseAmount?.toString(),
        maxDiscountAmount: request.maxDiscountAmount?.toString(),
        isPublic: request.isPublic || false,
        createdBy: request.createdBy,
      })
      .returning();

    return {
      success: true,
      code: newCode,
    };
  } catch (error) {
    console.error('Create promotional code error:', error);
    return {
      success: false,
      error: 'Failed to create promotional code',
    };
  }
}

/**
 * Generate a random promotional code
 */
export function generatePromotionalCode(
  type: 'student' | 'loyalty' | 'time_based' | 'custom' = 'custom',
  length: number = PROMO_CODE_CONFIG.defaultLength
): string {
  const prefix = PROMO_CODE_CONFIG.prefixes[type];
  const randomLength = length - prefix.length;

  let randomPart = '';
  for (let i = 0; i < randomLength; i++) {
    randomPart += PROMO_CODE_CONFIG.allowedCharacters.charAt(
      Math.floor(Math.random() * PROMO_CODE_CONFIG.allowedCharacters.length)
    );
  }

  return prefix + randomPart;
}

/**
 * Create a referral link for a candidate
 */
export async function createReferralLink(
  request: CreateReferralLinkRequest
): Promise<{ success: boolean; referralLink?: any; error?: string }> {
  try {
    // Check if candidate already has a referral link
    const existingLink = await db
      .select()
      .from(referralLinks)
      .where(
        and(
          eq(referralLinks.candidateId, request.candidateId),
          eq(referralLinks.organizationId, request.organizationId)
        )
      )
      .limit(1);

    if (existingLink.length > 0) {
      return {
        success: true,
        referralLink: existingLink[0],
      };
    }

    // Generate unique referral code
    let referralCode: string;
    let isUnique = false;
    let attempts = 0;

    do {
      referralCode = generateReferralCode();
      const existing = await db
        .select()
        .from(referralLinks)
        .where(eq(referralLinks.referralCode, referralCode))
        .limit(1);

      isUnique = existing.length === 0;
      attempts++;
    } while (!isUnique && attempts < 10);

    if (!isUnique) {
      return {
        success: false,
        error: 'Failed to generate unique referral code',
      };
    }

    // Create referral link
    const [newReferralLink] = await db
      .insert(referralLinks)
      .values({
        organizationId: request.organizationId,
        candidateId: request.candidateId,
        referralCode: referralCode!,
      })
      .returning();

    return {
      success: true,
      referralLink: newReferralLink,
    };
  } catch (error) {
    console.error('Create referral link error:', error);
    return {
      success: false,
      error: 'Failed to create referral link',
    };
  }
}

/**
 * Generate a unique referral code
 */
function generateReferralCode(): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = 'REF';

  for (let i = 0; i < 6; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }

  return result;
}

/**
 * Get referral statistics for a candidate
 */
export async function getReferralStats(candidateId: string): Promise<ReferralStats> {
  try {
    const referralLink = await db
      .select()
      .from(referralLinks)
      .where(eq(referralLinks.candidateId, candidateId))
      .limit(1);

    if (referralLink.length === 0) {
      return {
        totalClicks: 0,
        totalConversions: 0,
        conversionRate: 0,
        totalEarnings: 0,
        pendingEarnings: 0,
        paidEarnings: 0,
      };
    }

    const link = referralLink[0];

    // Get conversion statistics
    const conversions = await db
      .select({
        total: count(),
        totalEarnings: sum(referralConversions.commissionAmount),
        pendingEarnings: sql<number>`SUM(CASE WHEN ${referralConversions.status} = 'pending' THEN ${referralConversions.commissionAmount} ELSE 0 END)`,
        paidEarnings: sql<number>`SUM(CASE WHEN ${referralConversions.status} = 'paid' THEN ${referralConversions.commissionAmount} ELSE 0 END)`,
      })
      .from(referralConversions)
      .where(eq(referralConversions.referralLinkId, link.id));

    const stats = conversions[0];
    const conversionRate = link.clickCount > 0 ? (link.conversionCount / link.clickCount) * 100 : 0;

    return {
      totalClicks: link.clickCount,
      totalConversions: link.conversionCount,
      conversionRate: Math.round(conversionRate * 100) / 100,
      totalEarnings: parseFloat(stats.totalEarnings || '0'),
      pendingEarnings: parseFloat(stats.pendingEarnings?.toString() || '0'),
      paidEarnings: parseFloat(stats.paidEarnings?.toString() || '0'),
    };
  } catch (error) {
    console.error('Get referral stats error:', error);
    return {
      totalClicks: 0,
      totalConversions: 0,
      conversionRate: 0,
      totalEarnings: 0,
      pendingEarnings: 0,
      paidEarnings: 0,
    };
  }
}
