'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { calculateBandScore, calculateOverallBandScore } from '@/lib/utils/ielts-scoring';
import { FileText, Loader2, Calculator, Search } from 'lucide-react';

const testResultSchema = z.object({
  registrationId: z.string().min(1, 'Please select a test registration'),
  listeningScore: z.number().min(0).max(40),
  readingScore: z.number().min(0).max(40),
  writingTask1Score: z.number().min(0).max(9).step(0.5),
  writingTask2Score: z.number().min(0).max(9).step(0.5),
  speakingFluencyScore: z.number().min(0).max(9).step(0.5),
  speakingLexicalScore: z.number().min(0).max(9).step(0.5),
  speakingGrammarScore: z.number().min(0).max(9).step(0.5),
  speakingPronunciationScore: z.number().min(0).max(9).step(0.5),
  status: z.enum(['draft', 'completed']),
});

type TestResultForm = z.infer<typeof testResultSchema>;

interface TestResultEntryFormProps {
  organizationId: string;
  editResultId?: string;
  preselectedRegistrationId?: string;
}

export function TestResultEntryForm({
  organizationId,
  editResultId,
  preselectedRegistrationId
}: TestResultEntryFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [registrations, setRegistrations] = useState<any[]>([]);
  const [selectedRegistration, setSelectedRegistration] = useState<any>(null);
  const [calculatedScores, setCalculatedScores] = useState({
    listeningBand: 0,
    readingBand: 0,
    writingBand: 0,
    speakingBand: 0,
    overallBand: 0,
  });

  const {
    register,
    handleSubmit,
    watch,
    reset,
    setValue,
    formState: { errors },
  } = useForm<TestResultForm>({
    resolver: zodResolver(testResultSchema),
    defaultValues: {
      status: 'draft',
    },
  });

  const watchedValues = watch();

  // Load available registrations
  useEffect(() => {
    const loadRegistrations = async () => {
      try {
        const response = await fetch(`/api/test-registrations?status=completed&organizationId=${organizationId}`);
        if (response.ok) {
          const data = await response.json();
          setRegistrations(data);

          // If preselected registration, set it
          if (preselectedRegistrationId) {
            const selected = data.find((r: any) => r.registration.id === preselectedRegistrationId);
            if (selected) {
              setSelectedRegistration(selected);
              setValue('registrationId', preselectedRegistrationId);
            }
          }
        }
      } catch (error) {
        console.error('Error loading registrations:', error);
      }
    };

    loadRegistrations();
  }, [organizationId, preselectedRegistrationId, setValue]);

  // Load existing result if editing
  useEffect(() => {
    if (editResultId) {
      const loadResult = async () => {
        try {
          const response = await fetch(`/api/test-results/${editResultId}`);
          if (response.ok) {
            const result = await response.json();
            // Populate form with existing data
            Object.keys(result).forEach((key) => {
              if (key in watchedValues) {
                setValue(key as keyof TestResultForm, result[key]);
              }
            });
          }
        } catch (error) {
          console.error('Error loading result:', error);
        }
      };

      loadResult();
    }
  }, [editResultId, setValue, watchedValues]);

  // Calculate band scores in real-time
  useEffect(() => {
    const listeningBand = calculateBandScore('listening', watchedValues.listeningScore || 0);
    const readingBand = calculateBandScore('reading', watchedValues.readingScore || 0);

    // Writing band score is average of task 1 and task 2
    const writingBand = ((watchedValues.writingTask1Score || 0) + (watchedValues.writingTask2Score || 0)) / 2;

    // Speaking band score is average of all speaking components
    const speakingBand = (
      (watchedValues.speakingFluencyScore || 0) +
      (watchedValues.speakingLexicalScore || 0) +
      (watchedValues.speakingGrammarScore || 0) +
      (watchedValues.speakingPronunciationScore || 0)
    ) / 4;

    const overallBand = calculateOverallBandScore(listeningBand, readingBand, writingBand, speakingBand);

    setCalculatedScores({
      listeningBand,
      readingBand,
      writingBand,
      speakingBand,
      overallBand,
    });
  }, [watchedValues]);

  const onSubmit = async (data: TestResultForm) => {
    setIsLoading(true);
    try {
      const resultData = {
        registrationId: data.registrationId,
        listeningScore: data.listeningScore,
        readingScore: data.readingScore,
        writingTask1Score: data.writingTask1Score.toString(),
        writingTask2Score: data.writingTask2Score.toString(),
        speakingFluencyScore: data.speakingFluencyScore.toString(),
        speakingLexicalScore: data.speakingLexicalScore.toString(),
        speakingGrammarScore: data.speakingGrammarScore.toString(),
        speakingPronunciationScore: data.speakingPronunciationScore.toString(),
        listeningBandScore: calculatedScores.listeningBand.toString(),
        readingBandScore: calculatedScores.readingBand.toString(),
        writingBandScore: calculatedScores.writingBand.toString(),
        speakingBandScore: calculatedScores.speakingBand.toString(),
        overallBand: calculatedScores.overallBand.toString(),
        status: data.status,
      };

      const url = editResultId
        ? `/api/test-results/${editResultId}`
        : '/api/test-results';

      const method = editResultId ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(resultData),
      });

      if (!response.ok) {
        throw new Error('Failed to save test result');
      }

      reset();
      setSelectedRegistration(null);
      alert('Test result saved successfully!');
      window.location.reload(); // Refresh to show updated results
    } catch (error) {
      console.error('Error saving test result:', error);
      alert('Failed to save test result. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Registration Selection */}
      {!preselectedRegistrationId && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Select Test Registration
            </CardTitle>
            <CardDescription>
              Choose a completed test registration to enter results for
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Select
              value={watchedValues.registrationId || ''}
              onValueChange={(value) => {
                setValue('registrationId', value);
                const selected = registrations.find(r => r.registration.id === value);
                setSelectedRegistration(selected);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a test registration..." />
              </SelectTrigger>
              <SelectContent>
                {registrations.map((reg) => (
                  <SelectItem key={reg.registration.id} value={reg.registration.id}>
                    <div className="flex items-center justify-between w-full">
                      <span>{reg.candidate?.fullName}</span>
                      <span className="text-sm text-muted-foreground ml-2">
                        {reg.registration.testDate} - {reg.registration.candidateNumber}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.registrationId && (
              <p className="text-sm text-red-600 mt-1">{errors.registrationId.message}</p>
            )}
          </CardContent>
        </Card>
      )}

      {/* Selected Registration Info */}
      {selectedRegistration && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">{selectedRegistration.candidate?.fullName}</h3>
                <p className="text-sm text-muted-foreground">
                  {selectedRegistration.candidate?.passportNumber} •
                  Test Date: {new Date(selectedRegistration.registration.testDate).toLocaleDateString()} •
                  Candidate #: {selectedRegistration.registration.candidateNumber}
                </p>
              </div>
              <Badge variant="outline">
                {selectedRegistration.registration.testType || 'Academic'}
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Result Entry Form */}
      {(selectedRegistration || editResultId) && (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Listening Section */}
          <Card>
            <CardHeader>
              <CardTitle>Listening</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Raw Score (0-40)
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="40"
                    {...register('listeningScore', { valueAsNumber: true })}
                  />
                  {errors.listeningScore && (
                    <p className="text-sm text-red-600 mt-1">{errors.listeningScore.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Band Score (Calculated)
                  </label>
                  <div className="px-3 py-2 bg-muted border rounded-md text-lg font-bold text-blue-600">
                    {calculatedScores.listeningBand.toFixed(1)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Reading Section */}
          <Card>
            <CardHeader>
              <CardTitle>Reading</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Raw Score (0-40)
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="40"
                    {...register('readingScore', { valueAsNumber: true })}
                  />
                  {errors.readingScore && (
                    <p className="text-sm text-red-600 mt-1">{errors.readingScore.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Band Score (Calculated)
                  </label>
                  <div className="px-3 py-2 bg-muted border rounded-md text-lg font-bold text-blue-600">
                    {calculatedScores.readingBand.toFixed(1)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Writing Section */}
          <Card>
            <CardHeader>
              <CardTitle>Writing</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Task 1 Score (0-9)
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="9"
                    step="0.5"
                    {...register('writingTask1Score', { valueAsNumber: true })}
                  />
                  {errors.writingTask1Score && (
                    <p className="text-sm text-red-600 mt-1">{errors.writingTask1Score.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Task 2 Score (0-9)
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="9"
                    step="0.5"
                    {...register('writingTask2Score', { valueAsNumber: true })}
                  />
                  {errors.writingTask2Score && (
                    <p className="text-sm text-red-600 mt-1">{errors.writingTask2Score.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Band Score (Calculated)
                  </label>
                  <div className="px-3 py-2 bg-muted border rounded-md text-lg font-bold text-blue-600">
                    {calculatedScores.writingBand.toFixed(1)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Speaking Section */}
          <Card>
            <CardHeader>
              <CardTitle>Speaking</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Fluency & Coherence (0-9)
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="9"
                    step="0.5"
                    {...register('speakingFluencyScore', { valueAsNumber: true })}
                  />
                  {errors.speakingFluencyScore && (
                    <p className="text-sm text-red-600 mt-1">{errors.speakingFluencyScore.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Lexical Resource (0-9)
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="9"
                    step="0.5"
                    {...register('speakingLexicalScore', { valueAsNumber: true })}
                  />
                  {errors.speakingLexicalScore && (
                    <p className="text-sm text-red-600 mt-1">{errors.speakingLexicalScore.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Grammar & Accuracy (0-9)
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="9"
                    step="0.5"
                    {...register('speakingGrammarScore', { valueAsNumber: true })}
                  />
                  {errors.speakingGrammarScore && (
                    <p className="text-sm text-red-600 mt-1">{errors.speakingGrammarScore.message}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Pronunciation (0-9)
                  </label>
                  <Input
                    type="number"
                    min="0"
                    max="9"
                    step="0.5"
                    {...register('speakingPronunciationScore', { valueAsNumber: true })}
                  />
                  {errors.speakingPronunciationScore && (
                    <p className="text-sm text-red-600 mt-1">{errors.speakingPronunciationScore.message}</p>
                  )}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  Band Score (Calculated)
                </label>
                <div className="px-3 py-2 bg-muted border rounded-md text-lg font-bold text-blue-600">
                  {calculatedScores.speakingBand.toFixed(1)}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Overall Score */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calculator className="h-5 w-5" />
                Overall Band Score
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-600 mb-2">
                  {calculatedScores.overallBand.toFixed(1)}
                </div>
                <p className="text-sm text-muted-foreground">
                  Calculated from all four skills
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Status and Submit */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Result Status
                  </label>
                  <Select
                    value={watchedValues.status}
                    onValueChange={(value) => setValue('status', value as 'draft' | 'completed')}
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    editResultId ? 'Update Result' : 'Save Result'
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </form>
      )}
    </div>
  );
}
