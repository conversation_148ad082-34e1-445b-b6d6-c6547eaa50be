import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { z } from 'zod';
import { createPaymentTransaction, getFeaturePricing } from '@/lib/payments/utils';
import { PaymentGateway, FeatureType } from '@/lib/payments/types';

const initiatePaymentSchema = z.object({
  candidateId: z.string().min(1),
  featureType: z.enum(['feedback', 'certificate', 'progress']),
  gateway: z.enum(['click', 'payme', 'manual']),
  resultId: z.string().optional(),
  returnUrl: z.string().url(),
  promotionalCode: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = initiatePaymentSchema.parse(body);

    // Get feature pricing
    const pricing = getFeaturePricing(validatedData.featureType as FeatureType);
    let finalAmount = pricing.amount;
    let discountAmount = 0;
    let promotionalCodeUsage = null;

    // Apply promotional code if provided
    if (validatedData.promotionalCode) {
      const { validatePromotionalCode, applyPromotionalCode } = await import('@/lib/promotions/utils');

      const validation = await validatePromotionalCode(
        validatedData.promotionalCode,
        validatedData.candidateId,
        validatedData.featureType as FeatureType,
        pricing.amount,
        session.user.organizationId,
        validatedData.resultId
      );

      if (validation.isValid && validation.finalAmount !== undefined) {
        finalAmount = validation.finalAmount;
        discountAmount = validation.discountAmount || 0;

        // Apply the promotional code
        const applyResult = await applyPromotionalCode({
          code: validatedData.promotionalCode,
          candidateId: validatedData.candidateId,
          featureType: validatedData.featureType as FeatureType,
          originalAmount: pricing.amount,
          resultId: validatedData.resultId,
          organizationId: session.user.organizationId,
        });

        if (applyResult.success) {
          promotionalCodeUsage = applyResult.usage;
        }
      }
    }

    // Create payment transaction
    const paymentResponse = await createPaymentTransaction({
      candidateId: validatedData.candidateId,
      organizationId: session.user.organizationId,
      amount: finalAmount,
      currency: pricing.currency,
      gateway: validatedData.gateway as PaymentGateway,
      featureType: validatedData.featureType as FeatureType,
      resultId: validatedData.resultId,
      returnUrl: validatedData.returnUrl,
      description: pricing.description,
      metadata: {
        originalAmount: pricing.amount,
        discountAmount,
        promotionalCodeUsage: promotionalCodeUsage?.id,
      },
    });

    if (paymentResponse.status === 'failed') {
      return NextResponse.json(
        { error: paymentResponse.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      transactionId: paymentResponse.transactionId,
      paymentUrl: paymentResponse.paymentUrl,
      status: paymentResponse.status,
      pricing: {
        originalAmount: pricing.amount,
        discountAmount,
        finalAmount,
        currency: pricing.currency,
        description: pricing.description,
      },
      promotionalCode: validatedData.promotionalCode ? {
        code: validatedData.promotionalCode,
        applied: !!promotionalCodeUsage,
        usageId: promotionalCodeUsage?.id,
      } : null,
    });
  } catch (error) {
    console.error('Payment initiation error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Payment initiation failed' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const featureType = searchParams.get('featureType') as FeatureType;

    if (!featureType || !['feedback', 'certificate', 'progress'].includes(featureType)) {
      return NextResponse.json(
        { error: 'Invalid feature type' },
        { status: 400 }
      );
    }

    // Get pricing information
    const pricing = getFeaturePricing(featureType);

    return NextResponse.json({
      featureType,
      amount: pricing.amount,
      currency: pricing.currency,
      description: pricing.description,
      availableGateways: [
        {
          id: 'click',
          name: 'Click',
          description: 'Pay with Click - Fast and secure',
          processingTime: 'Instant',
        },
        {
          id: 'payme',
          name: 'Payme',
          description: 'Pay with Payme - Convenient mobile payments',
          processingTime: 'Instant',
        },
        {
          id: 'manual',
          name: 'Manual Payment',
          description: 'Bank transfer or cash payment with admin approval',
          processingTime: '1-3 business days',
        },
      ],
    });
  } catch (error) {
    console.error('Payment info error:', error);
    return NextResponse.json(
      { error: 'Failed to get payment information' },
      { status: 500 }
    );
  }
}
