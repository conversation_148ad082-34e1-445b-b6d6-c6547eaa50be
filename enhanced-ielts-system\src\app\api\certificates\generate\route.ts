import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { CertificateGenerator } from '@/lib/certificates/generator';

export async function POST(request: NextRequest) {
  const session = await auth();
  
  if (!session?.user?.organizationId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { resultId } = await request.json();

  try {
    const certificate = await CertificateGenerator.generateCertificate(resultId);
    
    return NextResponse.json({
      certificateId: certificate.certificateId,
      serialNumber: certificate.serialNumber,
      downloadUrl: `/api/certificates/download/${certificate.certificateId}`,
    }, { status: 201 });
  } catch (error) {
    console.error('Error generating certificate:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to generate certificate' },
      { status: 500 }
    );
  }
}
