import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { paymentTransactions, candidates } from '@/lib/db/schema';
import { eq, and, desc } from 'drizzle-orm';
import { ManualPaymentsList } from '@/components/admin/manual-payments-list';
import { PaymentStatistics } from '@/components/admin/payment-statistics';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { CreditCard, Search, Filter, Download } from 'lucide-react';

export default async function PaymentsPage() {
  const session = await auth();
  
  if (!session?.user?.organizationId) {
    return <div>Access denied</div>;
  }

  if (session.user.role !== 'admin' && !session.user.masterAdmin) {
    return <div>Insufficient permissions</div>;
  }

  // Get all payment transactions for the organization
  const allTransactions = await db
    .select({
      transaction: paymentTransactions,
      candidate: candidates,
    })
    .from(paymentTransactions)
    .leftJoin(candidates, eq(paymentTransactions.candidateId, candidates.id))
    .where(eq(candidates.organizationId, session.user.organizationId))
    .orderBy(desc(paymentTransactions.createdAt));

  // Get manual payments that need approval
  const pendingManualPayments = allTransactions.filter(
    t => t.transaction.gateway === 'manual' && t.transaction.status === 'pending'
  );

  // Calculate statistics
  const totalTransactions = allTransactions.length;
  const completedPayments = allTransactions.filter(t => t.transaction.status === 'completed').length;
  const pendingPayments = allTransactions.filter(t => t.transaction.status === 'pending').length;
  const totalRevenue = allTransactions
    .filter(t => t.transaction.status === 'completed')
    .reduce((sum, t) => sum + parseFloat(t.transaction.amount), 0);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Management</h1>
          <p className="text-gray-600">Manage payments and approve manual transactions</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Payment Statistics */}
      <PaymentStatistics
        totalTransactions={totalTransactions}
        completedPayments={completedPayments}
        pendingPayments={pendingPayments}
        totalRevenue={totalRevenue}
        pendingManualPayments={pendingManualPayments.length}
      />

      {/* Pending Manual Payments Alert */}
      {pendingManualPayments.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <CreditCard className="h-5 w-5 text-yellow-600 mr-3" />
            <div>
              <h3 className="font-medium text-yellow-900">
                {pendingManualPayments.length} Manual Payment{pendingManualPayments.length > 1 ? 's' : ''} Awaiting Approval
              </h3>
              <p className="text-sm text-yellow-700">
                Review and approve manual payments to grant access to premium features.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search by candidate name, transaction ID, or reference..."
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
              <option value="">All Gateways</option>
              <option value="click">Click</option>
              <option value="payme">Payme</option>
              <option value="manual">Manual</option>
            </select>
            <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="completed">Completed</option>
              <option value="failed">Failed</option>
              <option value="cancelled">Cancelled</option>
            </select>
            <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
              <option value="">All Features</option>
              <option value="feedback">AI Feedback</option>
              <option value="certificate">Certificate</option>
              <option value="progress">Progress Analytics</option>
            </select>
          </div>
        </div>
      </div>

      {/* Manual Payments List */}
      <ManualPaymentsList transactions={allTransactions} />

      {allTransactions.length === 0 && (
        <div className="text-center py-12">
          <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No payment transactions</h3>
          <p className="mt-1 text-sm text-gray-500">
            Payment transactions will appear here once candidates start purchasing premium features.
          </p>
        </div>
      )}
    </div>
  );
}
