'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Modal } from '@/components/ui/modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Calendar, Loader2 } from 'lucide-react';

const testRegistrationSchema = z.object({
  candidateId: z.string().min(1, 'Please select a candidate'),
  testDate: z.string().min(1, 'Test date is required'),
  testCenter: z.string().min(1, 'Test center is required'),
  candidateNumber: z.string().min(1, 'Candidate number is required'),
});

type TestRegistrationForm = z.infer<typeof testRegistrationSchema>;

interface TestRegistrationModalProps {
  children: React.ReactNode;
  candidates?: Array<{
    id: string;
    fullName: string;
    passportNumber: string;
  }>;
}

export function TestRegistrationModal({ children, candidates = [] }: TestRegistrationModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<TestRegistrationForm>({
    resolver: zodResolver(testRegistrationSchema),
  });

  const onSubmit = async (data: TestRegistrationForm) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/test-registrations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to register test');
      }

      reset();
      setIsOpen(false);
      window.location.reload(); // Refresh the page to show new registration
    } catch (error) {
      console.error('Error registering test:', error);
      alert('Failed to register test. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div onClick={() => setIsOpen(true)}>
        {children}
      </div>

      <Modal isOpen={isOpen} onClose={() => setIsOpen(false)}>
        <div className="p-6">
          <div className="flex items-center mb-4">
            <Calendar className="h-6 w-6 text-blue-600 mr-2" />
            <h2 className="text-xl font-semibold text-gray-900">Register New Test</h2>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Candidate
              </label>
              <select
                {...register('candidateId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a candidate</option>
                {candidates.map((candidate) => (
                  <option key={candidate.id} value={candidate.id}>
                    {candidate.fullName} ({candidate.passportNumber})
                  </option>
                ))}
              </select>
              {errors.candidateId && (
                <p className="text-red-500 text-sm mt-1">{errors.candidateId.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Test Date
              </label>
              <Input
                type="datetime-local"
                {...register('testDate')}
                error={errors.testDate?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Test Center
              </label>
              <Input
                placeholder="Enter test center location"
                {...register('testCenter')}
                error={errors.testCenter?.message}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Candidate Number
              </label>
              <Input
                placeholder="Enter candidate test number"
                {...register('candidateNumber')}
                error={errors.candidateNumber?.message}
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Registering...
                  </>
                ) : (
                  'Register Test'
                )}
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
}
