'use client';

import { useState } from 'react';
import { Modal } from '@/components/ui/modal';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { PaymentGatewaySelector } from './payment-gateway-selector';
import {
  Lock,
  Star,
  Clock,
  Shield,
  CreditCard,
  CheckCircle,
  AlertCircle,
  X
} from 'lucide-react';
import { FeatureType, PAYMENT_PRICING, FEATURE_DESCRIPTIONS } from '@/lib/payments/types';
import { formatAmount } from '@/lib/utils/format';

interface PaywallOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  featureType: FeatureType;
  candidateId: string;
  resultId?: string;
  onPaymentSuccess?: () => void;
  children?: React.ReactNode;
}

export function PaywallOverlay({
  isOpen,
  onClose,
  featureType,
  candidateId,
  resultId,
  onPaymentSuccess,
  children,
}: PaywallOverlayProps) {
  const [showPaymentSelector, setShowPaymentSelector] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const pricing = PAYMENT_PRICING[featureType];
  const description = FEATURE_DESCRIPTIONS[featureType];

  const getFeatureIcon = () => {
    switch (featureType) {
      case 'feedback':
        return <Star className="h-8 w-8 text-yellow-500" />;
      case 'certificate':
        return <Shield className="h-8 w-8 text-blue-500" />;
      case 'progress':
        return <CheckCircle className="h-8 w-8 text-green-500" />;
      default:
        return <Lock className="h-8 w-8 text-gray-500" />;
    }
  };

  const getFeatureTitle = () => {
    switch (featureType) {
      case 'feedback':
        return 'AI-Powered Feedback';
      case 'certificate':
        return 'Official Certificate';
      case 'progress':
        return 'Progress Analytics';
      default:
        return 'Premium Feature';
    }
  };

  const getFeatureBenefits = () => {
    switch (featureType) {
      case 'feedback':
        return [
          'Detailed performance analysis',
          'Personalized study recommendations',
          'Weakness identification',
          'Improvement strategies',
          'AI-powered insights',
        ];
      case 'certificate':
        return [
          'Official IELTS certificate',
          'Verification QR code',
          'Digital download',
          'Shareable format',
          'Lifetime validity',
        ];
      case 'progress':
        return [
          'Detailed progress tracking',
          'Performance comparisons',
          'Historical analytics',
          'Trend analysis',
          'Goal setting tools',
        ];
      default:
        return [];
    }
  };

  const handlePaymentInitiate = () => {
    setShowPaymentSelector(true);
  };

  const handlePaymentComplete = () => {
    setIsProcessing(false);
    setShowPaymentSelector(false);
    onClose();
    onPaymentSuccess?.();
  };

  const handlePaymentError = (error: string) => {
    setIsProcessing(false);
    console.error('Payment error:', error);
    // Show error message to user
  };

  return (
    <>
      {/* Blurred Content Overlay */}
      {children && (
        <div className="relative">
          <div className="filter blur-sm pointer-events-none">
            {children}
          </div>
          <div className="absolute inset-0 bg-white/80 flex items-center justify-center">
            <div className="text-center p-6">
              <Lock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Premium Feature Locked
              </h3>
              <p className="text-gray-600 mb-4">
                Unlock {getFeatureTitle()} to access this content
              </p>
              <Button onClick={() => setShowPaymentSelector(true)}>
                <CreditCard className="h-4 w-4 mr-2" />
                Unlock Now - {formatAmount(pricing.amount, pricing.currency)}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Payment Modal */}
      <Modal isOpen={isOpen || showPaymentSelector} onClose={onClose} size="large">
        <div className="p-6">
          {!showPaymentSelector ? (
            // Feature Information
            <div className="text-center">
              <div className="flex justify-between items-start mb-6">
                <div className="flex-1">
                  <div className="flex items-center justify-center mb-4">
                    {getFeatureIcon()}
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    {getFeatureTitle()}
                  </h2>
                  <p className="text-gray-600 mb-6">
                    {description}
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>

              {/* Pricing */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 mb-6">
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  {formatAmount(pricing.amount, pricing.currency)}
                </div>
                <Badge variant="blue" className="mb-4">
                  One-time payment
                </Badge>
                <p className="text-sm text-gray-600">
                  {pricing.description}
                </p>
              </div>

              {/* Benefits */}
              <div className="text-left mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  What you'll get:
                </h3>
                <div className="space-y-3">
                  {getFeatureBenefits().map((benefit, index) => (
                    <div key={index} className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Security Notice */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <div className="flex items-center text-sm text-gray-600">
                  <Shield className="h-4 w-4 mr-2" />
                  <span>Secure payment processing with 256-bit SSL encryption</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="flex-1"
                >
                  Maybe Later
                </Button>
                <Button
                  onClick={handlePaymentInitiate}
                  className="flex-1"
                  disabled={isProcessing}
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  Continue to Payment
                </Button>
              </div>
            </div>
          ) : (
            // Payment Gateway Selection
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  Choose Payment Method
                </h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPaymentSelector(false)}
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>

              <div className="mb-6">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium text-gray-900">
                      {getFeatureTitle()}
                    </div>
                    <div className="text-sm text-gray-600">
                      One-time payment
                    </div>
                  </div>
                  <div className="text-xl font-bold text-gray-900">
                    {formatAmount(pricing.amount, pricing.currency)}
                  </div>
                </div>
              </div>

              <PaymentGatewaySelector
                candidateId={candidateId}
                featureType={featureType}
                resultId={resultId}
                amount={pricing.amount}
                currency={pricing.currency}
                onPaymentComplete={handlePaymentComplete}
                onPaymentError={handlePaymentError}
                onProcessingChange={setIsProcessing}
              />

              {/* Processing Indicator */}
              {isProcessing && (
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-blue-600 mr-3 animate-spin" />
                    <div>
                      <div className="font-medium text-blue-900">
                        Processing Payment...
                      </div>
                      <div className="text-sm text-blue-700">
                        Please wait while we process your payment
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </Modal>
    </>
  );
}
