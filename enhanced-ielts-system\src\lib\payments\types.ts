/**
 * Payment system types and interfaces
 */

export type PaymentGateway = 'click' | 'payme' | 'manual';

export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';

export type FeatureType = 'feedback' | 'certificate' | 'progress';

export interface PaymentTransaction {
  id: string;
  candidateId: string;
  organizationId: string;
  amount: number;
  currency: string;
  gateway: PaymentGateway;
  gatewayTransactionId?: string;
  status: PaymentStatus;
  featureType: FeatureType;
  resultId?: string;
  metadata: {
    gatewayResponse?: any;
    failureReason?: string;
    refundReason?: string;
    manualApprovalBy?: string;
    manualApprovalAt?: string;
  };
  createdAt: Date;
  completedAt?: Date;
}

export interface PaymentRequest {
  candidateId: string;
  organizationId: string;
  amount: number;
  currency?: string;
  gateway: PaymentGateway;
  featureType: FeatureType;
  resultId?: string;
  returnUrl: string;
  description?: string;
}

export interface PaymentResponse {
  transactionId: string;
  paymentUrl?: string;
  status: PaymentStatus;
  gatewayTransactionId?: string;
  error?: string;
}

export interface PaymentWebhookPayload {
  gateway: PaymentGateway;
  transactionId: string;
  gatewayTransactionId: string;
  status: PaymentStatus;
  amount: number;
  metadata?: any;
}

export interface PaymentConfig {
  click: {
    baseUrl: string;
    merchantId: string;
    serviceId: string;
    secretKey: string;
    userId: string;
  };
  payme: {
    baseUrl: string;
    merchantId: string;
    secretKey: string;
    testMode: boolean;
  };
  pricing: {
    feedback: {
      amount: number;
      currency: string;
      description: string;
    };
    certificate: {
      amount: number;
      currency: string;
      description: string;
    };
    progress: {
      amount: number;
      currency: string;
      description: string;
    };
  };
}

export interface AccessPermission {
  id: string;
  candidateId: string;
  resultId?: string;
  featureType: FeatureType;
  accessType: 'paid' | 'promotional' | 'manual';
  grantedBy?: string;
  grantedAt: Date;
  expiresAt?: Date;
  metadata?: {
    transactionId?: string;
    promotionId?: string;
    manualReason?: string;
  };
}

// Payment pricing configuration
export const PAYMENT_PRICING: Record<FeatureType, { amount: number; currency: string; description: string }> = {
  feedback: {
    amount: 50000, // 50,000 UZS
    currency: 'UZS',
    description: 'AI-powered personalized feedback and study recommendations',
  },
  certificate: {
    amount: 30000, // 30,000 UZS
    currency: 'UZS',
    description: 'Official IELTS certificate with verification',
  },
  progress: {
    amount: 25000, // 25,000 UZS
    currency: 'UZS',
    description: 'Detailed progress tracking and analytics',
  },
};

// Payment gateway configurations
export const GATEWAY_CONFIG = {
  click: {
    name: 'Click',
    logo: '/images/gateways/click.png',
    description: 'Pay with Click - Fast and secure',
    supportedCurrencies: ['UZS'],
    processingTime: 'Instant',
  },
  payme: {
    name: 'Payme',
    logo: '/images/gateways/payme.png',
    description: 'Pay with Payme - Convenient mobile payments',
    supportedCurrencies: ['UZS'],
    processingTime: 'Instant',
  },
  manual: {
    name: 'Manual Payment',
    logo: '/images/gateways/manual.png',
    description: 'Bank transfer or cash payment with admin approval',
    supportedCurrencies: ['UZS', 'USD'],
    processingTime: '1-3 business days',
  },
};

// Payment status descriptions
export const PAYMENT_STATUS_DESCRIPTIONS: Record<PaymentStatus, string> = {
  pending: 'Payment is being processed',
  completed: 'Payment completed successfully',
  failed: 'Payment failed - please try again',
  cancelled: 'Payment was cancelled',
  refunded: 'Payment has been refunded',
};

// Feature type descriptions
export const FEATURE_DESCRIPTIONS: Record<FeatureType, string> = {
  feedback: 'Get detailed AI-powered feedback on your IELTS performance with personalized study recommendations',
  certificate: 'Download your official IELTS certificate with verification QR code',
  progress: 'Access detailed progress tracking, performance analytics, and historical comparisons',
};

// Payment error codes
export const PAYMENT_ERROR_CODES = {
  INVALID_AMOUNT: 'INVALID_AMOUNT',
  GATEWAY_ERROR: 'GATEWAY_ERROR',
  TRANSACTION_NOT_FOUND: 'TRANSACTION_NOT_FOUND',
  ALREADY_PAID: 'ALREADY_PAID',
  INSUFFICIENT_FUNDS: 'INSUFFICIENT_FUNDS',
  EXPIRED_TRANSACTION: 'EXPIRED_TRANSACTION',
  INVALID_SIGNATURE: 'INVALID_SIGNATURE',
  FEATURE_NOT_AVAILABLE: 'FEATURE_NOT_AVAILABLE',
  ORGANIZATION_DISABLED: 'ORGANIZATION_DISABLED',
} as const;

export type PaymentErrorCode = typeof PAYMENT_ERROR_CODES[keyof typeof PAYMENT_ERROR_CODES];

// Payment validation rules
export const PAYMENT_VALIDATION = {
  minAmount: 1000, // 1,000 UZS minimum
  maxAmount: 10000000, // 10,000,000 UZS maximum
  allowedCurrencies: ['UZS', 'USD'],
  transactionTimeout: 30 * 60 * 1000, // 30 minutes
  maxRetries: 3,
};

// Webhook security
export const WEBHOOK_CONFIG = {
  timeout: 30000, // 30 seconds
  maxRetries: 5,
  retryDelay: 1000, // 1 second
  allowedIPs: {
    click: ['*************', '*************'], // Click webhook IPs
    payme: ['*************/24'], // Payme webhook IP range
  },
};

// Manual payment approval workflow
export const MANUAL_PAYMENT_CONFIG = {
  requiresApproval: true,
  autoApprovalThreshold: 0, // All manual payments require approval
  approvalTimeout: 7 * 24 * 60 * 60 * 1000, // 7 days
  reminderInterval: 24 * 60 * 60 * 1000, // 24 hours
  maxPendingPayments: 10, // Per candidate
};

// Access permission rules
export const ACCESS_PERMISSION_CONFIG = {
  defaultExpiry: {
    feedback: null, // No expiry for feedback
    certificate: 6 * 30 * 24 * 60 * 60 * 1000, // 6 months
    progress: null, // No expiry for progress
  },
  gracePeriod: 7 * 24 * 60 * 60 * 1000, // 7 days grace period
  maxConcurrentAccess: {
    feedback: 1, // One feedback per result
    certificate: 1, // One certificate per result
    progress: 1, // One progress access per candidate
  },
};
