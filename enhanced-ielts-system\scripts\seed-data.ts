import * as dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

import { db } from '../src/lib/db';
import { organizations, users, candidates, testRegistrations, testResults } from '../src/lib/db/schema';
import bcrypt from 'bcryptjs';
import { createId } from '@paralleldrive/cuid2';

async function seedData() {
  try {
    console.log('🌱 Starting database seeding...');

    // Create sample organizations
    console.log('Creating organizations...');
    const [org1, org2] = await db.insert(organizations).values([
      {
        id: createId(),
        name: 'IELTS Center Tashkent',
        slug: 'ielts-center-tashkent',
        settings: {
          timezone: 'Asia/Tashkent',
          currency: 'UZS',
          language: 'en',
          features: ['ai_feedback', 'certificates', 'progress_tracking'],
        },
        features: ['ai_feedback', 'certificates', 'progress_tracking'],
        billingPlan: 'premium',
        status: 'active',
      },
      {
        id: createId(),
        name: 'IELTS Academy Samarkand',
        slug: 'ielts-academy-samarkand',
        settings: {
          timezone: 'Asia/Tashkent',
          currency: 'UZS',
          language: 'en',
          features: ['certificates', 'progress_tracking'],
        },
        features: ['certificates', 'progress_tracking'],
        billingPlan: 'basic',
        status: 'active',
      },
    ]).returning();

    // Create sample users
    console.log('Creating users...');
    const hashedPassword = await bcrypt.hash('password123', 12);

    const [admin1, checker1, admin2] = await db.insert(users).values([
      {
        id: createId(),
        organizationId: org1.id,
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Admin Tashkent',
        role: 'admin',
        masterAdmin: false,
        status: 'active',
      },
      {
        id: createId(),
        organizationId: org1.id,
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Test Checker',
        role: 'checker',
        masterAdmin: false,
        status: 'active',
      },
      {
        id: createId(),
        organizationId: org2.id,
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Admin Samarkand',
        role: 'admin',
        masterAdmin: false,
        status: 'active',
      },
    ]).returning();

    // Create sample candidates
    console.log('Creating candidates...');
    const [candidate1, candidate2, candidate3] = await db.insert(candidates).values([
      {
        id: createId(),
        organizationId: org1.id,
        fullName: 'John Smith',
        email: '<EMAIL>',
        phoneNumber: '+998901234567',
        dateOfBirth: new Date('1995-05-15'),
        nationality: 'Uzbekistan',
        passportNumber: '*********',
        studentStatus: true,
        totalTests: 2,
      },
      {
        id: createId(),
        organizationId: org1.id,
        fullName: 'Sarah Johnson',
        email: '<EMAIL>',
        phoneNumber: '+998907654321',
        dateOfBirth: new Date('1992-08-22'),
        nationality: 'Kazakhstan',
        passportNumber: '*********',
        studentStatus: false,
        totalTests: 1,
      },
      {
        id: createId(),
        organizationId: org2.id,
        fullName: 'Ahmed Hassan',
        email: '<EMAIL>',
        phoneNumber: '+998909876543',
        dateOfBirth: new Date('1990-12-10'),
        nationality: 'Egypt',
        passportNumber: '*********',
        studentStatus: true,
        totalTests: 1,
      },
    ]).returning();

    // Create sample test registrations
    console.log('Creating test registrations...');
    const [testReg1, testReg2, testReg3] = await db.insert(testRegistrations).values([
      {
        id: createId(),
        candidateId: candidate1.id,
        candidateNumber: 'TKT001',
        testDate: new Date('2024-01-15'),
        testCenter: 'IELTS Center Tashkent',
        status: 'completed',
      },
      {
        id: createId(),
        candidateId: candidate2.id,
        candidateNumber: 'TKT002',
        testDate: new Date('2024-02-20'),
        testCenter: 'IELTS Center Tashkent',
        status: 'completed',
      },
      {
        id: createId(),
        candidateId: candidate3.id,
        candidateNumber: 'SMK001',
        testDate: new Date('2024-03-10'),
        testCenter: 'IELTS Academy Samarkand',
        status: 'completed',
      },
    ]).returning();

    // Create sample test results
    console.log('Creating test results...');
    await db.insert(testResults).values([
      {
        id: createId(),
        testRegistrationId: testReg1.id,
        listeningScore: 32,
        listeningBandScore: '7.5',
        readingScore: 35,
        readingBandScore: '8.0',
        writingTask1Score: '7.0',
        writingTask2Score: '7.5',
        writingBandScore: '7.0',
        speakingFluencyScore: '7.5',
        speakingLexicalScore: '7.0',
        speakingGrammarScore: '7.5',
        speakingPronunciationScore: '7.0',
        speakingBandScore: '7.0',
        overallBandScore: '7.5',
        status: 'verified',
        enteredBy: checker1.id,
        verifiedBy: admin1.id,
      },
      {
        id: createId(),
        testRegistrationId: testReg2.id,
        listeningScore: 28,
        listeningBandScore: '6.5',
        readingScore: 30,
        readingBandScore: '7.0',
        writingTask1Score: '6.0',
        writingTask2Score: '6.5',
        writingBandScore: '6.0',
        speakingFluencyScore: '6.5',
        speakingLexicalScore: '6.0',
        speakingGrammarScore: '6.5',
        speakingPronunciationScore: '6.0',
        speakingBandScore: '6.0',
        overallBandScore: '6.5',
        status: 'verified',
        enteredBy: checker1.id,
        verifiedBy: admin1.id,
      },
      {
        id: createId(),
        testRegistrationId: testReg3.id,
        listeningScore: 25,
        listeningBandScore: '6.0',
        readingScore: 27,
        readingBandScore: '6.5',
        writingTask1Score: '5.5',
        writingTask2Score: '6.0',
        writingBandScore: '5.5',
        speakingFluencyScore: '6.0',
        speakingLexicalScore: '5.5',
        speakingGrammarScore: '6.0',
        speakingPronunciationScore: '5.5',
        speakingBandScore: '5.5',
        overallBandScore: '6.0',
        status: 'verified',
        enteredBy: admin2.id,
        verifiedBy: admin2.id,
      },
    ]);

    console.log('✅ Database seeding completed successfully!');
    console.log('📊 Created:');
    console.log(`  - ${2} organizations`);
    console.log(`  - ${3} users`);
    console.log(`  - ${3} candidates`);
    console.log(`  - ${3} test registrations`);
    console.log(`  - ${3} test results`);

    console.log('\n🔑 Login credentials:');
    console.log('Admin (Tashkent): <EMAIL> / password123');
    console.log('Checker (Tashkent): <EMAIL> / password123');
    console.log('Admin (Samarkand): <EMAIL> / password123');

    process.exit(0);
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

seedData();
