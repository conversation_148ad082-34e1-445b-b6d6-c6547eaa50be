/**
 * IELTS Band Score Calculation Utilities
 * Based on official IELTS scoring guidelines
 */

// Listening score conversion table (raw score to band score)
const LISTENING_CONVERSION = {
  39: 9.0, 38: 9.0, 37: 8.5, 36: 8.5, 35: 8.0, 34: 8.0, 33: 7.5, 32: 7.5,
  31: 7.0, 30: 7.0, 29: 6.5, 28: 6.5, 27: 6.0, 26: 6.0, 25: 5.5, 24: 5.5,
  23: 5.0, 22: 5.0, 21: 4.5, 20: 4.5, 19: 4.0, 18: 4.0, 17: 3.5, 16: 3.5,
  15: 3.0, 14: 3.0, 13: 2.5, 12: 2.5, 11: 2.0, 10: 2.0, 9: 1.5, 8: 1.5,
  7: 1.0, 6: 1.0, 5: 0.5, 4: 0.5, 3: 0.0, 2: 0.0, 1: 0.0, 0: 0.0
};

// Reading score conversion table (raw score to band score)
// Academic and General Training have slightly different conversions
const READING_ACADEMIC_CONVERSION = {
  39: 9.0, 38: 9.0, 37: 8.5, 36: 8.5, 35: 8.0, 34: 8.0, 33: 7.5, 32: 7.5,
  31: 7.0, 30: 7.0, 29: 6.5, 28: 6.5, 27: 6.0, 26: 6.0, 25: 5.5, 24: 5.5,
  23: 5.0, 22: 5.0, 21: 4.5, 20: 4.5, 19: 4.0, 18: 4.0, 17: 3.5, 16: 3.5,
  15: 3.0, 14: 3.0, 13: 2.5, 12: 2.5, 11: 2.0, 10: 2.0, 9: 1.5, 8: 1.5,
  7: 1.0, 6: 1.0, 5: 0.5, 4: 0.5, 3: 0.0, 2: 0.0, 1: 0.0, 0: 0.0
};

const READING_GENERAL_CONVERSION = {
  40: 9.0, 39: 8.5, 38: 8.5, 37: 8.0, 36: 8.0, 35: 7.5, 34: 7.5, 33: 7.0,
  32: 7.0, 31: 6.5, 30: 6.5, 29: 6.0, 28: 6.0, 27: 5.5, 26: 5.5, 25: 5.0,
  24: 5.0, 23: 4.5, 22: 4.5, 21: 4.0, 20: 4.0, 19: 3.5, 18: 3.5, 17: 3.0,
  16: 3.0, 15: 2.5, 14: 2.5, 13: 2.0, 12: 2.0, 11: 1.5, 10: 1.5, 9: 1.0,
  8: 1.0, 7: 0.5, 6: 0.5, 5: 0.0, 4: 0.0, 3: 0.0, 2: 0.0, 1: 0.0, 0: 0.0
};

/**
 * Calculate band score for Listening or Reading sections
 */
export function calculateBandScore(
  section: 'listening' | 'reading', 
  rawScore: number, 
  testType: 'academic' | 'general' = 'academic'
): number {
  // Ensure raw score is within valid range
  const score = Math.max(0, Math.min(40, Math.floor(rawScore)));
  
  if (section === 'listening') {
    return LISTENING_CONVERSION[score as keyof typeof LISTENING_CONVERSION] || 0;
  }
  
  if (section === 'reading') {
    const conversionTable = testType === 'academic' 
      ? READING_ACADEMIC_CONVERSION 
      : READING_GENERAL_CONVERSION;
    return conversionTable[score as keyof typeof conversionTable] || 0;
  }
  
  return 0;
}

/**
 * Calculate overall band score from individual section scores
 * IELTS uses the average of all four skills, rounded to the nearest 0.5
 */
export function calculateOverallBandScore(
  listening: number,
  reading: number,
  writing: number,
  speaking: number
): number {
  const average = (listening + reading + writing + speaking) / 4;
  
  // Round to nearest 0.5
  return Math.round(average * 2) / 2;
}

/**
 * Round individual band scores to nearest 0.5
 */
export function roundToBandScore(score: number): number {
  return Math.round(score * 2) / 2;
}

/**
 * Validate if a band score is valid (0-9 in 0.5 increments)
 */
export function isValidBandScore(score: number): boolean {
  return score >= 0 && score <= 9 && (score * 2) % 1 === 0;
}

/**
 * Get band score description
 */
export function getBandScoreDescription(bandScore: number): string {
  const descriptions = {
    9: 'Expert User',
    8.5: 'Very Good User',
    8: 'Very Good User',
    7.5: 'Good User',
    7: 'Good User',
    6.5: 'Competent User',
    6: 'Competent User',
    5.5: 'Modest User',
    5: 'Modest User',
    4.5: 'Limited User',
    4: 'Limited User',
    3.5: 'Extremely Limited User',
    3: 'Extremely Limited User',
    2.5: 'Intermittent User',
    2: 'Intermittent User',
    1.5: 'Non User',
    1: 'Non User',
    0.5: 'Did not attempt the test',
    0: 'Did not attempt the test'
  };
  
  return descriptions[bandScore as keyof typeof descriptions] || 'Invalid Score';
}

/**
 * Calculate writing band score from task scores
 * Task 2 is weighted more heavily (2/3) than Task 1 (1/3)
 */
export function calculateWritingBandScore(task1Score: number, task2Score: number): number {
  // Weighted average: Task 1 = 1/3, Task 2 = 2/3
  const weightedAverage = (task1Score + task2Score * 2) / 3;
  return roundToBandScore(weightedAverage);
}

/**
 * Calculate speaking band score from component scores
 * All four criteria are equally weighted
 */
export function calculateSpeakingBandScore(
  fluency: number,
  lexical: number,
  grammar: number,
  pronunciation: number
): number {
  const average = (fluency + lexical + grammar + pronunciation) / 4;
  return roundToBandScore(average);
}

/**
 * Validate test result data
 */
export function validateTestResult(result: {
  listeningScore?: number;
  readingScore?: number;
  writingTask1Score?: number;
  writingTask2Score?: number;
  speakingFluencyScore?: number;
  speakingLexicalScore?: number;
  speakingGrammarScore?: number;
  speakingPronunciationScore?: number;
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Validate listening score
  if (result.listeningScore !== undefined) {
    if (result.listeningScore < 0 || result.listeningScore > 40) {
      errors.push('Listening score must be between 0 and 40');
    }
  }
  
  // Validate reading score
  if (result.readingScore !== undefined) {
    if (result.readingScore < 0 || result.readingScore > 40) {
      errors.push('Reading score must be between 0 and 40');
    }
  }
  
  // Validate writing scores
  if (result.writingTask1Score !== undefined) {
    if (!isValidBandScore(result.writingTask1Score)) {
      errors.push('Writing Task 1 score must be a valid band score (0-9 in 0.5 increments)');
    }
  }
  
  if (result.writingTask2Score !== undefined) {
    if (!isValidBandScore(result.writingTask2Score)) {
      errors.push('Writing Task 2 score must be a valid band score (0-9 in 0.5 increments)');
    }
  }
  
  // Validate speaking scores
  const speakingScores = [
    { score: result.speakingFluencyScore, name: 'Speaking Fluency' },
    { score: result.speakingLexicalScore, name: 'Speaking Lexical Resource' },
    { score: result.speakingGrammarScore, name: 'Speaking Grammar' },
    { score: result.speakingPronunciationScore, name: 'Speaking Pronunciation' }
  ];
  
  speakingScores.forEach(({ score, name }) => {
    if (score !== undefined && !isValidBandScore(score)) {
      errors.push(`${name} score must be a valid band score (0-9 in 0.5 increments)`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Get performance level based on band score
 */
export function getPerformanceLevel(bandScore: number): {
  level: string;
  color: string;
  description: string;
} {
  if (bandScore >= 8.5) {
    return {
      level: 'Excellent',
      color: 'green',
      description: 'Very high proficiency level'
    };
  } else if (bandScore >= 7.0) {
    return {
      level: 'Good',
      color: 'blue',
      description: 'Good proficiency level'
    };
  } else if (bandScore >= 6.0) {
    return {
      level: 'Competent',
      color: 'yellow',
      description: 'Competent proficiency level'
    };
  } else if (bandScore >= 5.0) {
    return {
      level: 'Modest',
      color: 'orange',
      description: 'Modest proficiency level'
    };
  } else {
    return {
      level: 'Limited',
      color: 'red',
      description: 'Limited proficiency level'
    };
  }
}
