import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { z } from 'zod';
import { validatePromotionalCode, applyPromotionalCode } from '@/lib/promotions/utils';
import { ApplyPromotionalCodeRequest, FeatureType } from '@/lib/promotions/types';

const validateCodeSchema = z.object({
  code: z.string().min(1),
  candidateId: z.string().min(1),
  featureType: z.enum(['feedback', 'certificate', 'progress']),
  originalAmount: z.number().positive(),
  resultId: z.string().optional(),
});

const applyCodeSchema = z.object({
  code: z.string().min(1),
  candidateId: z.string().min(1),
  featureType: z.enum(['feedback', 'certificate', 'progress']),
  originalAmount: z.number().positive(),
  resultId: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action, ...data } = body;

    if (action === 'validate') {
      const validatedData = validateCodeSchema.parse(data);

      const validation = await validatePromotionalCode(
        validatedData.code,
        validatedData.candidateId,
        validatedData.featureType as FeatureType,
        validatedData.originalAmount,
        session.user.organizationId,
        validatedData.resultId
      );

      return NextResponse.json(validation);
    }

    if (action === 'apply') {
      const validatedData = applyCodeSchema.parse(data);

      const applyRequest: ApplyPromotionalCodeRequest = {
        code: validatedData.code,
        candidateId: validatedData.candidateId,
        featureType: validatedData.featureType as FeatureType,
        originalAmount: validatedData.originalAmount,
        resultId: validatedData.resultId,
        organizationId: session.user.organizationId,
      };

      const result = await applyPromotionalCode(applyRequest);

      if (!result.success) {
        return NextResponse.json({ error: result.error }, { status: 400 });
      }

      return NextResponse.json({
        success: true,
        usage: result.usage,
        message: 'Promotional code applied successfully',
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Promotional code validation/application error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to process promotional code' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const candidateId = searchParams.get('candidateId');
    const featureType = searchParams.get('featureType') as FeatureType;
    const originalAmount = parseFloat(searchParams.get('originalAmount') || '0');
    const resultId = searchParams.get('resultId');

    if (!code || !candidateId || !featureType || !originalAmount) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    const validation = await validatePromotionalCode(
      code,
      candidateId,
      featureType,
      originalAmount,
      session.user.organizationId,
      resultId || undefined
    );

    return NextResponse.json(validation);
  } catch (error) {
    console.error('Promotional code validation error:', error);
    return NextResponse.json(
      { error: 'Failed to validate promotional code' },
      { status: 500 }
    );
  }
}
