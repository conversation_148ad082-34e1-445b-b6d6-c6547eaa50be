'use client';

import { useState } from 'react';
import { PaywallOverlay } from '@/components/paywall/paywall-overlay';
import { usePaywall } from '@/hooks/use-paywall';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  TrendingDown, 
  Minus,
  BarChart3,
  Calendar,
  Target,
  Award,
  Lock,
  ArrowUp,
  ArrowDown
} from 'lucide-react';

interface Candidate {
  id: string;
  fullName: string;
}

interface TestData {
  registration: {
    id: string;
    testDate: Date;
    testCenter: string;
  };
  result: {
    id: string;
    listeningBandScore: string | null;
    readingBandScore: string | null;
    writingBandScore: string | null;
    speakingBandScore: string | null;
    overallBandScore: string | null;
    createdAt: Date;
  };
}

interface ProgressTabProps {
  candidate: Candidate;
  testData: TestData[];
}

export function ProgressTab({ candidate, testData }: ProgressTabProps) {
  const [showPaywall, setShowPaywall] = useState(false);
  const { hasAccess, isLoading } = usePaywall({
    candidateId: candidate.id,
    featureType: 'progress',
  });

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const calculateTrend = (current: number, previous: number) => {
    const diff = current - previous;
    if (Math.abs(diff) < 0.5) return { trend: 'stable', icon: Minus, color: 'text-gray-500' };
    if (diff > 0) return { trend: 'up', icon: TrendingUp, color: 'text-green-500' };
    return { trend: 'down', icon: TrendingDown, color: 'text-red-500' };
  };

  const getProgressData = () => {
    if (testData.length < 2) return null;

    const latest = testData[0];
    const previous = testData[1];

    const latestOverall = parseFloat(latest.result.overallBandScore || '0');
    const previousOverall = parseFloat(previous.result.overallBandScore || '0');

    return {
      overall: {
        current: latestOverall,
        previous: previousOverall,
        trend: calculateTrend(latestOverall, previousOverall),
      },
      listening: {
        current: parseFloat(latest.result.listeningBandScore || '0'),
        previous: parseFloat(previous.result.listeningBandScore || '0'),
      },
      reading: {
        current: parseFloat(latest.result.readingBandScore || '0'),
        previous: parseFloat(previous.result.readingBandScore || '0'),
      },
      writing: {
        current: parseFloat(latest.result.writingBandScore || '0'),
        previous: parseFloat(previous.result.writingBandScore || '0'),
      },
      speaking: {
        current: parseFloat(latest.result.speakingBandScore || '0'),
        previous: parseFloat(previous.result.speakingBandScore || '0'),
      },
    };
  };

  const renderBasicProgress = () => {
    if (testData.length === 0) {
      return (
        <div className="text-center py-12">
          <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Progress Data</h3>
          <p className="text-gray-600">
            Take more tests to see your progress over time.
          </p>
        </div>
      );
    }

    if (testData.length === 1) {
      const test = testData[0];
      return (
        <div className="space-y-6">
          <div className="text-center py-8">
            <Target className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">First Test Completed!</h3>
            <p className="text-gray-600 mb-4">
              Take another test to see your progress and improvement trends.
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
              <h4 className="font-medium text-blue-900 mb-2">Your Current Score</h4>
              <div className="text-2xl font-bold text-blue-600">
                {test.result.overallBandScore}
              </div>
              <div className="text-sm text-blue-700">
                Test Date: {formatDate(test.registration.testDate)}
              </div>
            </div>
          </div>
        </div>
      );
    }

    const progressData = getProgressData();
    if (!progressData) return null;

    return (
      <div className="space-y-6">
        {/* Overall Progress */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Overall Progress</h3>
          <div className="flex items-center justify-between">
            <div>
              <div className="text-3xl font-bold text-blue-600">
                {progressData.overall.current}
              </div>
              <div className="text-sm text-gray-600">Current Band Score</div>
            </div>
            <div className="text-right">
              <div className="flex items-center space-x-2">
                <progressData.overall.trend.icon 
                  className={`h-5 w-5 ${progressData.overall.trend.color}`} 
                />
                <span className={`font-medium ${progressData.overall.trend.color}`}>
                  {Math.abs(progressData.overall.current - progressData.overall.previous).toFixed(1)}
                </span>
              </div>
              <div className="text-sm text-gray-600">
                vs Previous: {progressData.overall.previous}
              </div>
            </div>
          </div>
        </div>

        {/* Skills Progress */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[
            { name: 'Listening', data: progressData.listening },
            { name: 'Reading', data: progressData.reading },
            { name: 'Writing', data: progressData.writing },
            { name: 'Speaking', data: progressData.speaking },
          ].map((skill) => {
            const trend = calculateTrend(skill.data.current, skill.data.previous);
            return (
              <div key={skill.name} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{skill.name}</h4>
                  <trend.icon className={`h-4 w-4 ${trend.color}`} />
                </div>
                <div className="flex items-center justify-between">
                  <div className="text-xl font-bold text-gray-900">
                    {skill.data.current}
                  </div>
                  <div className="text-sm text-gray-600">
                    was {skill.data.previous}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Test History */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Test History</h3>
          <div className="space-y-3">
            {testData.slice(0, 3).map((test, index) => (
              <div key={test.result.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <Calendar className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">
                      {formatDate(test.registration.testDate)}
                    </div>
                    <div className="text-sm text-gray-600">
                      {test.registration.testCenter}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-gray-900">
                    {test.result.overallBandScore}
                  </div>
                  {index === 0 && (
                    <Badge variant="blue" className="text-xs">Latest</Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!hasAccess) {
    return (
      <PaywallOverlay
        isOpen={showPaywall}
        onClose={() => setShowPaywall(false)}
        featureType="progress"
        candidateId={candidate.id}
        onPaymentSuccess={() => window.location.reload()}
      >
        <div className="relative">
          <div className="filter blur-sm pointer-events-none">
            {renderBasicProgress()}
          </div>
          <div className="absolute inset-0 bg-white/80 flex items-center justify-center">
            <div className="text-center p-6">
              <Lock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Premium Progress Analytics
              </h3>
              <p className="text-gray-600 mb-4">
                Unlock detailed progress tracking, performance analytics, and historical comparisons
              </p>
              <Button onClick={() => setShowPaywall(true)}>
                <Award className="h-4 w-4 mr-2" />
                Unlock Progress Analytics - 25,000 UZS
              </Button>
            </div>
          </div>
        </div>
      </PaywallOverlay>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Progress Analytics</h2>
        <Badge variant="green">Premium Feature</Badge>
      </div>

      {renderBasicProgress()}

      {/* Advanced Analytics (Premium) */}
      {testData.length >= 2 && (
        <div className="space-y-6">
          {/* Improvement Recommendations */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-900 mb-4 flex items-center">
              <Target className="h-5 w-5 mr-2" />
              Improvement Recommendations
            </h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <ArrowUp className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <div className="font-medium text-green-900">Strengths</div>
                  <div className="text-sm text-green-800">
                    Your listening skills show consistent improvement. Keep practicing with varied accents.
                  </div>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <ArrowDown className="h-5 w-5 text-orange-600 mt-0.5" />
                <div>
                  <div className="font-medium text-orange-900">Areas for Improvement</div>
                  <div className="text-sm text-orange-800">
                    Focus on writing task 2 structure and coherence to boost your writing score.
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Goal Setting */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-4">Goal Tracking</h3>
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-blue-900">Target Band Score: 7.0</span>
                  <span className="text-sm text-blue-700">
                    Current: {testData[0]?.result.overallBandScore || 'N/A'}
                  </span>
                </div>
                <div className="w-full bg-blue-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ 
                      width: `${Math.min(100, (parseFloat(testData[0]?.result.overallBandScore || '0') / 7.0) * 100)}%` 
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
