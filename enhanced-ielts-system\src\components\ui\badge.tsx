import * as React from 'react';
import { cn } from '@/lib/utils/cn';

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'orange' | 'gray';
}

function Badge({ className, variant = 'default', ...props }: BadgeProps) {
  return (
    <div
      className={cn(
        'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
        {
          'border-transparent bg-primary text-primary-foreground hover:bg-primary/80': variant === 'default',
          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80': variant === 'secondary',
          'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80': variant === 'destructive',
          'text-foreground': variant === 'outline',
          'border-transparent bg-blue-100 text-blue-800': variant === 'blue',
          'border-transparent bg-green-100 text-green-800': variant === 'green',
          'border-transparent bg-yellow-100 text-yellow-800': variant === 'yellow',
          'border-transparent bg-red-100 text-red-800': variant === 'red',
          'border-transparent bg-purple-100 text-purple-800': variant === 'purple',
          'border-transparent bg-orange-100 text-orange-800': variant === 'orange',
          'border-transparent bg-gray-100 text-gray-800': variant === 'gray',
        },
        className
      )}
      {...props}
    />
  );
}

export { Badge };
