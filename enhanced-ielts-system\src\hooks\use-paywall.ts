'use client';

import { useState, useEffect } from 'react';
import { FeatureType } from '@/lib/payments/types';

interface UsePaywallProps {
  candidateId: string;
  featureType: FeatureType;
  resultId?: string;
}

interface PaywallState {
  hasAccess: boolean;
  isLoading: boolean;
  error: string | null;
  accessType?: 'paid' | 'promotional' | 'manual';
  expiresAt?: Date;
  daysRemaining?: number;
}

export function usePaywall({ candidateId, featureType, resultId }: UsePaywallProps) {
  const [state, setState] = useState<PaywallState>({
    hasAccess: false,
    isLoading: true,
    error: null,
  });

  const checkAccess = async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const params = new URLSearchParams({
        candidateId,
        featureType,
      });

      if (resultId) {
        params.append('resultId', resultId);
      }

      const response = await fetch(`/api/payments/verify?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to check access');
      }

      const data = await response.json();

      setState({
        hasAccess: data.hasAccess,
        isLoading: false,
        error: null,
        accessType: data.accessType,
        expiresAt: data.expiresAt ? new Date(data.expiresAt) : undefined,
        daysRemaining: data.daysRemaining,
      });
    } catch (error) {
      setState({
        hasAccess: false,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  const refreshAccess = () => {
    checkAccess();
  };

  useEffect(() => {
    checkAccess();
  }, [candidateId, featureType, resultId]);

  return {
    ...state,
    refreshAccess,
  };
}

// Note: withPaywall HOC removed as it's not used in the current implementation
// Components handle paywall logic directly using the usePaywall hook
