import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import {
  promotionalCodes,
  promotionalCodeUsage,
  referralLinks,
  referralConversions,
} from '@/lib/db/schema';
import { eq, and, gte, lte, desc, count, sum, sql } from 'drizzle-orm';
import { PromotionalAnalytics } from '@/lib/promotions/types';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (session.user.role !== 'admin' && !session.user.masterAdmin) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Set default date range (last 30 days)
    const defaultEndDate = new Date();
    const defaultStartDate = new Date();
    defaultStartDate.setDate(defaultStartDate.getDate() - 30);

    const fromDate = startDate ? new Date(startDate) : defaultStartDate;
    const toDate = endDate ? new Date(endDate) : defaultEndDate;

    // Get promotional codes statistics
    const [codesStats] = await db
      .select({
        totalCodes: count(),
        activeCodes: sql<number>`SUM(CASE WHEN ${promotionalCodes.status} = 'active' THEN 1 ELSE 0 END)`,
      })
      .from(promotionalCodes)
      .where(eq(promotionalCodes.organizationId, session.user.organizationId));

    // Get usage statistics
    const [usageStats] = await db
      .select({
        totalUsage: count(),
        totalDiscountGiven: sum(promotionalCodeUsage.discountAmount),
      })
      .from(promotionalCodeUsage)
      .innerJoin(promotionalCodes, eq(promotionalCodeUsage.codeId, promotionalCodes.id))
      .where(
        and(
          eq(promotionalCodes.organizationId, session.user.organizationId),
          gte(promotionalCodeUsage.usedAt, fromDate),
          lte(promotionalCodeUsage.usedAt, toDate)
        )
      );

    // Get top performing codes
    const topPerformingCodes = await db
      .select({
        code: promotionalCodes.code,
        name: promotionalCodes.name,
        usageCount: count(promotionalCodeUsage.id),
        totalDiscount: sum(promotionalCodeUsage.discountAmount),
      })
      .from(promotionalCodes)
      .leftJoin(promotionalCodeUsage, eq(promotionalCodes.id, promotionalCodeUsage.codeId))
      .where(
        and(
          eq(promotionalCodes.organizationId, session.user.organizationId),
          promotionalCodeUsage.usedAt ? gte(promotionalCodeUsage.usedAt, fromDate) : undefined,
          promotionalCodeUsage.usedAt ? lte(promotionalCodeUsage.usedAt, toDate) : undefined
        )
      )
      .groupBy(promotionalCodes.id, promotionalCodes.code, promotionalCodes.name)
      .orderBy(desc(count(promotionalCodeUsage.id)))
      .limit(10);

    // Get usage by feature type
    const usageByFeature = await db
      .select({
        featureType: promotionalCodeUsage.featureType,
        usage: count(),
      })
      .from(promotionalCodeUsage)
      .innerJoin(promotionalCodes, eq(promotionalCodeUsage.codeId, promotionalCodes.id))
      .where(
        and(
          eq(promotionalCodes.organizationId, session.user.organizationId),
          gte(promotionalCodeUsage.usedAt, fromDate),
          lte(promotionalCodeUsage.usedAt, toDate)
        )
      )
      .groupBy(promotionalCodeUsage.featureType);

    // Get usage by month (last 12 months)
    const usageByMonth = await db
      .select({
        month: sql<string>`TO_CHAR(${promotionalCodeUsage.usedAt}, 'YYYY-MM')`,
        usage: count(),
        discount: sum(promotionalCodeUsage.discountAmount),
      })
      .from(promotionalCodeUsage)
      .innerJoin(promotionalCodes, eq(promotionalCodeUsage.codeId, promotionalCodes.id))
      .where(
        and(
          eq(promotionalCodes.organizationId, session.user.organizationId),
          gte(promotionalCodeUsage.usedAt, new Date(Date.now() - 365 * 24 * 60 * 60 * 1000))
        )
      )
      .groupBy(sql`TO_CHAR(${promotionalCodeUsage.usedAt}, 'YYYY-MM')`)
      .orderBy(sql`TO_CHAR(${promotionalCodeUsage.usedAt}, 'YYYY-MM')`);

    // Get referral statistics
    const [referralStats] = await db
      .select({
        totalReferralLinks: count(referralLinks.id),
        totalClicks: sum(referralLinks.clickCount),
        totalConversions: sum(referralLinks.conversionCount),
        totalEarnings: sum(referralLinks.totalEarnings),
      })
      .from(referralLinks)
      .where(eq(referralLinks.organizationId, session.user.organizationId));

    const [referralConversionStats] = await db
      .select({
        pendingEarnings: sql<number>`SUM(CASE WHEN ${referralConversions.status} = 'pending' THEN ${referralConversions.commissionAmount} ELSE 0 END)`,
        confirmedEarnings: sql<number>`SUM(CASE WHEN ${referralConversions.status} = 'confirmed' THEN ${referralConversions.commissionAmount} ELSE 0 END)`,
        paidEarnings: sql<number>`SUM(CASE WHEN ${referralConversions.status} = 'paid' THEN ${referralConversions.commissionAmount} ELSE 0 END)`,
      })
      .from(referralConversions)
      .innerJoin(referralLinks, eq(referralConversions.referralLinkId, referralLinks.id))
      .where(eq(referralLinks.organizationId, session.user.organizationId));

    // Format the analytics data
    const analytics: PromotionalAnalytics & {
      referralStats: any;
      dateRange: { from: string; to: string };
    } = {
      totalCodes: codesStats.totalCodes || 0,
      activeCodes: codesStats.activeCodes || 0,
      totalUsage: usageStats.totalUsage || 0,
      totalDiscountGiven: parseFloat(usageStats.totalDiscountGiven || '0'),
      topPerformingCodes: topPerformingCodes.map(code => ({
        code: code.code,
        name: code.name,
        usageCount: code.usageCount || 0,
        totalDiscount: parseFloat(code.totalDiscount || '0'),
      })),
      usageByFeature: usageByFeature.reduce((acc, item) => {
        acc[item.featureType as keyof typeof acc] = item.usage;
        return acc;
      }, {} as Record<string, number>),
      usageByMonth: usageByMonth.map(item => ({
        month: item.month,
        usage: item.usage || 0,
        discount: parseFloat(item.discount || '0'),
      })),
      referralStats: {
        totalReferralLinks: referralStats.totalReferralLinks || 0,
        totalClicks: referralStats.totalClicks || 0,
        totalConversions: referralStats.totalConversions || 0,
        totalEarnings: parseFloat(referralStats.totalEarnings || '0'),
        pendingEarnings: parseFloat(referralConversionStats.pendingEarnings?.toString() || '0'),
        confirmedEarnings: parseFloat(referralConversionStats.confirmedEarnings?.toString() || '0'),
        paidEarnings: parseFloat(referralConversionStats.paidEarnings?.toString() || '0'),
        conversionRate: referralStats.totalClicks > 0 
          ? ((referralStats.totalConversions || 0) / (referralStats.totalClicks || 1)) * 100 
          : 0,
      },
      dateRange: {
        from: fromDate.toISOString(),
        to: toDate.toISOString(),
      },
    };

    return NextResponse.json(analytics);
  } catch (error) {
    console.error('Get promotional analytics error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch promotional analytics' },
      { status: 500 }
    );
  }
}
