# Enhanced IELTS Certification System

A comprehensive multi-organization IELTS certification platform with premium paywall features, AI-powered feedback, and integrated payment processing.

## 🚀 Features

- **Multi-Organization Architecture**: Master system managing multiple test centers
- **Single Candidate Profiles**: One profile per person with multi-test history
- **Paywall System**: Premium AI feedback and certificate access
- **Payment Integration**: Click/Payme APIs with manual approval
- **Progress Tracking**: Historical performance visualization
- **Certificate Lifecycle**: 6-month expiration with auto-deletion
- **Promotional System**: Flexible rules for free access

## 🛠️ Technology Stack

- **Frontend**: Next.js 15 (App Router), TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, NextAuth.js v5
- **Database**: PostgreSQL with Dr<PERSON>zle ORM
- **UI Components**: Radix UI, Lucide React
- **Forms**: React Hook Form + Zod validation
- **Charts**: Recharts
- **AI**: Anthropic Claude API
- **Payments**: Click API + Payme API

## 📋 Prerequisites

- Node.js 18+
- PostgreSQL database
- npm or yarn

## 🔧 Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd enhanced-ielts-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Edit `.env.local` with your configuration:
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/enhanced_ielts_system"
   NEXTAUTH_SECRET="your-secret-key-here"
   NEXTAUTH_URL="http://localhost:3000"
   ANTHROPIC_API_KEY="your-anthropic-api-key"
   # ... other variables
   ```

4. **Set up the database**
   ```bash
   # Generate and run migrations
   npm run db:generate
   npm run db:migrate

   # Seed initial data
   npm run db:seed

   # Create master admin user
   npm run setup:master-admin
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 👤 Default Login Credentials

After running the setup scripts, you can log in with:

**Master Admin:**
- Email: `<EMAIL>`
- Password: `admin123`

**Test Organization Admins:**
- Tashkent: `<EMAIL>` / `password123`
- Samarkand: `<EMAIL>` / `password123`

**Test Checker:**
- Email: `<EMAIL>`
- Password: `password123`

⚠️ **Important**: Change these passwords in production!

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication pages
│   ├── (public)/          # Public pages
│   ├── master/            # Master admin routes
│   ├── admin/             # Organization admin routes
│   ├── checker/           # Test checker routes
│   └── api/               # API routes
├── components/            # Reusable components
│   ├── ui/               # Base UI components
│   ├── forms/            # Form components
│   ├── layout/           # Layout components
│   └── specialized/      # Feature-specific components
├── lib/                  # Core utilities
│   ├── db/              # Database layer
│   ├── auth/            # Authentication
│   ├── payments/        # Payment processing
│   └── utils/           # Utility functions
└── types/               # TypeScript definitions
```

## 🗄️ Database Schema

The system uses a comprehensive PostgreSQL schema with the following main tables:

- `organizations` - Test center management
- `users` - Multi-role authentication
- `candidates` - Single candidate profiles
- `test_registrations` - Test scheduling
- `test_results` - Comprehensive IELTS scoring
- `payment_transactions` - Payment tracking
- `access_permissions` - Premium feature access
- `promotional_rules` - Flexible promotion system
- `ai_feedback` - Generated feedback storage
- `certificate_lifecycle` - Certificate management

## 🔐 User Roles & Permissions

1. **Master Admin**: System-wide management, organization creation
2. **Organization Admin**: Candidate management, results oversight, promotions
3. **Test Checker**: Result entry and verification

## 💳 Payment Integration

The system supports multiple payment gateways:

- **Click**: Uzbekistan payment gateway
- **Payme**: Popular regional payment system
- **Manual**: Admin-approved payments

## 🤖 AI Features

- Personalized feedback generation using Anthropic Claude
- Study recommendations based on performance
- Strength and weakness analysis
- Custom study plans

## 📊 Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
npm run db:generate  # Generate database migrations
npm run db:migrate   # Run database migrations
npm run db:studio    # Open Drizzle Studio
npm run db:seed      # Seed sample data
```

## 🚀 Deployment

The application is designed to be deployed on Vercel with a PostgreSQL database (recommended: Neon).

1. **Deploy to Vercel**
   ```bash
   vercel --prod
   ```

2. **Set up environment variables** in Vercel dashboard

3. **Run database migrations** in production

## 📝 Development Phases

The project follows an 8-phase development approach:

1. **Phase 1**: Project Foundation (✅ Complete)
2. **Phase 2**: Authentication & Organization System (🚧 In Progress)
3. **Phase 3**: Core Candidate & Test Management
4. **Phase 4**: Payment Integration & Paywall
5. **Phase 5**: Public Results Interface
6. **Phase 6**: AI Feedback System
7. **Phase 7**: Certificate System
8. **Phase 8**: Promotional System

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and type checking
5. Submit a pull request

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For support and questions, please contact the development team.
