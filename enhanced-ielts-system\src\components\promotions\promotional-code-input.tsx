'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

// Simple Card component
const Card = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
  <div className={`bg-white rounded-lg border shadow-sm p-4 ${className}`}>{children}</div>
);
import { Loader2, Tag, CheckCircle, XCircle, Percent, DollarSign, Gift } from 'lucide-react';
import { PromotionalCodeValidation, FeatureType } from '@/lib/promotions/types';

interface PromotionalCodeInputProps {
  candidateId: string;
  featureType: FeatureType;
  originalAmount: number;
  resultId?: string;
  onCodeApplied: (validation: PromotionalCodeValidation) => void;
  onCodeRemoved: () => void;
  disabled?: boolean;
}

export function PromotionalCodeInput({
  candidateId,
  featureType,
  originalAmount,
  resultId,
  onCodeApplied,
  onCodeRemoved,
  disabled = false,
}: PromotionalCodeInputProps) {
  const [code, setCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [validation, setValidation] = useState<PromotionalCodeValidation | null>(null);
  const [error, setError] = useState<string | null>(null);

  const validateCode = async () => {
    if (!code.trim()) {
      setError('Please enter a promotional code');
      return;
    }

    setIsValidating(true);
    setError(null);

    try {
      const response = await fetch('/api/promotions/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'validate',
          code: code.trim(),
          candidateId,
          featureType,
          originalAmount,
          resultId,
        }),
      });

      const result = await response.json();

      if (response.ok && result.isValid) {
        setValidation(result);
        onCodeApplied(result);
      } else {
        setError(result.error || 'Invalid promotional code');
        setValidation(null);
      }
    } catch (err) {
      setError('Failed to validate promotional code');
      setValidation(null);
    } finally {
      setIsValidating(false);
    }
  };

  const removeCode = () => {
    setCode('');
    setValidation(null);
    setError(null);
    onCodeRemoved();
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('uz-UZ', {
      style: 'currency',
      currency: 'UZS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getDiscountIcon = (type: string) => {
    switch (type) {
      case 'percentage':
        return <Percent className="h-4 w-4" />;
      case 'fixed_amount':
        return <DollarSign className="h-4 w-4" />;
      case 'free_access':
        return <Gift className="h-4 w-4" />;
      default:
        return <Tag className="h-4 w-4" />;
    }
  };

  const getDiscountText = (validation: PromotionalCodeValidation) => {
    if (!validation.code) return '';

    const { type, value } = validation.code;

    switch (type) {
      case 'percentage':
        return `${value}% off`;
      case 'fixed_amount':
        return `${formatAmount(parseFloat(value))} off`;
      case 'free_access':
        return 'Free access';
      default:
        return 'Discount applied';
    }
  };

  if (validation && validation.isValid) {
    return (
      <Card className="p-4 border-green-200 bg-green-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2 text-green-700">
              <CheckCircle className="h-5 w-5" />
              <span className="font-medium">Promotional code applied!</span>
            </div>
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <div className="flex items-center space-x-1">
                {getDiscountIcon(validation.code?.type || '')}
                <span>{validation.code?.code}</span>
              </div>
            </Badge>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={removeCode}
            disabled={disabled}
            className="text-green-700 hover:text-green-800"
          >
            Remove
          </Button>
        </div>

        <div className="mt-3 space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Discount:</span>
            <span className="font-medium text-green-700">
              {getDiscountText(validation)}
            </span>
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Original amount:</span>
            <span className="line-through text-gray-500">
              {formatAmount(originalAmount)}
            </span>
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">You save:</span>
            <span className="font-medium text-green-700">
              {formatAmount(validation.savings || 0)}
            </span>
          </div>

          <div className="flex items-center justify-between font-medium border-t pt-2">
            <span>Final amount:</span>
            <span className="text-lg text-green-700">
              {formatAmount(validation.finalAmount || originalAmount)}
            </span>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-4">
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Tag className="h-5 w-5 text-gray-500" />
          <span className="font-medium">Have a promotional code?</span>
        </div>

        <div className="flex space-x-2">
          <div className="flex-1">
            <Input
              placeholder="Enter promotional code"
              value={code}
              onChange={(e) => setCode(e.target.value.toUpperCase())}
              disabled={disabled || isValidating}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  validateCode();
                }
              }}
            />
          </div>
          <Button
            onClick={validateCode}
            disabled={disabled || isValidating || !code.trim()}
            variant="outline"
          >
            {isValidating ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              'Apply'
            )}
          </Button>
        </div>

        {error && (
          <div className="flex items-center space-x-2 text-red-600 text-sm">
            <XCircle className="h-4 w-4" />
            <span>{error}</span>
          </div>
        )}

        <div className="text-xs text-gray-500">
          Enter your promotional code to get a discount on this feature.
        </div>
      </div>
    </Card>
  );
}
