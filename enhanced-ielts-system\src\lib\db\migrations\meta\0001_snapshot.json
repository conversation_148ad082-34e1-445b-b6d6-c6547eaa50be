{"id": "72b2ef90-bd12-49ef-9e48-52e42b643d62", "prevId": "742ff19b-196b-43ee-a687-bf47e06e9ecb", "version": "7", "dialect": "postgresql", "tables": {"public.access_permissions": {"name": "access_permissions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "candidate_id": {"name": "candidate_id", "type": "text", "primaryKey": false, "notNull": true}, "result_id": {"name": "result_id", "type": "text", "primaryKey": false, "notNull": false}, "feature_type": {"name": "feature_type", "type": "text", "primaryKey": false, "notNull": true}, "access_type": {"name": "access_type", "type": "text", "primaryKey": false, "notNull": true}, "granted_by": {"name": "granted_by", "type": "text", "primaryKey": false, "notNull": false}, "granted_at": {"name": "granted_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}}, "indexes": {"access_candidate_idx": {"name": "access_candidate_idx", "columns": [{"expression": "candidate_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "access_result_idx": {"name": "access_result_idx", "columns": [{"expression": "result_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "access_feature_idx": {"name": "access_feature_idx", "columns": [{"expression": "feature_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "access_expiry_idx": {"name": "access_expiry_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"access_permissions_candidate_id_candidates_id_fk": {"name": "access_permissions_candidate_id_candidates_id_fk", "tableFrom": "access_permissions", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "access_permissions_result_id_test_results_id_fk": {"name": "access_permissions_result_id_test_results_id_fk", "tableFrom": "access_permissions", "tableTo": "test_results", "columnsFrom": ["result_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "access_permissions_granted_by_users_id_fk": {"name": "access_permissions_granted_by_users_id_fk", "tableFrom": "access_permissions", "tableTo": "users", "columnsFrom": ["granted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ai_feedback": {"name": "ai_feedback", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "test_result_id": {"name": "test_result_id", "type": "text", "primaryKey": false, "notNull": true}, "listening_feedback": {"name": "listening_feedback", "type": "text", "primaryKey": false, "notNull": false}, "reading_feedback": {"name": "reading_feedback", "type": "text", "primaryKey": false, "notNull": false}, "writing_feedback": {"name": "writing_feedback", "type": "text", "primaryKey": false, "notNull": false}, "speaking_feedback": {"name": "speaking_feedback", "type": "text", "primaryKey": false, "notNull": false}, "overall_feedback": {"name": "overall_feedback", "type": "text", "primaryKey": false, "notNull": false}, "study_recommendations": {"name": "study_recommendations", "type": "text", "primaryKey": false, "notNull": false}, "strengths": {"name": "strengths", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "weaknesses": {"name": "weaknesses", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "study_plan": {"name": "study_plan", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "generated_at": {"name": "generated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"ai_feedback_result_idx": {"name": "ai_feedback_result_idx", "columns": [{"expression": "test_result_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ai_feedback_test_result_id_test_results_id_fk": {"name": "ai_feedback_test_result_id_test_results_id_fk", "tableFrom": "ai_feedback", "tableTo": "test_results", "columnsFrom": ["test_result_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"ai_feedback_test_result_id_unique": {"name": "ai_feedback_test_result_id_unique", "nullsNotDistinct": false, "columns": ["test_result_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.candidates": {"name": "candidates", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "text", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "timestamp", "primaryKey": false, "notNull": false}, "nationality": {"name": "nationality", "type": "text", "primaryKey": false, "notNull": false}, "passport_number": {"name": "passport_number", "type": "text", "primaryKey": false, "notNull": true}, "photo_data": {"name": "photo_data", "type": "text", "primaryKey": false, "notNull": false}, "student_status": {"name": "student_status", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "total_tests": {"name": "total_tests", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"candidate_passport_idx": {"name": "candidate_passport_idx", "columns": [{"expression": "passport_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "candidate_org_idx": {"name": "candidate_org_idx", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "candidate_name_idx": {"name": "candidate_name_idx", "columns": [{"expression": "full_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"candidates_organization_id_organizations_id_fk": {"name": "candidates_organization_id_organizations_id_fk", "tableFrom": "candidates", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_passport_per_org": {"name": "unique_passport_per_org", "nullsNotDistinct": false, "columns": ["organization_id", "passport_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.certificate_lifecycle": {"name": "certificate_lifecycle", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "result_id": {"name": "result_id", "type": "text", "primaryKey": false, "notNull": true}, "serial_number": {"name": "serial_number", "type": "text", "primaryKey": false, "notNull": true}, "generated_at": {"name": "generated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "deletion_scheduled_at": {"name": "deletion_scheduled_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"cert_result_idx": {"name": "cert_result_idx", "columns": [{"expression": "result_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cert_serial_idx": {"name": "cert_serial_idx", "columns": [{"expression": "serial_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cert_status_idx": {"name": "cert_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "cert_expiry_idx": {"name": "cert_expiry_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"certificate_lifecycle_result_id_test_results_id_fk": {"name": "certificate_lifecycle_result_id_test_results_id_fk", "tableFrom": "certificate_lifecycle", "tableTo": "test_results", "columnsFrom": ["result_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"certificate_lifecycle_result_id_unique": {"name": "certificate_lifecycle_result_id_unique", "nullsNotDistinct": false, "columns": ["result_id"]}, "certificate_lifecycle_serial_number_unique": {"name": "certificate_lifecycle_serial_number_unique", "nullsNotDistinct": false, "columns": ["serial_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organizations": {"name": "organizations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "settings": {"name": "settings", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "features": {"name": "features", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "billing_plan": {"name": "billing_plan", "type": "text", "primaryKey": false, "notNull": false, "default": "'basic'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"org_slug_idx": {"name": "org_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "org_status_idx": {"name": "org_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organizations_slug_unique": {"name": "organizations_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_transactions": {"name": "payment_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "candidate_id": {"name": "candidate_id", "type": "text", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'UZS'"}, "gateway": {"name": "gateway", "type": "text", "primaryKey": false, "notNull": true}, "gateway_transaction_id": {"name": "gateway_transaction_id", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "feature_type": {"name": "feature_type", "type": "text", "primaryKey": false, "notNull": true}, "result_id": {"name": "result_id", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"payment_candidate_idx": {"name": "payment_candidate_idx", "columns": [{"expression": "candidate_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payment_status_idx": {"name": "payment_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payment_gateway_idx": {"name": "payment_gateway_idx", "columns": [{"expression": "gateway", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payment_feature_idx": {"name": "payment_feature_idx", "columns": [{"expression": "feature_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payment_transactions_candidate_id_candidates_id_fk": {"name": "payment_transactions_candidate_id_candidates_id_fk", "tableFrom": "payment_transactions", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payment_transactions_organization_id_organizations_id_fk": {"name": "payment_transactions_organization_id_organizations_id_fk", "tableFrom": "payment_transactions", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payment_transactions_result_id_test_results_id_fk": {"name": "payment_transactions_result_id_test_results_id_fk", "tableFrom": "payment_transactions", "tableTo": "test_results", "columnsFrom": ["result_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.promotional_code_usage": {"name": "promotional_code_usage", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "code_id": {"name": "code_id", "type": "text", "primaryKey": false, "notNull": true}, "candidate_id": {"name": "candidate_id", "type": "text", "primaryKey": false, "notNull": true}, "result_id": {"name": "result_id", "type": "text", "primaryKey": false, "notNull": false}, "feature_type": {"name": "feature_type", "type": "text", "primaryKey": false, "notNull": true}, "original_amount": {"name": "original_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "discount_amount": {"name": "discount_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "final_amount": {"name": "final_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "payment_transaction_id": {"name": "payment_transaction_id", "type": "text", "primaryKey": false, "notNull": false}, "used_at": {"name": "used_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {"promo_usage_code_idx": {"name": "promo_usage_code_idx", "columns": [{"expression": "code_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "promo_usage_candidate_idx": {"name": "promo_usage_candidate_idx", "columns": [{"expression": "candidate_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "promo_usage_date_idx": {"name": "promo_usage_date_idx", "columns": [{"expression": "used_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"promotional_code_usage_code_id_promotional_codes_id_fk": {"name": "promotional_code_usage_code_id_promotional_codes_id_fk", "tableFrom": "promotional_code_usage", "tableTo": "promotional_codes", "columnsFrom": ["code_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "promotional_code_usage_candidate_id_candidates_id_fk": {"name": "promotional_code_usage_candidate_id_candidates_id_fk", "tableFrom": "promotional_code_usage", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "promotional_code_usage_result_id_test_results_id_fk": {"name": "promotional_code_usage_result_id_test_results_id_fk", "tableFrom": "promotional_code_usage", "tableTo": "test_results", "columnsFrom": ["result_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "promotional_code_usage_payment_transaction_id_payment_transactions_id_fk": {"name": "promotional_code_usage_payment_transaction_id_payment_transactions_id_fk", "tableFrom": "promotional_code_usage", "tableTo": "payment_transactions", "columnsFrom": ["payment_transaction_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.promotional_codes": {"name": "promotional_codes", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "feature_type": {"name": "feature_type", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "valid_from": {"name": "valid_from", "type": "timestamp", "primaryKey": false, "notNull": true}, "valid_until": {"name": "valid_until", "type": "timestamp", "primaryKey": false, "notNull": true}, "usage_limit": {"name": "usage_limit", "type": "integer", "primaryKey": false, "notNull": false}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "min_purchase_amount": {"name": "min_purchase_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "max_discount_amount": {"name": "max_discount_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"promo_code_idx": {"name": "promo_code_idx", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "promo_code_status_idx": {"name": "promo_code_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "promo_code_validity_idx": {"name": "promo_code_validity_idx", "columns": [{"expression": "valid_from", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "valid_until", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "promo_code_public_idx": {"name": "promo_code_public_idx", "columns": [{"expression": "is_public", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"promotional_codes_organization_id_organizations_id_fk": {"name": "promotional_codes_organization_id_organizations_id_fk", "tableFrom": "promotional_codes", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "promotional_codes_created_by_users_id_fk": {"name": "promotional_codes_created_by_users_id_fk", "tableFrom": "promotional_codes", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"org_code_unique": {"name": "org_code_unique", "nullsNotDistinct": false, "columns": ["organization_id", "code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.promotional_rules": {"name": "promotional_rules", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "feature_type": {"name": "feature_type", "type": "text", "primaryKey": false, "notNull": true}, "criteria": {"name": "criteria", "type": "json", "primaryKey": false, "notNull": true}, "benefits": {"name": "benefits", "type": "json", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "valid_from": {"name": "valid_from", "type": "timestamp", "primaryKey": false, "notNull": true}, "valid_until": {"name": "valid_until", "type": "timestamp", "primaryKey": false, "notNull": false}, "usage_limit": {"name": "usage_limit", "type": "integer", "primaryKey": false, "notNull": false}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"promo_org_idx": {"name": "promo_org_idx", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "promo_status_idx": {"name": "promo_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "promo_type_idx": {"name": "promo_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "promo_validity_idx": {"name": "promo_validity_idx", "columns": [{"expression": "valid_from", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "valid_until", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"promotional_rules_organization_id_organizations_id_fk": {"name": "promotional_rules_organization_id_organizations_id_fk", "tableFrom": "promotional_rules", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.referral_conversions": {"name": "referral_conversions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "referral_link_id": {"name": "referral_link_id", "type": "text", "primaryKey": false, "notNull": true}, "referred_candidate_id": {"name": "referred_candidate_id", "type": "text", "primaryKey": false, "notNull": true}, "payment_transaction_id": {"name": "payment_transaction_id", "type": "text", "primaryKey": false, "notNull": false}, "commission_amount": {"name": "commission_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "commission_rate": {"name": "commission_rate", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "converted_at": {"name": "converted_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "paid_at": {"name": "paid_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {"conversion_referral_idx": {"name": "conversion_referral_idx", "columns": [{"expression": "referral_link_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "conversion_candidate_idx": {"name": "conversion_candidate_idx", "columns": [{"expression": "referred_candidate_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "conversion_status_idx": {"name": "conversion_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "conversion_date_idx": {"name": "conversion_date_idx", "columns": [{"expression": "converted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"referral_conversions_referral_link_id_referral_links_id_fk": {"name": "referral_conversions_referral_link_id_referral_links_id_fk", "tableFrom": "referral_conversions", "tableTo": "referral_links", "columnsFrom": ["referral_link_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "referral_conversions_referred_candidate_id_candidates_id_fk": {"name": "referral_conversions_referred_candidate_id_candidates_id_fk", "tableFrom": "referral_conversions", "tableTo": "candidates", "columnsFrom": ["referred_candidate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "referral_conversions_payment_transaction_id_payment_transactions_id_fk": {"name": "referral_conversions_payment_transaction_id_payment_transactions_id_fk", "tableFrom": "referral_conversions", "tableTo": "payment_transactions", "columnsFrom": ["payment_transaction_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.referral_links": {"name": "referral_links", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": true}, "candidate_id": {"name": "candidate_id", "type": "text", "primaryKey": false, "notNull": true}, "referral_code": {"name": "referral_code", "type": "text", "primaryKey": false, "notNull": true}, "click_count": {"name": "click_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "conversion_count": {"name": "conversion_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "total_earnings": {"name": "total_earnings", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"referral_org_idx": {"name": "referral_org_idx", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "referral_candidate_idx": {"name": "referral_candidate_idx", "columns": [{"expression": "candidate_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "referral_code_idx": {"name": "referral_code_idx", "columns": [{"expression": "referral_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "referral_status_idx": {"name": "referral_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"referral_links_organization_id_organizations_id_fk": {"name": "referral_links_organization_id_organizations_id_fk", "tableFrom": "referral_links", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "referral_links_candidate_id_candidates_id_fk": {"name": "referral_links_candidate_id_candidates_id_fk", "tableFrom": "referral_links", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"referral_links_referral_code_unique": {"name": "referral_links_referral_code_unique", "nullsNotDistinct": false, "columns": ["referral_code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.test_registrations": {"name": "test_registrations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "candidate_id": {"name": "candidate_id", "type": "text", "primaryKey": false, "notNull": true}, "candidate_number": {"name": "candidate_number", "type": "text", "primaryKey": false, "notNull": true}, "test_date": {"name": "test_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "test_center": {"name": "test_center", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'registered'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"test_reg_candidate_idx": {"name": "test_reg_candidate_idx", "columns": [{"expression": "candidate_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "test_reg_date_idx": {"name": "test_reg_date_idx", "columns": [{"expression": "test_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "test_reg_status_idx": {"name": "test_reg_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"test_registrations_candidate_id_candidates_id_fk": {"name": "test_registrations_candidate_id_candidates_id_fk", "tableFrom": "test_registrations", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.test_results": {"name": "test_results", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "test_registration_id": {"name": "test_registration_id", "type": "text", "primaryKey": false, "notNull": true}, "listening_score": {"name": "listening_score", "type": "integer", "primaryKey": false, "notNull": false}, "listening_band_score": {"name": "listening_band_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "reading_score": {"name": "reading_score", "type": "integer", "primaryKey": false, "notNull": false}, "reading_band_score": {"name": "reading_band_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "writing_task1_score": {"name": "writing_task1_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "writing_task2_score": {"name": "writing_task2_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "writing_band_score": {"name": "writing_band_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "speaking_fluency_score": {"name": "speaking_fluency_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "speaking_lexical_score": {"name": "speaking_lexical_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "speaking_grammar_score": {"name": "speaking_grammar_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "speaking_pronunciation_score": {"name": "speaking_pronunciation_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "speaking_band_score": {"name": "speaking_band_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "overall_band_score": {"name": "overall_band_score", "type": "numeric(2, 1)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "entered_by": {"name": "entered_by", "type": "text", "primaryKey": false, "notNull": false}, "verified_by": {"name": "verified_by", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"test_result_reg_idx": {"name": "test_result_reg_idx", "columns": [{"expression": "test_registration_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "test_result_status_idx": {"name": "test_result_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "test_result_overall_idx": {"name": "test_result_overall_idx", "columns": [{"expression": "overall_band_score", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"test_results_test_registration_id_test_registrations_id_fk": {"name": "test_results_test_registration_id_test_registrations_id_fk", "tableFrom": "test_results", "tableTo": "test_registrations", "columnsFrom": ["test_registration_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "test_results_entered_by_users_id_fk": {"name": "test_results_entered_by_users_id_fk", "tableFrom": "test_results", "tableTo": "users", "columnsFrom": ["entered_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "test_results_verified_by_users_id_fk": {"name": "test_results_verified_by_users_id_fk", "tableFrom": "test_results", "tableTo": "users", "columnsFrom": ["verified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "master_admin": {"name": "master_admin", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_email_idx": {"name": "user_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_org_idx": {"name": "user_org_idx", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_role_idx": {"name": "user_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"users_organization_id_organizations_id_fk": {"name": "users_organization_id_organizations_id_fk", "tableFrom": "users", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}