import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { candidates, testRegistrations, paymentTransactions } from '@/lib/db/schema';
import { eq, count, and, gte } from 'drizzle-orm';
import { StatsCard } from '@/components/specialized/stats-card';
import { RecentActivity } from '@/components/specialized/recent-activity';
import { Users, FileText, CreditCard, TrendingUp } from 'lucide-react';

export default async function AdminDashboardPage() {
  const session = await auth();

  if (!session?.user?.organizationId) {
    return <div>Access denied</div>;
  }

  // Get statistics for the organization
  const [
    candidateCount,
    testCount,
    completedTestCount,
    recentPayments,
  ] = await Promise.all([
    // Total candidates
    db
      .select({ count: count() })
      .from(candidates)
      .where(eq(candidates.organizationId, session.user.organizationId)),

    // Total tests
    db
      .select({ count: count() })
      .from(testRegistrations)
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(eq(candidates.organizationId, session.user.organizationId)),

    // Completed tests this month
    db
      .select({ count: count() })
      .from(testRegistrations)
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(
        and(
          eq(candidates.organizationId, session.user.organizationId),
          eq(testRegistrations.status, 'completed'),
          gte(testRegistrations.testDate, new Date(new Date().getFullYear(), new Date().getMonth(), 1))
        )
      ),

    // Recent payments
    db
      .select({ count: count() })
      .from(paymentTransactions)
      .where(
        and(
          eq(paymentTransactions.organizationId, session.user.organizationId),
          eq(paymentTransactions.status, 'completed'),
          gte(paymentTransactions.createdAt, new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
        )
      ),
  ]);

  const stats = [
    {
      title: 'Total Candidates',
      value: candidateCount[0]?.count || 0,
      icon: Users,
      description: 'Registered candidates',
      trend: '+12% from last month',
    },
    {
      title: 'Total Tests',
      value: testCount[0]?.count || 0,
      icon: FileText,
      description: 'All test registrations',
      trend: '+8% from last month',
    },
    {
      title: 'Tests This Month',
      value: completedTestCount[0]?.count || 0,
      icon: TrendingUp,
      description: 'Completed this month',
      trend: '+15% from last month',
    },
    {
      title: 'Recent Payments',
      value: recentPayments[0]?.count || 0,
      icon: CreditCard,
      description: 'Last 30 days',
      trend: '+23% from last month',
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome back! Here&apos;s what&apos;s happening with your IELTS center.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatsCard key={index} {...stat} />
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Test Results</h2>
          <RecentActivity organizationId={session.user.organizationId} type="results" />
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Registrations</h2>
          <RecentActivity organizationId={session.user.organizationId} type="registrations" />
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <a
            href="/admin/candidates"
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Users className="h-8 w-8 text-blue-600 mb-2" />
            <h3 className="font-medium text-gray-900">Add New Candidate</h3>
            <p className="text-sm text-gray-600">Register a new test candidate</p>
          </a>

          <a
            href="/admin/results"
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <FileText className="h-8 w-8 text-green-600 mb-2" />
            <h3 className="font-medium text-gray-900">View Results</h3>
            <p className="text-sm text-gray-600">Check recent test results</p>
          </a>

          <a
            href="/admin/payments"
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <CreditCard className="h-8 w-8 text-purple-600 mb-2" />
            <h3 className="font-medium text-gray-900">Payment Reports</h3>
            <p className="text-sm text-gray-600">View payment analytics</p>
          </a>
        </div>
      </div>
    </div>
  );
}
