import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { candidates } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';

const validateSearchSchema = z.object({
  passport: z.string().min(5),
  dateOfBirth: z.string().min(1),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { passport, dateOfBirth } = validateSearchSchema.parse(body);

    // Check if candidate exists with the provided credentials
    const candidate = await db
      .select({
        id: candidates.id,
        fullName: candidates.fullName,
      })
      .from(candidates)
      .where(
        and(
          eq(candidates.passportNumber, passport.trim()),
          eq(candidates.dateOfBirth, new Date(dateOfBirth))
        )
      )
      .limit(1);

    if (candidate.length === 0) {
      return NextResponse.json(
        { 
          message: 'No candidate found with the provided passport/birth certificate number and date of birth. Please check your details and try again.' 
        },
        { status: 404 }
      );
    }

    // Return success without revealing sensitive information
    return NextResponse.json({
      message: 'Candidate found',
      candidateExists: true,
    });
  } catch (error) {
    console.error('Search validation error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid search parameters' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: 'Search validation failed. Please try again.' },
      { status: 500 }
    );
  }
}
