'use client';

import { Badge } from '@/components/ui/badge';
import { 
  Calendar, 
  MapPin, 
  FileText, 
  TrendingUp,
  Info,
  CheckCircle,
  Clock
} from 'lucide-react';
import { getBandScoreDescription, getPerformanceLevel } from '@/lib/utils/ielts-scoring';

interface Candidate {
  id: string;
  fullName: string;
  passportNumber: string;
}

interface TestData {
  registration: {
    id: string;
    candidateNumber: string;
    testDate: Date;
    testCenter: string;
    status: string;
  };
  result: {
    id: string;
    listeningScore: number | null;
    listeningBandScore: string | null;
    readingScore: number | null;
    readingBandScore: string | null;
    writingTask1Score: string | null;
    writingTask2Score: string | null;
    writingBandScore: string | null;
    speakingFluencyScore: string | null;
    speakingLexicalScore: string | null;
    speakingGrammarScore: string | null;
    speakingPronunciationScore: string | null;
    speakingBandScore: string | null;
    overallBandScore: string | null;
    status: string;
    createdAt: Date;
  } | null;
}

interface ResultsTabProps {
  candidate: Candidate;
  testData: TestData[];
}

export function ResultsTab({ candidate, testData }: ResultsTabProps) {
  const completedTests = testData.filter(test => test.result && test.result.status === 'completed');

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      registered: { color: 'blue', label: 'Registered', icon: Calendar },
      completed: { color: 'green', label: 'Completed', icon: CheckCircle },
      cancelled: { color: 'red', label: 'Cancelled', icon: Clock },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { 
      color: 'gray', 
      label: status, 
      icon: Clock 
    };
    
    const IconComponent = config.icon;
    
    return (
      <Badge variant={config.color as any} className="flex items-center">
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const renderScoreCard = (title: string, bandScore: string | null, rawScore?: number | null, maxScore?: number) => {
    const score = bandScore ? parseFloat(bandScore) : 0;
    const performance = getPerformanceLevel(score);
    
    return (
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-medium text-gray-900">{title}</h4>
          <div className="text-right">
            <div className="text-2xl font-bold text-blue-600">
              {bandScore || 'N/A'}
            </div>
            {rawScore !== null && maxScore && (
              <div className="text-xs text-gray-500">
                {rawScore}/{maxScore}
              </div>
            )}
          </div>
        </div>
        {bandScore && (
          <div className="space-y-2">
            <Badge variant={performance.color as any} className="text-xs">
              {performance.level}
            </Badge>
            <p className="text-xs text-gray-600">
              {getBandScoreDescription(score)}
            </p>
          </div>
        )}
      </div>
    );
  };

  if (completedTests.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
          <FileText className="h-8 w-8 text-blue-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Completed Tests</h3>
        <p className="text-gray-600 mb-6">
          You don't have any completed test results yet.
        </p>
        
        {/* Show pending registrations */}
        {testData.length > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
            <h4 className="font-medium text-blue-900 mb-2">Upcoming Tests</h4>
            <div className="space-y-2">
              {testData.map((test) => (
                <div key={test.registration.id} className="flex items-center justify-between text-sm">
                  <span>{formatDate(test.registration.testDate)}</span>
                  {getStatusBadge(test.registration.status)}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Test Results</h2>
        <Badge variant="blue">
          {completedTests.length} Completed Test{completedTests.length > 1 ? 's' : ''}
        </Badge>
      </div>

      {completedTests.map((test, index) => (
        <div key={test.registration.id} className="border border-gray-200 rounded-lg p-6">
          {/* Test Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                IELTS Test #{test.registration.candidateNumber}
              </h3>
              <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  {formatDate(test.registration.testDate)}
                </div>
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-1" />
                  {test.registration.testCenter}
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-blue-600 mb-1">
                {test.result?.overallBandScore || 'N/A'}
              </div>
              <div className="text-sm text-gray-600">Overall Band Score</div>
              {getStatusBadge(test.registration.status)}
            </div>
          </div>

          {/* Score Breakdown */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {renderScoreCard(
              'Listening',
              test.result?.listeningBandScore,
              test.result?.listeningScore,
              40
            )}
            {renderScoreCard(
              'Reading',
              test.result?.readingBandScore,
              test.result?.readingScore,
              40
            )}
            {renderScoreCard(
              'Writing',
              test.result?.writingBandScore
            )}
            {renderScoreCard(
              'Speaking',
              test.result?.speakingBandScore
            )}
          </div>

          {/* Detailed Scores */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
              <Info className="h-4 w-4 mr-2" />
              Detailed Breakdown
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Writing Details */}
              <div>
                <h5 className="font-medium text-gray-800 mb-2">Writing Tasks</h5>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Task 1:</span>
                    <span className="font-medium">{test.result?.writingTask1Score || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Task 2:</span>
                    <span className="font-medium">{test.result?.writingTask2Score || 'N/A'}</span>
                  </div>
                </div>
              </div>

              {/* Speaking Details */}
              <div>
                <h5 className="font-medium text-gray-800 mb-2">Speaking Criteria</h5>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Fluency & Coherence:</span>
                    <span className="font-medium">{test.result?.speakingFluencyScore || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Lexical Resource:</span>
                    <span className="font-medium">{test.result?.speakingLexicalScore || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Grammar:</span>
                    <span className="font-medium">{test.result?.speakingGrammarScore || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Pronunciation:</span>
                    <span className="font-medium">{test.result?.speakingPronunciationScore || 'N/A'}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Performance Summary */}
          {test.result?.overallBandScore && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start">
                <TrendingUp className="h-5 w-5 text-blue-600 mr-3 mt-0.5" />
                <div>
                  <h5 className="font-medium text-blue-900 mb-1">Performance Summary</h5>
                  <p className="text-sm text-blue-800">
                    {getBandScoreDescription(parseFloat(test.result.overallBandScore))}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      ))}

      {/* IELTS Band Score Guide */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">IELTS Band Score Guide</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium text-gray-800">Band 9-8</h4>
            <p className="text-sm text-gray-600">Expert to Very Good User</p>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium text-gray-800">Band 7-6</h4>
            <p className="text-sm text-gray-600">Good to Competent User</p>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium text-gray-800">Band 5-4</h4>
            <p className="text-sm text-gray-600">Modest to Limited User</p>
          </div>
        </div>
      </div>
    </div>
  );
}
