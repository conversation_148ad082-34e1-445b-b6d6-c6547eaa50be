/**
 * Payme Payment Gateway Integration
 * Official Payme API for Uzbekistan
 */

import { z } from 'zod';

// Payme API Configuration
const PAYME_CONFIG = {
  baseUrl: process.env.PAYME_BASE_URL || 'https://checkout.paycom.uz',
  merchantId: process.env.PAYME_MERCHANT_ID || '',
  secretKey: process.env.PAYME_SECRET_KEY || '',
  testMode: process.env.NODE_ENV !== 'production',
};

// Payme API Types
export interface PaymePaymentRequest {
  amount: number; // Amount in tiyin (1 UZS = 100 tiyin)
  account: {
    order_id: string;
    user_id?: string;
  };
  return_url: string;
  description?: string;
}

export interface PaymeTransaction {
  id: string;
  time: number;
  amount: number;
  account: {
    order_id: string;
    user_id?: string;
  };
  create_time: number;
  perform_time?: number;
  cancel_time?: number;
  state: number;
  reason?: number;
}

export interface PaymeWebhookPayload {
  method: string;
  params: {
    id?: string;
    time?: number;
    amount?: number;
    account?: {
      order_id: string;
      user_id?: string;
    };
    reason?: number;
  };
}

// Validation schemas
const paymePaymentRequestSchema = z.object({
  amount: z.number().positive(),
  account: z.object({
    order_id: z.string().min(1),
    user_id: z.string().optional(),
  }),
  return_url: z.string().url(),
  description: z.string().optional(),
});

const paymeWebhookSchema = z.object({
  method: z.string(),
  params: z.object({
    id: z.string().optional(),
    time: z.number().optional(),
    amount: z.number().optional(),
    account: z.object({
      order_id: z.string(),
      user_id: z.string().optional(),
    }).optional(),
    reason: z.number().optional(),
  }),
});

/**
 * Generate Base64 encoded credentials for Payme API
 */
function generatePaymeAuth(): string {
  const credentials = `Paycom:${PAYME_CONFIG.secretKey}`;
  return Buffer.from(credentials).toString('base64');
}

/**
 * Create Payme payment URL
 */
export async function createPaymePayment(request: PaymePaymentRequest): Promise<string> {
  // Validate request
  const validatedRequest = paymePaymentRequestSchema.parse(request);
  
  // Convert amount to tiyin (Payme uses tiyin, 1 UZS = 100 tiyin)
  const amountInTiyin = Math.round(validatedRequest.amount * 100);
  
  // Encode account data
  const accountData = Buffer.from(JSON.stringify(validatedRequest.account)).toString('base64');
  
  // Generate payment URL
  const params = new URLSearchParams({
    'm': PAYME_CONFIG.merchantId,
    'ac.order_id': validatedRequest.account.order_id,
    'a': amountInTiyin.toString(),
    'c': accountData,
    'cr': validatedRequest.return_url,
  });

  if (validatedRequest.account.user_id) {
    params.append('ac.user_id', validatedRequest.account.user_id);
  }

  if (validatedRequest.description) {
    params.append('d', validatedRequest.description);
  }

  return `${PAYME_CONFIG.baseUrl}/?${params.toString()}`;
}

/**
 * Process Payme webhook (JSON-RPC 2.0)
 */
export async function processPaymeWebhook(payload: any): Promise<{
  success: boolean;
  result?: any;
  error?: {
    code: number;
    message: string;
    data?: any;
  };
}> {
  try {
    // Validate payload
    const validatedPayload = paymeWebhookSchema.parse(payload);
    
    // Process based on method
    switch (validatedPayload.method) {
      case 'CheckPerformTransaction':
        return await handlePaymeCheckPerform(validatedPayload.params);
      case 'CreateTransaction':
        return await handlePaymeCreateTransaction(validatedPayload.params);
      case 'PerformTransaction':
        return await handlePaymePerformTransaction(validatedPayload.params);
      case 'CancelTransaction':
        return await handlePaymeCancelTransaction(validatedPayload.params);
      case 'CheckTransaction':
        return await handlePaymeCheckTransaction(validatedPayload.params);
      case 'GetStatement':
        return await handlePaymeGetStatement(validatedPayload.params);
      default:
        return {
          success: false,
          error: {
            code: -32601,
            message: 'Method not found',
          },
        };
    }
  } catch (error) {
    console.error('Payme webhook processing error:', error);
    return {
      success: false,
      error: {
        code: -32700,
        message: 'Parse error',
      },
    };
  }
}

/**
 * Handle CheckPerformTransaction method
 */
async function handlePaymeCheckPerform(params: any): Promise<{
  success: boolean;
  result?: any;
  error?: any;
}> {
  try {
    const { account, amount } = params;
    
    // Validate order exists and amount is correct
    // This should check against your database
    
    return {
      success: true,
      result: {
        allow: true,
      },
    };
  } catch (error) {
    return {
      success: false,
      error: {
        code: -31001,
        message: 'Order not found',
      },
    };
  }
}

/**
 * Handle CreateTransaction method
 */
async function handlePaymeCreateTransaction(params: any): Promise<{
  success: boolean;
  result?: any;
  error?: any;
}> {
  try {
    const { id, time, amount, account } = params;
    
    // Create transaction in database
    // Store Payme transaction ID and details
    
    return {
      success: true,
      result: {
        create_time: time,
        transaction: id,
        state: 1, // Created
      },
    };
  } catch (error) {
    return {
      success: false,
      error: {
        code: -31001,
        message: 'Transaction creation failed',
      },
    };
  }
}

/**
 * Handle PerformTransaction method
 */
async function handlePaymePerformTransaction(params: any): Promise<{
  success: boolean;
  result?: any;
  error?: any;
}> {
  try {
    const { id } = params;
    
    // Mark transaction as performed
    // Grant access to premium features
    const performTime = Date.now();
    
    return {
      success: true,
      result: {
        perform_time: performTime,
        transaction: id,
        state: 2, // Performed
      },
    };
  } catch (error) {
    return {
      success: false,
      error: {
        code: -31001,
        message: 'Transaction not found',
      },
    };
  }
}

/**
 * Handle CancelTransaction method
 */
async function handlePaymeCancelTransaction(params: any): Promise<{
  success: boolean;
  result?: any;
  error?: any;
}> {
  try {
    const { id, reason } = params;
    
    // Cancel transaction in database
    // Revoke access if already granted
    const cancelTime = Date.now();
    
    return {
      success: true,
      result: {
        cancel_time: cancelTime,
        transaction: id,
        state: reason === 1 ? -1 : -2, // Cancelled by timeout or other reason
      },
    };
  } catch (error) {
    return {
      success: false,
      error: {
        code: -31001,
        message: 'Transaction not found',
      },
    };
  }
}

/**
 * Handle CheckTransaction method
 */
async function handlePaymeCheckTransaction(params: any): Promise<{
  success: boolean;
  result?: any;
  error?: any;
}> {
  try {
    const { id } = params;
    
    // Get transaction from database
    // Return transaction details
    
    return {
      success: true,
      result: {
        create_time: Date.now(),
        perform_time: 0,
        cancel_time: 0,
        transaction: id,
        state: 1,
        reason: null,
      },
    };
  } catch (error) {
    return {
      success: false,
      error: {
        code: -31001,
        message: 'Transaction not found',
      },
    };
  }
}

/**
 * Handle GetStatement method
 */
async function handlePaymeGetStatement(params: any): Promise<{
  success: boolean;
  result?: any;
  error?: any;
}> {
  try {
    const { from, to } = params;
    
    // Get transactions from database for the specified period
    // Return list of transactions
    
    return {
      success: true,
      result: {
        transactions: [],
      },
    };
  } catch (error) {
    return {
      success: false,
      error: {
        code: -32400,
        message: 'Invalid request',
      },
    };
  }
}

/**
 * Get Payme transaction status
 */
export async function getPaymeTransactionStatus(transactionId: string): Promise<{
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  amount?: number;
  error?: string;
}> {
  try {
    // This would typically make an API call to Payme to check status
    // For now, return a placeholder implementation
    
    return {
      status: 'pending',
    };
  } catch (error) {
    console.error('Payme status check error:', error);
    return {
      status: 'failed',
      error: 'Status check failed',
    };
  }
}
