import { pgTable, text, timestamp, integer, decimal, boolean, json, unique, index } from 'drizzle-orm/pg-core';
import { createId } from '@paralleldrive/cuid2';
import { relations } from 'drizzle-orm';

// Organizations table - Master level management
export const organizations = pgTable('organizations', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  name: text('name').notNull(),
  slug: text('slug').unique().notNull(),
  settings: json('settings').$type<{
    timezone?: string;
    currency?: string;
    language?: string;
    features?: string[];
  }>().default({}),
  features: json('features').$type<string[]>().default([]),
  billingPlan: text('billing_plan', { enum: ['basic', 'premium', 'enterprise'] }).default('basic'),
  status: text('status', { enum: ['active', 'suspended', 'disabled'] }).default('active'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  slugIdx: index('org_slug_idx').on(table.slug),
  statusIdx: index('org_status_idx').on(table.status),
}));

// Users table - Multi-level authentication
export const users = pgTable('users', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  organizationId: text('organization_id').references(() => organizations.id, { onDelete: 'cascade' }),
  email: text('email').unique().notNull(),
  password: text('password').notNull(),
  name: text('name').notNull(),
  role: text('role', { enum: ['admin', 'checker'] }).notNull(),
  masterAdmin: boolean('master_admin').default(false),
  status: text('status', { enum: ['active', 'inactive', 'suspended'] }).default('active'),
  lastLoginAt: timestamp('last_login_at'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  emailIdx: index('user_email_idx').on(table.email),
  orgIdx: index('user_org_idx').on(table.organizationId),
  roleIdx: index('user_role_idx').on(table.role),
}));

// Candidates table - Single profile per person
export const candidates = pgTable('candidates', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  organizationId: text('organization_id').references(() => organizations.id, { onDelete: 'cascade' }).notNull(),
  fullName: text('full_name').notNull(),
  email: text('email'),
  phoneNumber: text('phone_number'),
  dateOfBirth: timestamp('date_of_birth'),
  nationality: text('nationality'),
  passportNumber: text('passport_number').notNull(),
  photoData: text('photo_data'), // Base64 or URL to photo
  studentStatus: boolean('student_status').default(false),
  totalTests: integer('total_tests').default(0),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  passportIdx: index('candidate_passport_idx').on(table.passportNumber),
  orgIdx: index('candidate_org_idx').on(table.organizationId),
  nameIdx: index('candidate_name_idx').on(table.fullName),
  uniquePassport: unique('unique_passport_per_org').on(table.organizationId, table.passportNumber),
}));

// Test Registrations table - Multiple tests per candidate
export const testRegistrations = pgTable('test_registrations', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  candidateId: text('candidate_id').references(() => candidates.id, { onDelete: 'cascade' }).notNull(),
  candidateNumber: text('candidate_number').notNull(),
  testDate: timestamp('test_date').notNull(),
  testCenter: text('test_center').notNull(),
  status: text('status', { enum: ['registered', 'completed', 'cancelled'] }).default('registered'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  candidateIdx: index('test_reg_candidate_idx').on(table.candidateId),
  dateIdx: index('test_reg_date_idx').on(table.testDate),
  statusIdx: index('test_reg_status_idx').on(table.status),
}));

// Test Results table - Comprehensive IELTS scoring
export const testResults = pgTable('test_results', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  testRegistrationId: text('test_registration_id').references(() => testRegistrations.id, { onDelete: 'cascade' }).notNull(),

  // Listening scores
  listeningScore: integer('listening_score'), // Raw score 0-40
  listeningBandScore: decimal('listening_band_score', { precision: 2, scale: 1 }), // Band score 0-9

  // Reading scores
  readingScore: integer('reading_score'), // Raw score 0-40
  readingBandScore: decimal('reading_band_score', { precision: 2, scale: 1 }), // Band score 0-9

  // Writing scores
  writingTask1Score: decimal('writing_task1_score', { precision: 2, scale: 1 }), // Band score 0-9
  writingTask2Score: decimal('writing_task2_score', { precision: 2, scale: 1 }), // Band score 0-9
  writingBandScore: decimal('writing_band_score', { precision: 2, scale: 1 }), // Overall writing band

  // Speaking scores
  speakingFluencyScore: decimal('speaking_fluency_score', { precision: 2, scale: 1 }),
  speakingLexicalScore: decimal('speaking_lexical_score', { precision: 2, scale: 1 }),
  speakingGrammarScore: decimal('speaking_grammar_score', { precision: 2, scale: 1 }),
  speakingPronunciationScore: decimal('speaking_pronunciation_score', { precision: 2, scale: 1 }),
  speakingBandScore: decimal('speaking_band_score', { precision: 2, scale: 1 }),

  // Overall score
  overallBandScore: decimal('overall_band_score', { precision: 2, scale: 1 }),

  // Metadata
  status: text('status', { enum: ['draft', 'completed', 'verified'] }).default('draft'),
  enteredBy: text('entered_by').references(() => users.id),
  verifiedBy: text('verified_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  testRegIdx: index('test_result_reg_idx').on(table.testRegistrationId),
  statusIdx: index('test_result_status_idx').on(table.status),
  overallScoreIdx: index('test_result_overall_idx').on(table.overallBandScore),
}));

// Payment Transactions table - Payment tracking
export const paymentTransactions = pgTable('payment_transactions', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  candidateId: text('candidate_id').references(() => candidates.id, { onDelete: 'cascade' }).notNull(),
  organizationId: text('organization_id').references(() => organizations.id, { onDelete: 'cascade' }).notNull(),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  currency: text('currency').default('UZS').notNull(),
  gateway: text('gateway', { enum: ['click', 'payme', 'manual'] }).notNull(),
  gatewayTransactionId: text('gateway_transaction_id'),
  status: text('status', { enum: ['pending', 'completed', 'failed', 'cancelled', 'refunded'] }).default('pending'),
  featureType: text('feature_type', { enum: ['feedback', 'certificate', 'progress'] }).notNull(),
  resultId: text('result_id').references(() => testResults.id),
  metadata: json('metadata').$type<{
    gatewayResponse?: any;
    failureReason?: string;
    refundReason?: string;
  }>().default({}),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  completedAt: timestamp('completed_at'),
}, (table) => ({
  candidateIdx: index('payment_candidate_idx').on(table.candidateId),
  statusIdx: index('payment_status_idx').on(table.status),
  gatewayIdx: index('payment_gateway_idx').on(table.gateway),
  featureIdx: index('payment_feature_idx').on(table.featureType),
}));

// Access Permissions table - Premium feature access
export const accessPermissions = pgTable('access_permissions', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  candidateId: text('candidate_id').references(() => candidates.id, { onDelete: 'cascade' }).notNull(),
  resultId: text('result_id').references(() => testResults.id, { onDelete: 'cascade' }),
  featureType: text('feature_type', { enum: ['feedback', 'certificate', 'progress'] }).notNull(),
  accessType: text('access_type', { enum: ['paid', 'promotional', 'manual'] }).notNull(),
  grantedBy: text('granted_by').references(() => users.id),
  grantedAt: timestamp('granted_at').defaultNow().notNull(),
  expiresAt: timestamp('expires_at'),
  metadata: json('metadata').$type<{
    paymentId?: string;
    promotionId?: string;
    reason?: string;
  }>().default({}),
}, (table) => ({
  candidateIdx: index('access_candidate_idx').on(table.candidateId),
  resultIdx: index('access_result_idx').on(table.resultId),
  featureIdx: index('access_feature_idx').on(table.featureType),
  expiryIdx: index('access_expiry_idx').on(table.expiresAt),
}));

// Promotional Rules table - Flexible promotion system
export const promotionalRules = pgTable('promotional_rules', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  organizationId: text('organization_id').references(() => organizations.id, { onDelete: 'cascade' }).notNull(),
  name: text('name').notNull(),
  type: text('type', { enum: ['student_discount', 'loyalty_reward', 'time_based', 'custom'] }).notNull(),
  featureType: text('feature_type', { enum: ['feedback', 'certificate', 'progress', 'all'] }).notNull(),
  criteria: json('criteria').$type<{
    studentStatus?: boolean;
    minTests?: number;
    testDateRange?: { start: string; end: string };
    customConditions?: any;
  }>().notNull(),
  benefits: json('benefits').$type<{
    discountPercent?: number;
    freeAccess?: boolean;
    validityDays?: number;
  }>().notNull(),
  status: text('status', { enum: ['active', 'inactive', 'expired'] }).default('active'),
  validFrom: timestamp('valid_from').notNull(),
  validUntil: timestamp('valid_until'),
  usageLimit: integer('usage_limit'),
  usageCount: integer('usage_count').default(0),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  orgIdx: index('promo_org_idx').on(table.organizationId),
  statusIdx: index('promo_status_idx').on(table.status),
  typeIdx: index('promo_type_idx').on(table.type),
  validityIdx: index('promo_validity_idx').on(table.validFrom, table.validUntil),
}));

// Promotional Codes table - Specific discount codes
export const promotionalCodes = pgTable('promotional_codes', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  organizationId: text('organization_id').references(() => organizations.id, { onDelete: 'cascade' }).notNull(),
  code: text('code').notNull(),
  name: text('name').notNull(),
  description: text('description'),
  type: text('type', { enum: ['percentage', 'fixed_amount', 'free_access'] }).notNull(),
  value: decimal('value', { precision: 10, scale: 2 }).notNull(), // Percentage or fixed amount
  featureType: text('feature_type', { enum: ['feedback', 'certificate', 'progress', 'all'] }).notNull(),
  status: text('status', { enum: ['active', 'inactive', 'expired', 'used_up'] }).default('active'),
  validFrom: timestamp('valid_from').notNull(),
  validUntil: timestamp('valid_until').notNull(),
  usageLimit: integer('usage_limit'), // null = unlimited
  usageCount: integer('usage_count').default(0),
  minPurchaseAmount: decimal('min_purchase_amount', { precision: 10, scale: 2 }),
  maxDiscountAmount: decimal('max_discount_amount', { precision: 10, scale: 2 }),
  isPublic: boolean('is_public').default(false), // Can be shared publicly
  createdBy: text('created_by').references(() => users.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  orgCodeIdx: unique('org_code_unique').on(table.organizationId, table.code),
  codeIdx: index('promo_code_idx').on(table.code),
  statusIdx: index('promo_code_status_idx').on(table.status),
  validityIdx: index('promo_code_validity_idx').on(table.validFrom, table.validUntil),
  publicIdx: index('promo_code_public_idx').on(table.isPublic),
}));

// Promotional Code Usage table - Track code usage
export const promotionalCodeUsage = pgTable('promotional_code_usage', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  codeId: text('code_id').references(() => promotionalCodes.id, { onDelete: 'cascade' }).notNull(),
  candidateId: text('candidate_id').references(() => candidates.id, { onDelete: 'cascade' }).notNull(),
  resultId: text('result_id').references(() => testResults.id, { onDelete: 'cascade' }),
  featureType: text('feature_type', { enum: ['feedback', 'certificate', 'progress'] }).notNull(),
  originalAmount: decimal('original_amount', { precision: 10, scale: 2 }).notNull(),
  discountAmount: decimal('discount_amount', { precision: 10, scale: 2 }).notNull(),
  finalAmount: decimal('final_amount', { precision: 10, scale: 2 }).notNull(),
  paymentTransactionId: text('payment_transaction_id').references(() => paymentTransactions.id),
  usedAt: timestamp('used_at').defaultNow().notNull(),
  metadata: json('metadata').$type<{
    userAgent?: string;
    ipAddress?: string;
    referralSource?: string;
  }>(),
}, (table) => ({
  codeIdx: index('promo_usage_code_idx').on(table.codeId),
  candidateIdx: index('promo_usage_candidate_idx').on(table.candidateId),
  dateIdx: index('promo_usage_date_idx').on(table.usedAt),
}));

// Referral System table - Track referral links and conversions
export const referralLinks = pgTable('referral_links', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  organizationId: text('organization_id').references(() => organizations.id, { onDelete: 'cascade' }).notNull(),
  candidateId: text('candidate_id').references(() => candidates.id, { onDelete: 'cascade' }).notNull(),
  referralCode: text('referral_code').unique().notNull(),
  clickCount: integer('click_count').default(0),
  conversionCount: integer('conversion_count').default(0),
  totalEarnings: decimal('total_earnings', { precision: 10, scale: 2 }).default('0'),
  status: text('status', { enum: ['active', 'inactive', 'suspended'] }).default('active'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  orgIdx: index('referral_org_idx').on(table.organizationId),
  candidateIdx: index('referral_candidate_idx').on(table.candidateId),
  codeIdx: index('referral_code_idx').on(table.referralCode),
  statusIdx: index('referral_status_idx').on(table.status),
}));

// Referral Conversions table - Track successful referrals
export const referralConversions = pgTable('referral_conversions', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  referralLinkId: text('referral_link_id').references(() => referralLinks.id, { onDelete: 'cascade' }).notNull(),
  referredCandidateId: text('referred_candidate_id').references(() => candidates.id, { onDelete: 'cascade' }).notNull(),
  paymentTransactionId: text('payment_transaction_id').references(() => paymentTransactions.id),
  commissionAmount: decimal('commission_amount', { precision: 10, scale: 2 }).notNull(),
  commissionRate: decimal('commission_rate', { precision: 5, scale: 2 }).notNull(), // Percentage
  status: text('status', { enum: ['pending', 'confirmed', 'paid', 'cancelled'] }).default('pending'),
  convertedAt: timestamp('converted_at').defaultNow().notNull(),
  paidAt: timestamp('paid_at'),
  metadata: json('metadata').$type<{
    featureType?: string;
    originalAmount?: number;
    notes?: string;
  }>(),
}, (table) => ({
  referralIdx: index('conversion_referral_idx').on(table.referralLinkId),
  candidateIdx: index('conversion_candidate_idx').on(table.referredCandidateId),
  statusIdx: index('conversion_status_idx').on(table.status),
  dateIdx: index('conversion_date_idx').on(table.convertedAt),
}));

// AI Feedback table - Generated feedback storage
export const aiFeedback = pgTable('ai_feedback', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  testResultId: text('test_result_id').references(() => testResults.id, { onDelete: 'cascade' }).notNull().unique(),
  listeningFeedback: text('listening_feedback'),
  readingFeedback: text('reading_feedback'),
  writingFeedback: text('writing_feedback'),
  speakingFeedback: text('speaking_feedback'),
  overallFeedback: text('overall_feedback'),
  studyRecommendations: text('study_recommendations'),
  strengths: json('strengths').$type<string[]>().default([]),
  weaknesses: json('weaknesses').$type<string[]>().default([]),
  studyPlan: json('study_plan').$type<{
    shortTerm?: string[];
    longTerm?: string[];
    resources?: string[];
  }>().default({}),
  generatedAt: timestamp('generated_at').defaultNow().notNull(),
}, (table) => ({
  resultIdx: index('ai_feedback_result_idx').on(table.testResultId),
}));

// Certificate Lifecycle table - Certificate management
export const certificateLifecycle = pgTable('certificate_lifecycle', {
  id: text('id').primaryKey().$defaultFn(() => createId()),
  resultId: text('result_id').references(() => testResults.id, { onDelete: 'cascade' }).notNull().unique(),
  serialNumber: text('serial_number').unique().notNull(),
  generatedAt: timestamp('generated_at').defaultNow().notNull(),
  expiresAt: timestamp('expires_at').notNull(), // 6 months from test date
  status: text('status', { enum: ['active', 'expired', 'deleted'] }).default('active'),
  deletionScheduledAt: timestamp('deletion_scheduled_at'),
  metadata: json('metadata').$type<{
    downloadCount?: number;
    lastDownloadAt?: string;
    fileSize?: number;
  }>().default({}),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  resultIdx: index('cert_result_idx').on(table.resultId),
  serialIdx: index('cert_serial_idx').on(table.serialNumber),
  statusIdx: index('cert_status_idx').on(table.status),
  expiryIdx: index('cert_expiry_idx').on(table.expiresAt),
}));

// Relations
export const organizationsRelations = relations(organizations, ({ many }) => ({
  users: many(users),
  candidates: many(candidates),
  paymentTransactions: many(paymentTransactions),
  promotionalRules: many(promotionalRules),
}));

export const usersRelations = relations(users, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [users.organizationId],
    references: [organizations.id],
  }),
  enteredResults: many(testResults, { relationName: 'enteredBy' }),
  verifiedResults: many(testResults, { relationName: 'verifiedBy' }),
  grantedPermissions: many(accessPermissions),
}));

export const candidatesRelations = relations(candidates, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [candidates.organizationId],
    references: [organizations.id],
  }),
  testRegistrations: many(testRegistrations),
  paymentTransactions: many(paymentTransactions),
  accessPermissions: many(accessPermissions),
}));

export const testRegistrationsRelations = relations(testRegistrations, ({ one, many }) => ({
  candidate: one(candidates, {
    fields: [testRegistrations.candidateId],
    references: [candidates.id],
  }),
  testResults: many(testResults),
}));

export const testResultsRelations = relations(testResults, ({ one }) => ({
  testRegistration: one(testRegistrations, {
    fields: [testResults.testRegistrationId],
    references: [testRegistrations.id],
  }),
  enteredByUser: one(users, {
    fields: [testResults.enteredBy],
    references: [users.id],
    relationName: 'enteredBy',
  }),
  verifiedByUser: one(users, {
    fields: [testResults.verifiedBy],
    references: [users.id],
    relationName: 'verifiedBy',
  }),
  aiFeedback: one(aiFeedback, {
    fields: [testResults.id],
    references: [aiFeedback.testResultId],
  }),
  certificate: one(certificateLifecycle, {
    fields: [testResults.id],
    references: [certificateLifecycle.resultId],
  }),
}));

export const paymentTransactionsRelations = relations(paymentTransactions, ({ one }) => ({
  candidate: one(candidates, {
    fields: [paymentTransactions.candidateId],
    references: [candidates.id],
  }),
  organization: one(organizations, {
    fields: [paymentTransactions.organizationId],
    references: [organizations.id],
  }),
  result: one(testResults, {
    fields: [paymentTransactions.resultId],
    references: [testResults.id],
  }),
}));

export const accessPermissionsRelations = relations(accessPermissions, ({ one }) => ({
  candidate: one(candidates, {
    fields: [accessPermissions.candidateId],
    references: [candidates.id],
  }),
  result: one(testResults, {
    fields: [accessPermissions.resultId],
    references: [testResults.id],
  }),
  grantedByUser: one(users, {
    fields: [accessPermissions.grantedBy],
    references: [users.id],
  }),
}));

export const promotionalRulesRelations = relations(promotionalRules, ({ one }) => ({
  organization: one(organizations, {
    fields: [promotionalRules.organizationId],
    references: [organizations.id],
  }),
}));

export const aiFeedbackRelations = relations(aiFeedback, ({ one }) => ({
  testResult: one(testResults, {
    fields: [aiFeedback.testResultId],
    references: [testResults.id],
  }),
}));

export const certificateLifecycleRelations = relations(certificateLifecycle, ({ one }) => ({
  result: one(testResults, {
    fields: [certificateLifecycle.resultId],
    references: [testResults.id],
  }),
}));
