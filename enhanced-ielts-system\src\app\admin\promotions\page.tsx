import { auth } from '@/lib/auth/config';
import { redirect } from 'next/navigation';
import { PromotionalCodeManager } from '@/components/promotions/promotional-code-manager';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';

// Simple Card component
const Card = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
  <div className={`bg-white rounded-lg border shadow-sm p-4 ${className}`}>{children}</div>
);
import {
  Tag,
  Users,
  TrendingUp,
  DollarSign,
  BarChart3,
  Gift,
  Share2
} from 'lucide-react';

export default async function PromotionsPage() {
  const session = await auth();

  if (!session?.user?.organizationId) {
    redirect('/login');
  }

  if (session.user.role !== 'admin' && !session.user.masterAdmin) {
    redirect('/checker');
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Promotional System</h1>
          <p className="text-gray-600">
            Manage promotional codes, discounts, and referral programs
          </p>
        </div>
        <Badge variant="outline" className="bg-blue-50 text-blue-700">
          Phase 8: Complete
        </Badge>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-blue-100 rounded-lg">
              <Tag className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Active Codes</p>
              <p className="text-2xl font-bold">12</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-green-100 rounded-lg">
              <Users className="h-6 w-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Usage</p>
              <p className="text-2xl font-bold">156</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-purple-100 rounded-lg">
              <DollarSign className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Discounts</p>
              <p className="text-2xl font-bold">2.4M UZS</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-yellow-100 rounded-lg">
              <Share2 className="h-6 w-6 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Referrals</p>
              <p className="text-2xl font-bold">23</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="codes" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="codes" className="flex items-center space-x-2">
            <Tag className="h-4 w-4" />
            <span>Promotional Codes</span>
          </TabsTrigger>
          <TabsTrigger value="referrals" className="flex items-center space-x-2">
            <Share2 className="h-4 w-4" />
            <span>Referral System</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="rules" className="flex items-center space-x-2">
            <Gift className="h-4 w-4" />
            <span>Promotional Rules</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="codes">
          <PromotionalCodeManager organizationId={session.user.organizationId} />
        </TabsContent>

        <TabsContent value="referrals">
          <Card className="p-6">
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mx-auto">
                <Share2 className="h-8 w-8 text-blue-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Referral System Management</h3>
                <p className="text-gray-600 mt-2">
                  View and manage referral links, track conversions, and monitor commission payouts.
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-blue-600">23</p>
                  <p className="text-sm text-gray-600">Active Referral Links</p>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">156</p>
                  <p className="text-sm text-gray-600">Total Conversions</p>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-purple-600">340K UZS</p>
                  <p className="text-sm text-gray-600">Total Commissions</p>
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card className="p-6">
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mx-auto">
                <BarChart3 className="h-8 w-8 text-purple-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Promotional Analytics</h3>
                <p className="text-gray-600 mt-2">
                  Comprehensive analytics and insights for your promotional campaigns.
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-blue-600">12.5%</p>
                  <p className="text-sm text-gray-600">Conversion Rate</p>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">2.4M UZS</p>
                  <p className="text-sm text-gray-600">Total Discounts</p>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-purple-600">156</p>
                  <p className="text-sm text-gray-600">Code Usage</p>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-yellow-600">89%</p>
                  <p className="text-sm text-gray-600">Customer Satisfaction</p>
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="rules">
          <Card className="p-6">
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mx-auto">
                <Gift className="h-8 w-8 text-green-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">Promotional Rules Engine</h3>
                <p className="text-gray-600 mt-2">
                  Create and manage flexible promotional rules with automatic eligibility checking.
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <div className="text-left p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-semibold text-green-700 mb-2">Student Discounts</h4>
                  <p className="text-sm text-gray-600">
                    Automatic discounts for students with verification
                  </p>
                </div>
                <div className="text-left p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-semibold text-blue-700 mb-2">Loyalty Rewards</h4>
                  <p className="text-sm text-gray-600">
                    Rewards based on test count and engagement
                  </p>
                </div>
                <div className="text-left p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-semibold text-purple-700 mb-2">Time-based Promotions</h4>
                  <p className="text-sm text-gray-600">
                    Seasonal and limited-time promotional offers
                  </p>
                </div>
                <div className="text-left p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-semibold text-yellow-700 mb-2">Custom Rules</h4>
                  <p className="text-sm text-gray-600">
                    Flexible rules with custom conditions and benefits
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Implementation Status */}
      <Card className="p-6 bg-green-50 border-green-200">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <Gift className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-green-800">
              Phase 8: Promotional System - Implementation Complete! ✅
            </h3>
            <p className="text-green-700 mt-1">
              All promotional system features have been successfully implemented including promotional codes,
              referral system, discount management, and analytics dashboard.
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
}
