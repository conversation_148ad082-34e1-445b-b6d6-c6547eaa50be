'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Modal } from '@/components/ui/modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Building2,
  Upload,
  Copy,
  CheckCircle,
  AlertCircle,
  Loader2,
  CreditCard,
  FileText
} from 'lucide-react';
import { FeatureType } from '@/lib/payments/types';
import { formatAmount } from '@/lib/utils/format';

const manualPaymentSchema = z.object({
  paymentMethod: z.enum(['bank_transfer', 'cash']),
  referenceNumber: z.string().min(1, 'Reference number is required'),
  paymentDate: z.string().min(1, 'Payment date is required'),
  notes: z.string().optional(),
});

type ManualPaymentForm = z.infer<typeof manualPaymentSchema>;

interface ManualPaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  candidateId: string;
  featureType: FeatureType;
  resultId?: string;
  amount: number;
  currency: string;
  onSubmit: () => void;
}

export function ManualPaymentModal({
  isOpen,
  onClose,
  candidateId,
  featureType,
  resultId,
  amount,
  currency,
  onSubmit,
}: ManualPaymentModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [copied, setCopied] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<ManualPaymentForm>({
    resolver: zodResolver(manualPaymentSchema),
  });

  const paymentMethod = watch('paymentMethod');

  const bankDetails = {
    bankName: 'National Bank of Uzbekistan',
    accountNumber: '20208000600000000001',
    accountName: 'TLD System LLC',
    swift: 'NBFAUZ2X',
    purpose: `Payment for ${featureType} feature - ${candidateId}`,
  };

  const handleCopyBankDetails = () => {
    const details = `
Bank: ${bankDetails.bankName}
Account: ${bankDetails.accountNumber}
Account Name: ${bankDetails.accountName}
SWIFT: ${bankDetails.swift}
Amount: ${formatAmount(amount, currency)}
Purpose: ${bankDetails.purpose}
    `.trim();

    navigator.clipboard.writeText(details);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const onFormSubmit = async (data: ManualPaymentForm) => {
    setIsSubmitting(true);
    try {
      // Submit manual payment request
      const response = await fetch('/api/payments/manual', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          candidateId,
          featureType,
          resultId,
          amount,
          currency,
          ...data,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to submit payment');
      }

      reset();
      onSubmit();
    } catch (error) {
      console.error('Manual payment submission error:', error);
      alert('Failed to submit payment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="large">
      <div className="p-6">
        <div className="flex items-center mb-6">
          <Building2 className="h-6 w-6 text-blue-600 mr-2" />
          <h2 className="text-xl font-semibold text-gray-900">Manual Payment</h2>
        </div>

        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
          {/* Payment Amount */}
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-blue-900">Payment Amount</div>
                <div className="text-sm text-blue-700">One-time payment</div>
              </div>
              <div className="text-2xl font-bold text-blue-900">
                {formatAmount(amount, currency)}
              </div>
            </div>
          </div>

          {/* Payment Method Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Payment Method
            </label>
            <div className="grid grid-cols-2 gap-4">
              <label className="relative">
                <input
                  type="radio"
                  value="bank_transfer"
                  {...register('paymentMethod')}
                  className="sr-only"
                />
                <div className={`
                  border-2 rounded-lg p-4 cursor-pointer transition-all
                  ${paymentMethod === 'bank_transfer'
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                  }
                `}>
                  <div className="flex items-center">
                    <CreditCard className="h-5 w-5 text-gray-600 mr-2" />
                    <span className="font-medium">Bank Transfer</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Transfer to our bank account
                  </p>
                </div>
              </label>

              <label className="relative">
                <input
                  type="radio"
                  value="cash"
                  {...register('paymentMethod')}
                  className="sr-only"
                />
                <div className={`
                  border-2 rounded-lg p-4 cursor-pointer transition-all
                  ${paymentMethod === 'cash'
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                  }
                `}>
                  <div className="flex items-center">
                    <Building2 className="h-5 w-5 text-gray-600 mr-2" />
                    <span className="font-medium">Cash Payment</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Pay at our office
                  </p>
                </div>
              </label>
            </div>
            {errors.paymentMethod && (
              <p className="text-red-500 text-sm mt-1">{errors.paymentMethod.message}</p>
            )}
          </div>

          {/* Bank Transfer Details */}
          {paymentMethod === 'bank_transfer' && (
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium text-gray-900">Bank Transfer Details</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleCopyBankDetails}
                >
                  {copied ? (
                    <>
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Copied
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4 mr-1" />
                      Copy Details
                    </>
                  )}
                </Button>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Bank Name:</span>
                  <span className="font-medium">{bankDetails.bankName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Account Number:</span>
                  <span className="font-medium font-mono">{bankDetails.accountNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Account Name:</span>
                  <span className="font-medium">{bankDetails.accountName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">SWIFT Code:</span>
                  <span className="font-medium font-mono">{bankDetails.swift}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount:</span>
                  <span className="font-bold text-blue-600">{formatAmount(amount, currency)}</span>
                </div>
                <div className="pt-2 border-t">
                  <span className="text-gray-600">Purpose:</span>
                  <div className="font-medium text-sm mt-1 p-2 bg-white rounded border">
                    {bankDetails.purpose}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Cash Payment Details */}
          {paymentMethod === 'cash' && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-3">Office Address</h3>
              <div className="text-sm text-gray-700">
                <div className="font-medium">TLD System Office</div>
                <div>123 Amir Temur Street</div>
                <div>Tashkent, Uzbekistan 100000</div>
                <div className="mt-2">
                  <span className="font-medium">Office Hours:</span> Mon-Fri 9:00-18:00
                </div>
                <div>
                  <span className="font-medium">Phone:</span> +998 71 123 4567
                </div>
              </div>
            </div>
          )}

          {/* Payment Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Reference Number
              </label>
              <Input
                placeholder="Transaction/Receipt number"
                {...register('referenceNumber')}
                error={errors.referenceNumber?.message}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Payment Date
              </label>
              <Input
                type="date"
                {...register('paymentDate')}
                error={errors.paymentDate?.message}
              />
            </div>
          </div>

          {/* Additional Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Additional Notes (Optional)
            </label>
            <textarea
              {...register('notes')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Any additional information about your payment..."
            />
          </div>

          {/* Important Notice */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 text-yellow-600 mr-3 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <div className="font-medium mb-1">Important Notice</div>
                <div>
                  Manual payments require admin approval and may take 1-3 business days to process.
                  You will receive an email confirmation once your payment is verified and access is granted.
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  Submit Payment
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
