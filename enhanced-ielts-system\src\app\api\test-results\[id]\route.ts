import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { testResults, testRegistrations, candidates } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';
import { validateTestResult } from '@/lib/utils/ielts-scoring';

const updateTestResultSchema = z.object({
  listeningScore: z.number().min(0).max(40).optional(),
  listeningBandScore: z.number().min(0).max(9).optional(),
  readingScore: z.number().min(0).max(40).optional(),
  readingBandScore: z.number().min(0).max(9).optional(),
  writingTask1Score: z.number().min(0).max(9).optional(),
  writingTask2Score: z.number().min(0).max(9).optional(),
  writingBandScore: z.number().min(0).max(9).optional(),
  speakingFluencyScore: z.number().min(0).max(9).optional(),
  speakingLexicalScore: z.number().min(0).max(9).optional(),
  speakingGrammarScore: z.number().min(0).max(9).optional(),
  speakingPronunciationScore: z.number().min(0).max(9).optional(),
  speakingBandScore: z.number().min(0).max(9).optional(),
  overallBandScore: z.number().min(0).max(9).optional(),
  status: z.enum(['draft', 'completed', 'verified']).optional(),
});

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (session.user.role !== 'admin' && session.user.role !== 'checker' && !session.user.masterAdmin) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = updateTestResultSchema.parse(body);

    // Verify test result exists and belongs to the organization
    const existingResult = await db
      .select({
        result: testResults,
        registration: testRegistrations,
        candidate: candidates,
      })
      .from(testResults)
      .leftJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .leftJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(
        and(
          eq(testResults.id, params.id),
          eq(candidates.organizationId, session.user.organizationId)
        )
      )
      .limit(1);

    if (existingResult.length === 0) {
      return NextResponse.json(
        { error: 'Test result not found or does not belong to your organization' },
        { status: 404 }
      );
    }

    // Validate test result data if provided
    if (Object.keys(validatedData).some(key => key.includes('Score'))) {
      const validation = validateTestResult({
        listeningScore: validatedData.listeningScore,
        readingScore: validatedData.readingScore,
        writingTask1Score: validatedData.writingTask1Score,
        writingTask2Score: validatedData.writingTask2Score,
        speakingFluencyScore: validatedData.speakingFluencyScore,
        speakingLexicalScore: validatedData.speakingLexicalScore,
        speakingGrammarScore: validatedData.speakingGrammarScore,
        speakingPronunciationScore: validatedData.speakingPronunciationScore,
      });

      if (!validation.isValid) {
        return NextResponse.json(
          { error: 'Invalid test result data', details: validation.errors },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date(),
    };

    // Convert numeric values to strings for decimal fields
    if (validatedData.listeningScore !== undefined) {
      updateData.listeningScore = validatedData.listeningScore;
    }
    if (validatedData.listeningBandScore !== undefined) {
      updateData.listeningBandScore = validatedData.listeningBandScore.toString();
    }
    if (validatedData.readingScore !== undefined) {
      updateData.readingScore = validatedData.readingScore;
    }
    if (validatedData.readingBandScore !== undefined) {
      updateData.readingBandScore = validatedData.readingBandScore.toString();
    }
    if (validatedData.writingTask1Score !== undefined) {
      updateData.writingTask1Score = validatedData.writingTask1Score.toString();
    }
    if (validatedData.writingTask2Score !== undefined) {
      updateData.writingTask2Score = validatedData.writingTask2Score.toString();
    }
    if (validatedData.writingBandScore !== undefined) {
      updateData.writingBandScore = validatedData.writingBandScore.toString();
    }
    if (validatedData.speakingFluencyScore !== undefined) {
      updateData.speakingFluencyScore = validatedData.speakingFluencyScore.toString();
    }
    if (validatedData.speakingLexicalScore !== undefined) {
      updateData.speakingLexicalScore = validatedData.speakingLexicalScore.toString();
    }
    if (validatedData.speakingGrammarScore !== undefined) {
      updateData.speakingGrammarScore = validatedData.speakingGrammarScore.toString();
    }
    if (validatedData.speakingPronunciationScore !== undefined) {
      updateData.speakingPronunciationScore = validatedData.speakingPronunciationScore.toString();
    }
    if (validatedData.speakingBandScore !== undefined) {
      updateData.speakingBandScore = validatedData.speakingBandScore.toString();
    }
    if (validatedData.overallBandScore !== undefined) {
      updateData.overallBandScore = validatedData.overallBandScore.toString();
    }
    if (validatedData.status !== undefined) {
      updateData.status = validatedData.status;
    }

    // Update test result
    const [updatedResult] = await db
      .update(testResults)
      .set(updateData)
      .where(eq(testResults.id, params.id))
      .returning();

    return NextResponse.json(updatedResult);
  } catch (error) {
    console.error('Error updating test result:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update test result' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get test result with registration and candidate info
    const result = await db
      .select({
        result: testResults,
        registration: testRegistrations,
        candidate: candidates,
      })
      .from(testResults)
      .leftJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .leftJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(
        and(
          eq(testResults.id, params.id),
          eq(candidates.organizationId, session.user.organizationId)
        )
      )
      .limit(1);

    if (result.length === 0) {
      return NextResponse.json(
        { error: 'Test result not found or does not belong to your organization' },
        { status: 404 }
      );
    }

    return NextResponse.json(result[0]);
  } catch (error) {
    console.error('Error fetching test result:', error);
    return NextResponse.json(
      { error: 'Failed to fetch test result' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (session.user.role !== 'admin' && !session.user.masterAdmin) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Verify test result exists and belongs to the organization
    const existingResult = await db
      .select({
        result: testResults,
        registration: testRegistrations,
        candidate: candidates,
      })
      .from(testResults)
      .leftJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .leftJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(
        and(
          eq(testResults.id, params.id),
          eq(candidates.organizationId, session.user.organizationId)
        )
      )
      .limit(1);

    if (existingResult.length === 0) {
      return NextResponse.json(
        { error: 'Test result not found or does not belong to your organization' },
        { status: 404 }
      );
    }

    // Delete test result
    await db.delete(testResults).where(eq(testResults.id, params.id));

    // Update test registration status back to registered
    await db
      .update(testRegistrations)
      .set({
        status: 'registered',
        updatedAt: new Date(),
      })
      .where(eq(testRegistrations.id, existingResult[0].registration.id));

    return NextResponse.json({ message: 'Test result deleted successfully' });
  } catch (error) {
    console.error('Error deleting test result:', error);
    return NextResponse.json(
      { error: 'Failed to delete test result' },
      { status: 500 }
    );
  }
}
