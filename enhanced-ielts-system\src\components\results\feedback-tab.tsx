'use client';

import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Loader2, Brain, Target, TrendingUp, BookOpen } from 'lucide-react';

interface FeedbackTabProps {
  testResultId: string;
  hasAccess: boolean;
  onUnlock: () => void;
}

interface AIFeedback {
  id: string;
  listeningFeedback: string;
  readingFeedback: string;
  writingFeedback: string;
  speakingFeedback: string;
  overallFeedback: string;
  studyRecommendations: string;
  strengths: string[];
  weaknesses: string[];
  studyPlan: any;
  generatedAt: string;
}

export function FeedbackTab({ testResultId, hasAccess, onUnlock }: FeedbackTabProps) {
  const [feedback, setFeedback] = useState<AIFeedback | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (hasAccess) {
      fetchFeedback();
    } else {
      setLoading(false);
    }
  }, [testResultId, hasAccess]);

  const fetchFeedback = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/ai/feedback?testResultId=${testResultId}`);
      
      if (response.status === 404) {
        // Generate feedback if it doesn't exist
        await generateFeedback();
        return;
      }
      
      if (!response.ok) {
        throw new Error('Failed to fetch feedback');
      }
      
      const data = await response.json();
      setFeedback(data);
      
      // Poll for completion if still generating
      if (!data.listeningFeedback || data.overallFeedback === '') {
        setTimeout(fetchFeedback, 3000);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load feedback');
    } finally {
      setLoading(false);
    }
  };

  const generateFeedback = async () => {
    try {
      const response = await fetch('/api/ai/feedback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ testResultId }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate feedback');
      }
      
      const data = await response.json();
      setFeedback(data);
      
      // Poll for completion
      setTimeout(fetchFeedback, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate feedback');
    }
  };

  if (!hasAccess) {
    return (
      <div className="relative">
        <div className="absolute inset-0 bg-white/80 backdrop-blur-sm z-10 flex items-center justify-center">
          <div className="text-center p-8 bg-white rounded-lg shadow-lg border max-w-md">
            <Brain className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">AI-Powered Feedback</h3>
            <p className="text-gray-600 mb-4">
              Get detailed, personalized feedback on your IELTS performance with specific improvement recommendations.
            </p>
            <ul className="text-sm text-gray-500 mb-6 space-y-1">
              <li>• Detailed analysis of each skill area</li>
              <li>• Personalized strengths and weaknesses</li>
              <li>• Specific improvement strategies</li>
              <li>• 4-week custom study plan</li>
            </ul>
            <Button onClick={onUnlock} className="w-full">
              Unlock AI Feedback
            </Button>
          </div>
        </div>
        
        {/* Preview content (blurred) */}
        <div className="space-y-6 blur-sm">
          <div className="p-6 bg-white rounded-lg border">
            <div className="flex items-center gap-2 mb-4">
              <Brain className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold">Overall Performance Analysis</h3>
            </div>
            <p className="text-gray-700">Your overall performance shows strong potential...</p>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">
            {feedback?.overallFeedback === '' 
              ? 'AI is analyzing your performance...' 
              : 'Loading feedback...'}
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={fetchFeedback} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  if (!feedback) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600 mb-4">No feedback available</p>
        <Button onClick={generateFeedback}>
          Generate AI Feedback
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overall Feedback */}
      <div className="p-6 bg-white rounded-lg border">
        <div className="flex items-center gap-2 mb-4">
          <Brain className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold">Overall Performance Analysis</h3>
        </div>
        <p className="text-gray-700 leading-relaxed">{feedback.overallFeedback}</p>
      </div>

      {/* Strengths and Weaknesses */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="p-6 bg-white rounded-lg border">
          <div className="flex items-center gap-2 mb-4">
            <TrendingUp className="h-5 w-5 text-green-600" />
            <h3 className="text-lg font-semibold text-green-700">Strengths</h3>
          </div>
          <div className="space-y-2">
            {feedback.strengths.map((strength, index) => (
              <Badge key={index} variant="secondary" className="bg-green-100 text-green-800">
                {strength}
              </Badge>
            ))}
          </div>
        </div>

        <div className="p-6 bg-white rounded-lg border">
          <div className="flex items-center gap-2 mb-4">
            <Target className="h-5 w-5 text-orange-600" />
            <h3 className="text-lg font-semibold text-orange-700">Areas for Improvement</h3>
          </div>
          <div className="space-y-2">
            {feedback.weaknesses.map((weakness, index) => (
              <Badge key={index} variant="secondary" className="bg-orange-100 text-orange-800">
                {weakness}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/* Skill-specific Feedback */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="p-6 bg-white rounded-lg border">
          <h3 className="text-lg font-semibold mb-3">Listening Feedback</h3>
          <p className="text-gray-700 text-sm leading-relaxed">{feedback.listeningFeedback}</p>
        </div>

        <div className="p-6 bg-white rounded-lg border">
          <h3 className="text-lg font-semibold mb-3">Reading Feedback</h3>
          <p className="text-gray-700 text-sm leading-relaxed">{feedback.readingFeedback}</p>
        </div>

        <div className="p-6 bg-white rounded-lg border">
          <h3 className="text-lg font-semibold mb-3">Writing Feedback</h3>
          <p className="text-gray-700 text-sm leading-relaxed">{feedback.writingFeedback}</p>
        </div>

        <div className="p-6 bg-white rounded-lg border">
          <h3 className="text-lg font-semibold mb-3">Speaking Feedback</h3>
          <p className="text-gray-700 text-sm leading-relaxed">{feedback.speakingFeedback}</p>
        </div>
      </div>

      {/* Study Recommendations */}
      <div className="p-6 bg-white rounded-lg border">
        <div className="flex items-center gap-2 mb-4">
          <BookOpen className="h-5 w-5 text-purple-600" />
          <h3 className="text-lg font-semibold">Study Recommendations</h3>
        </div>
        <p className="text-gray-700 leading-relaxed mb-4">{feedback.studyRecommendations}</p>
        
        {feedback.studyPlan?.plan && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2">4-Week Study Plan</h4>
            <div className="whitespace-pre-line text-sm text-gray-700">
              {feedback.studyPlan.plan}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
