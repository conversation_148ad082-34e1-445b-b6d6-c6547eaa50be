import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { paymentTransactions, candidates } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';
import { updatePaymentStatus } from '@/lib/payments/utils';

const approvePaymentSchema = z.object({
  action: z.enum(['approve', 'reject']),
  notes: z.string().optional(),
});

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can approve/reject manual payments
    if (session.user.role !== 'admin' && !session.user.masterAdmin) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const { action, notes } = approvePaymentSchema.parse(body);

    // Get the manual payment transaction
    const transaction = await db
      .select({
        transaction: paymentTransactions,
        candidate: candidates,
      })
      .from(paymentTransactions)
      .leftJoin(candidates, eq(paymentTransactions.candidateId, candidates.id))
      .where(
        and(
          eq(paymentTransactions.id, params.id),
          eq(paymentTransactions.gateway, 'manual'),
          eq(candidates.organizationId, session.user.organizationId)
        )
      )
      .limit(1);

    if (transaction.length === 0) {
      return NextResponse.json(
        { error: 'Manual payment not found or does not belong to your organization' },
        { status: 404 }
      );
    }

    const tx = transaction[0];

    // Check if transaction is still pending
    if (tx.transaction.status !== 'pending') {
      return NextResponse.json(
        { error: `Transaction is already ${tx.transaction.status}` },
        { status: 400 }
      );
    }

    // Update transaction status
    const newStatus = action === 'approve' ? 'completed' : 'failed';
    const metadata = {
      ...tx.transaction.metadata,
      approvalAction: action,
      approvalNotes: notes,
      approvedBy: session.user.id,
      approvedAt: new Date().toISOString(),
    };

    const success = await updatePaymentStatus(
      params.id,
      newStatus,
      undefined, // No gateway transaction ID for manual payments
      metadata
    );

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to update payment status' },
        { status: 500 }
      );
    }

    // TODO: Send notification to candidate about approval/rejection

    return NextResponse.json({
      transactionId: params.id,
      action,
      status: newStatus,
      message: action === 'approve' 
        ? 'Payment approved successfully. Access has been granted.'
        : 'Payment rejected. Candidate has been notified.',
    });
  } catch (error) {
    console.error('Manual payment approval error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to process approval' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user?.organizationId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can view manual payment details
    if (session.user.role !== 'admin' && !session.user.masterAdmin) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Get the manual payment transaction details
    const transaction = await db
      .select({
        transaction: paymentTransactions,
        candidate: candidates,
      })
      .from(paymentTransactions)
      .leftJoin(candidates, eq(paymentTransactions.candidateId, candidates.id))
      .where(
        and(
          eq(paymentTransactions.id, params.id),
          eq(paymentTransactions.gateway, 'manual'),
          eq(candidates.organizationId, session.user.organizationId)
        )
      )
      .limit(1);

    if (transaction.length === 0) {
      return NextResponse.json(
        { error: 'Manual payment not found or does not belong to your organization' },
        { status: 404 }
      );
    }

    const tx = transaction[0];

    return NextResponse.json({
      id: tx.transaction.id,
      candidate: {
        id: tx.candidate?.id,
        fullName: tx.candidate?.fullName,
        email: tx.candidate?.email,
        passportNumber: tx.candidate?.passportNumber,
      },
      amount: parseFloat(tx.transaction.amount),
      currency: tx.transaction.currency,
      featureType: tx.transaction.featureType,
      resultId: tx.transaction.resultId,
      status: tx.transaction.status,
      metadata: tx.transaction.metadata,
      createdAt: tx.transaction.createdAt,
      completedAt: tx.transaction.completedAt,
    });
  } catch (error) {
    console.error('Manual payment details fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch payment details' },
      { status: 500 }
    );
  }
}
