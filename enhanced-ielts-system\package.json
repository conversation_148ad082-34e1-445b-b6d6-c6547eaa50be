{"name": "enhanced-ielts-system", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:seed": "tsx scripts/seed-data.ts", "db:setup": "tsx scripts/setup-db.ts", "setup:master-admin": "tsx scripts/create-master-admin.ts", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky install"}, "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "@auth/drizzle-adapter": "^1.9.1", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.0.1", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.79.0", "@types/qrcode": "^1.5.5", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.44.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.511.0", "next": "15.3.3", "next-auth": "^5.0.0-beta.28", "postgres": "^3.4.7", "qrcode": "^1.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "zod": "^3.25.41", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/pg": "^8.15.2", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "husky": "^9.1.7", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5"}}