import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/config';
import { db } from '@/lib/db';
import { aiFeedback, testResults, candidates, testRegistrations } from '@/lib/db/schema';
import { generateFeedback } from '@/lib/ai/claude';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  const session = await auth();
  
  if (!session?.user?.organizationId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { testResultId } = await request.json();

  try {
    // Get test result with candidate info
    const testResult = await db
      .select({
        testResult: testResults,
        candidate: candidates,
        testRegistration: testRegistrations,
      })
      .from(testResults)
      .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(eq(testResults.id, testResultId))
      .limit(1);

    if (testResult.length === 0) {
      return NextResponse.json({ error: 'Test result not found' }, { status: 404 });
    }

    const { testResult: result, candidate } = testResult[0];

    // Check if feedback already exists
    const existingFeedback = await db
      .select()
      .from(aiFeedback)
      .where(eq(aiFeedback.testResultId, testResultId))
      .limit(1);

    if (existingFeedback.length > 0) {
      return NextResponse.json(existingFeedback[0]);
    }

    // Create initial feedback record
    const [feedbackRecord] = await db.insert(aiFeedback).values({
      testResultId,
      listeningFeedback: '',
      readingFeedback: '',
      writingFeedback: '',
      speakingFeedback: '',
      overallFeedback: '',
      studyRecommendations: '',
      strengths: [],
      weaknesses: [],
      studyPlan: {},
    }).returning();

    // Generate feedback asynchronously
    generateFeedbackAsync(testResultId, result, candidate);

    return NextResponse.json(feedbackRecord, { status: 201 });
  } catch (error) {
    console.error('Error initiating feedback generation:', error);
    return NextResponse.json({ error: 'Failed to generate feedback' }, { status: 500 });
  }
}

async function generateFeedbackAsync(testResultId: string, result: any, candidate: any) {
  try {
    const feedbackRequest = {
      testResultId,
      candidateName: candidate.fullName,
      listeningScore: result.listeningScore || 0,
      listeningBandScore: parseFloat(result.listeningBandScore) || 0,
      readingScore: result.readingScore || 0,
      readingBandScore: parseFloat(result.readingBandScore) || 0,
      writingTask1Score: parseFloat(result.writingTask1Score) || 0,
      writingTask2Score: parseFloat(result.writingTask2Score) || 0,
      writingBandScore: parseFloat(result.writingBandScore) || 0,
      speakingFluencyScore: parseFloat(result.speakingFluencyScore) || 0,
      speakingLexicalScore: parseFloat(result.speakingLexicalScore) || 0,
      speakingGrammarScore: parseFloat(result.speakingGrammarScore) || 0,
      speakingPronunciationScore: parseFloat(result.speakingPronunciationScore) || 0,
      speakingBandScore: parseFloat(result.speakingBandScore) || 0,
      overallBandScore: parseFloat(result.overallBandScore) || 0,
    };

    const generatedFeedback = await generateFeedback(feedbackRequest);

    // Update feedback record with generated content
    await db
      .update(aiFeedback)
      .set({
        listeningFeedback: generatedFeedback.listeningFeedback,
        readingFeedback: generatedFeedback.readingFeedback,
        writingFeedback: generatedFeedback.writingFeedback,
        speakingFeedback: generatedFeedback.speakingFeedback,
        overallFeedback: generatedFeedback.overallFeedback,
        studyRecommendations: generatedFeedback.studyRecommendations,
        strengths: generatedFeedback.strengths,
        weaknesses: generatedFeedback.weaknesses,
        studyPlan: { plan: generatedFeedback.studyPlan },
      })
      .where(eq(aiFeedback.testResultId, testResultId));

  } catch (error) {
    console.error('Error generating feedback:', error);
    
    // Update with error status
    await db
      .update(aiFeedback)
      .set({
        overallFeedback: 'Failed to generate feedback. Please try again later.',
      })
      .where(eq(aiFeedback.testResultId, testResultId));
  }
}

export async function GET(request: NextRequest) {
  const session = await auth();
  
  if (!session?.user?.organizationId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const testResultId = searchParams.get('testResultId');

  if (!testResultId) {
    return NextResponse.json({ error: 'Test result ID required' }, { status: 400 });
  }

  try {
    const feedback = await db
      .select()
      .from(aiFeedback)
      .where(eq(aiFeedback.testResultId, testResultId))
      .limit(1);

    if (feedback.length === 0) {
      return NextResponse.json({ error: 'Feedback not found' }, { status: 404 });
    }

    return NextResponse.json(feedback[0]);
  } catch (error) {
    console.error('Error fetching feedback:', error);
    return NextResponse.json({ error: 'Failed to fetch feedback' }, { status: 500 });
  }
}
